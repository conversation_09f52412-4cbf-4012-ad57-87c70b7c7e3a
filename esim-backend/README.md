# AuraESIM 后端服务

AuraESIM是一个eSIM管理平台的后端服务，提供eSIM卡管理、用户管理、订单处理等功能。

## 运行方式

### 构建

```bash
go build -o esim-api.bin ./cmd/api
```

### 运行

```bash
./esim-api.bin
```

### 支持的命令行参数

服务支持以下命令行参数：

| 参数 | 描述 | 默认值 |
| --- | --- | --- |
| `--config` | 配置文件路径 | `./config` |
| `--migrate` | 运行数据库迁移并退出 | `false` |
| `--auto-migrate` | 启动服务时自动运行数据库迁移 | `false` |
| `--init-admin` | 创建初始管理员用户并退出 | `false` |

### 配置文件路径

指定配置文件路径：

```bash
./esim-api.bin --config /path/to/config
```

### 数据库迁移

有两种方式运行数据库迁移：

1. 仅执行迁移后退出（适用于初始化或更新数据库结构）：

```bash
./esim-api.bin --migrate
```

1. 服务启动时自动执行迁移（适用于开发环境或需要自动维护数据库结构的场景）：

```bash
./esim-api.bin --auto-migrate
```

### 初始管理员创建

在首次部署时，您可以创建一个初始管理员账户：

```bash
./esim-api.bin --init-admin <EMAIL>
```

这个命令会：
1. 检查数据库中是否已存在用户
2. 如果数据库为空，创建一个具有管理员权限的用户
3. 自动生成一个安全的随机密码并显示在控制台
4. 程序执行完成后自动退出

**安全说明：**
- 只有在数据库中没有任何用户时才能创建初始管理员
- 密码会显示在控制台中，请及时复制并在首次登录后更改
- 该命令执行后程序会退出，不会启动Web服务

## Docker 环境

### 使用Docker运行

```bash
docker run -p 18080:18080 -v /path/to/config:/app/config auraesim/backend:latest
```

### 在Docker中执行数据库迁移

```bash
docker run --rm auraesim/backend:latest /app/esim-api --migrate
```

### 使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 执行数据库迁移
docker-compose exec api /app/esim-api --migrate
```

## 配置文件

配置文件使用YAML格式，包含数据库连接、服务端口、JWT配置等信息。详细配置示例请参考`config/config.example.yaml`。

## 开发文档

文档位于`docs`目录。