version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "18080:18080"
    environment:
      - ESIM_DATABASE_HOST=postgres
      - ESIM_DATABASE_USERNAME=postgres
      - ESIM_DATABASE_PASSWORD=postgres
      - ESIM_DATABASE_DATABASE=esim
      - ESIM_REDIS_HOST=redis
      - ESIM_APP_ENVIRONMENT=development
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    volumes:
      - ./config:/app/config

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=esim
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data: