package migrations

import (
	"gorm.io/gorm"

	"vereal/letsesim/internal/repository/postgres"
)

// Migrate 运行数据库迁移
func Migrate(db *gorm.DB) error {
	// 自动迁移模型
	return db.AutoMigrate(
		// 用户相关模型
		&postgres.UserModel{},

		// eSIM相关模型
		&postgres.ESIMModel{},
		&postgres.ESIMUsageModel{},

		// 订单相关模型
		&postgres.OrderModel{},

		// 积分相关模型
		&postgres.CreditModel{},
		&postgres.TransactionModel{},

		// 促销相关模型
		&postgres.PromotionModel{},

		// 费率相关模型
		&postgres.RateModel{},

		// 代理商相关模型
		&postgres.ResellerModel{},

		// 企业代理商相关模型
		&postgres.DepartmentModel{},
		&postgres.EmployeeModel{},
		&postgres.EmployeeQuotaModel{},
	)
}
