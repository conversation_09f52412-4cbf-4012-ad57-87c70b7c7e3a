# eSIM平台运维指南

本文档为eSIM平台的系统管理员提供全面的运维指南，包括系统部署、配置管理、日常维护等方面的操作说明，特别包含了系统优化相关的配置和最佳实践。

## 目录

- [1. 系统概述](#1-%E7%B3%BB%E7%BB%9F%E6%A6%82%E8%BF%B0)
- [2. 环境要求](#2-%E7%8E%AF%E5%A2%83%E8%A6%81%E6%B1%82)
- [3. Docker镜像构建与部署](#3-docker%E9%95%9C%E5%83%8F%E6%9E%84%E5%BB%BA%E4%B8%8E%E9%83%A8%E7%BD%B2)
- [4. 使用Docker Compose进行生产部署](#4-%E4%BD%BF%E7%94%A8docker-compose%E8%BF%9B%E8%A1%8C%E7%94%9F%E4%BA%A7%E9%83%A8%E7%BD%B2)
- [5. 系统配置管理](#5-%E7%B3%BB%E7%BB%9F%E9%85%8D%E7%BD%AE%E7%AE%A1%E7%90%86)
- [6. 数据库迁移](#6-%E6%95%B0%E6%8D%AE%E5%BA%93%E8%BF%81%E7%A7%BB)
- [7. 日志管理](#7-%E6%97%A5%E5%BF%97%E7%AE%A1%E7%90%86)
- [8. 系统监控与告警](#8-%E7%B3%BB%E7%BB%9F%E7%9B%91%E6%8E%A7%E4%B8%8E%E5%91%8A%E8%AD%A6)
- [9. 缓存策略配置](#9-%E7%BC%93%E5%AD%98%E7%AD%96%E7%95%A5%E9%85%8D%E7%BD%AE)
- [10. 异步任务系统配置](#10-%E5%BC%82%E6%AD%A5%E4%BB%BB%E5%8A%A1%E7%B3%BB%E7%BB%9F%E9%85%8D%E7%BD%AE)
- [11. 数据库优化配置](#11-%E6%95%B0%E6%8D%AE%E5%BA%93%E4%BC%98%E5%8C%96%E9%85%8D%E7%BD%AE)
- [12. API限流保护配置](#12-api%E9%99%90%E6%B5%81%E4%BF%9D%E6%8A%A4%E9%85%8D%E7%BD%AE)
- [13. 备份与恢复](#13-%E5%A4%87%E4%BB%BD%E4%B8%8E%E6%81%A2%E5%A4%8D)
- [14. 故障排除](#14-%E6%95%85%E9%9A%9C%E6%8E%92%E9%99%A4)
- [15. 性能调优](#15-%E6%80%A7%E8%83%BD%E8%B0%83%E4%BC%98)

## 1\. 系统概述

eSIM平台是一个基于Go语言开发的后端服务，使用Echo框架构建RESTful API，PostgreSQL作为数据库，Redis用于缓存和分布式任务处理。系统采用Docker容器化部署，便于在不同环境中一致地运行。

### 1.1 技术栈

- 语言：Go 1.22+
- Web框架：Echo
- 数据库：PostgreSQL
- 缓存：Redis（多级缓存架构）
- 监控：Prometheus + Grafana
- 容器化：Docker & Docker Compose
- 日志：Zap (结构化日志)
- 配置管理：Viper

### 1.2 系统优化特性

平台实现了多项系统优化技术：

1. **系统监控与统计**：基于Prometheus的全面监控系统
2. **多级缓存架构**：结合本地内存缓存和Redis分布式缓存
3. **异步任务处理**：健壮的任务队列和重试机制
4. **数据库查询优化**：查询装饰器、性能跟踪和索引优化
5. **API限流保护**：多层次限流和熔断机制

## 2\. 环境要求

### 2.1 硬件要求

- CPU：最低4核心，推荐8核心或更高（x86架构）
- 内存：最低8GB，推荐16GB或更高
- 磁盘：最低50GB SSD，推荐100GB或更高
- 网络：千兆网络连接

### 2.2 运行环境要求

- 操作系统：Debian 12 x86_64
- Docker Engine 23.0.0+
- Docker Compose 2.3.0+

### 2.3 网络要求

- 系统默认使用18080端口提供HTTP服务
- Redis默认使用6379端口
- PostgreSQL默认使用5432端口
- Prometheus默认使用9090端口
- Grafana默认使用3000端口
- 确保服务器防火墙允许所需端口的访问
- 如果使用反向代理，请确保正确配置代理规则

## 3\. Docker镜像构建与部署

### 3.1 手动构建Docker镜像

在Debian 12上首先安装Docker：

```bash
# 更新系统并安装依赖
apt update && apt upgrade -y
apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 设置Docker仓库
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
apt update
apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启用并启动Docker服务
systemctl enable docker
systemctl start docker

# 验证Docker安装
docker --version
docker compose version
```

然后在项目根目录下执行以下命令构建Docker镜像：

```bash
# 进入项目目录
cd /path/to/backend

# 构建Docker镜像
docker build -t esim-backend:latest .
```

您可以通过添加标签来进行版本管理：

```bash
docker build -t esim-backend:v1.0.0 -t esim-backend:latest .
```

### 3.2 镜像推送到Docker仓库

构建完成后，您可以将镜像推送到Docker镜像仓库（如Docker Hub或私有仓库）：

```bash
# 登录到Docker仓库
docker login [仓库地址]

# 为镜像添加仓库标签
docker tag esim-backend:latest [仓库地址]/esim-backend:latest

# 推送镜像到仓库
docker push [仓库地址]/esim-backend:latest
```

### 3.3 在生产服务器上拉取和运行镜像

在生产服务器上执行以下命令拉取和运行Docker镜像：

```bash
# 拉取镜像
docker pull [仓库地址]/esim-backend:latest

# 创建数据目录
mkdir -p /data/esim/postgres
mkdir -p /data/esim/redis
mkdir -p /data/esim/config
mkdir -p /data/esim/prometheus
mkdir -p /data/esim/grafana

# 运行容器
docker run -d \
  --name esim-api \
  -p 8080:8080 \
  -e ESIM_APP_ENVIRONMENT=production \
  -e ESIM_DATABASE_HOST=your-db-host \
  -e ESIM_DATABASE_USERNAME=your-username \
  -e ESIM_DATABASE_PASSWORD=your-password \
  -e ESIM_DATABASE_DATABASE=esim \
  -e ESIM_REDIS_HOST=your-redis-host \
  -e ESIM_AUTH_JWTSECRET=your-jwt-secret \
  -v /data/esim/config:/app/config \
  [仓库地址]/esim-backend:latest
```

## 4\. 使用Docker Compose进行生产部署

### 4.1 准备Docker Compose配置

为生产环境创建一个专用的配置文件`docker-compose.prod.yml`：

```yaml
version: '3.8'

services:
  api:
    image: [仓库地址]/esim-backend:latest
    container_name: esim-api
    ports:
      - "8080:8080"
    environment:
      - ESIM_APP_ENVIRONMENT=production
      - ESIM_DATABASE_HOST=postgres
      - ESIM_DATABASE_PORT=5432
      - ESIM_DATABASE_USERNAME=${DB_USERNAME}
      - ESIM_DATABASE_PASSWORD=${DB_PASSWORD}
      - ESIM_DATABASE_DATABASE=esim
      - ESIM_DATABASE_SSLMODE=disable
      - ESIM_DATABASE_MAXOPENCONNS=20
      - ESIM_DATABASE_MAXIDLECONNS=10
      - ESIM_DATABASE_CONNMAXLIFETIME=1h
      - ESIM_REDIS_HOST=redis
      - ESIM_REDIS_PORT=6379
      - ESIM_REDIS_PASSWORD=${REDIS_PASSWORD}
      - ESIM_REDIS_DB=0
      - ESIM_REDIS_POOLSIZE=20
      - ESIM_AUTH_JWTSECRET=${JWT_SECRET}
      - ESIM_AUTH_JWTEXPIRATION=720h
      - ESIM_LOG_LEVEL=info
      - ESIM_LOG_FORMAT=json
    depends_on:
      - postgres
      - redis
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - /data/esim/config:/app/config
      - /data/esim/logs:/app/logs
    networks:
      - esim-network
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  postgres:
    image: postgres:15-alpine
    container_name: esim-postgres
    environment:
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=esim
    volumes:
      - /data/esim/postgres:/var/lib/postgresql/data
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - esim-network
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 1G

  redis:
    image: redis:7-alpine
    container_name: esim-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy volatile-lru
    volumes:
      - /data/esim/redis:/data
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 5s
      timeout: 2s
      retries: 10
    networks:
      - esim-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 3G
        reservations:
          cpus: '0.5'
          memory: 1G
  
  prometheus:
    image: prom/prometheus:latest
    container_name: esim-prometheus
    volumes:
      - /data/esim/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - /data/esim/prometheus/data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9090:9090"
    restart: always
    networks:
      - esim-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
  
  grafana:
    image: grafana/grafana:latest
    container_name: esim-grafana
    volumes:
      - /data/esim/grafana:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    restart: always
    networks:
      - esim-network
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  esim-network:
    driver: bridge
```

### 4.2 创建环境变量文件

创建`.env`文件用于存储敏感信息：

```bash
DB_USERNAME=postgres
DB_PASSWORD=strong-password-here
REDIS_PASSWORD=redis-strong-password
JWT_SECRET=your-jwt-secret-key
GRAFANA_PASSWORD=grafana-admin-password
```

确保此文件的权限设置正确，仅允许需要访问的用户读取：

```bash
chmod 600 .env
```

### 4.3 创建Prometheus配置文件

创建`/data/esim/prometheus/prometheus.yml`文件：

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'esim-api'
    scrape_interval: 15s
    metrics_path: /metrics
    static_configs:
      - targets: ['api:8080']

  - job_name: 'prometheus'
    scrape_interval: 15s
    static_configs:
      - targets: ['localhost:9090']
```

### 4.4 启动服务

使用以下命令启动服务：

```bash
# 创建并启动所有服务
docker compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker compose -f docker-compose.prod.yml ps
```

### 4.5 运行数据库迁移

首次部署或更新后，需要运行数据库迁移：

```bash
docker compose -f docker-compose.prod.yml exec api /app/esim-api --migrate
```

### 4.6 停止服务

```bash
docker compose -f docker-compose.prod.yml down
```

如果需要同时删除数据卷（慎用，会删除所有数据）：

```bash
docker compose -f docker-compose.prod.yml down -v
```

## 5\. 系统配置管理

eSIM平台支持多种配置方式，按照优先级从高到低排序：

1. 环境变量
2. 配置文件
3. 默认值

### 5.1 环境变量配置

系统使用`ESIM_`前缀的环境变量进行配置，环境变量的命名格式为`ESIM_SECTION_KEY`，例如：

- `ESIM_APP_ENVIRONMENT`：应用运行环境
- `ESIM_DATABASE_HOST`：数据库主机
- `ESIM_REDIS_PORT`：Redis端口

### 5.2 配置文件配置

系统默认读取`./config`目录下的`config.yaml`文件。以下是一个完整的配置示例，包含所有优化特性的配置：

```yaml
app:
  name: "eSIM Backend"
  environment: "production"
  debug: false

server:
  host: "0.0.0.0"
  port: 18080
  shutdownTimeout: 5s

database:
  driver: "postgres"
  host: "postgres"
  port: 5432
  username: "postgres"
  password: "postgres"
  database: "esim"
  sslMode: "disable"
  maxOpenConns: 20
  maxIdleConns: 10
  connMaxLifetime: 1h

redis:
  host: "redis"
  port: 6379
  password: ""
  db: 0
  poolSize: 20

auth:
  jwtSecret: "your-jwt-secret-key"
  jwtExpiration: 720h

providers:
  esimAccess:
    enabled: true
    baseURL: "https://api.esimaccess.com"
    apiKey: "your-api-key"
    apiSecret: "your-api-secret"
    webhookURL: "https://your-domain.com/webhooks/esimaccess"
  
  mayaMobile:
    enabled: true
    baseURL: "https://api.maya.net/connectivity/v1"
    apiKey: "your-api-key"
    apiSecret: "your-api-secret"
    webhookURL: "https://your-domain.com/webhooks/mayamobile"

log:
  level: "info"
  format: "json"
  output: "stdout"
  timeFormat: "2006-01-02T15:04:05Z07:00"

cache:
  enabled: true
  defaultTTL: "30m"
  localMaxSize: 10000

async:
  workers: 10
  maxRetries: 3
  retryDelays: ["5s", "30s", "2m"]
  taskTTL: "168h"  # 7天

rateLimiter:
  defaultLimit: 60
  roleLimits:
    admin: 300
    reseller: 200
    enterprise: 200
    user: 100
  pathLimits:
    "/api/v1/register": 10
    "/api/v1/login": 20
    "/api/v1/webhook/*": 500
  expiry: "1m"

circuitBreaker:
  threshold: 5
  timeout: "30s"

monitoring:
  enabled: true
  metricsPath: "/metrics"
  healthPath: "/health"
```

### 5.3 指定配置文件路径

您可以通过命令行参数指定配置文件路径：

```bash
./esim-api --config /path/to/config
```

在Docker环境中：

```bash
docker run -v /host/path/to/config:/config esim-backend:latest /app/esim-api --config /config
```

## 6\. 数据库迁移

系统支持自动数据库迁移功能，可以通过`--migrate`参数触发：

```bash
./esim-api --migrate
```

在Docker容器中执行迁移：

```bash
docker exec -it esim-api /app/esim-api --migrate
```

在Docker Compose环境中：

```bash
docker compose exec api /app/esim-api --migrate
```

## 7\. 日志管理

### 7.1 日志配置

系统使用Zap日志库，支持通过配置文件或环境变量配置日志行为：

```yaml
log:
  level: "info"  # debug, info, warn, error, dpanic, panic, fatal
  format: "json" # json或console
  output: "stdout" # stdout、文件路径或"multifile"
  timeFormat: "2006-01-02T15:04:05Z07:00"
  maxSize: 100    # 单个日志文件最大大小(MB)
  maxAge: 7       # 日志文件保留天数
  maxBackups: 10  # 保留的旧日志文件最大数量
  compress: true  # 是否压缩旧日志文件
```

对应的环境变量：

- `ESIM_LOG_LEVEL`
- `ESIM_LOG_FORMAT`
- `ESIM_LOG_OUTPUT`
- `ESIM_LOG_TIMEFORMAT`
- `ESIM_LOG_MAXSIZE`
- `ESIM_LOG_MAXAGE`
- `ESIM_LOG_MAXBACKUPS`
- `ESIM_LOG_COMPRESS`

### 7.2 日志收集

在生产环境中，建议使用以下日志驱动配置，将日志发送到集中系统：

```yaml
services:
  api:
    # ... 其他配置 ...
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

或使用ELK Stack进行更完善的日志收集和分析：

```yaml
services:
  api:
    # ... 其他配置 ...
    logging:
      driver: "fluentd"
      options:
        fluentd-address: "localhost:24224"
        tag: "esim.api"
```

## 8\. 系统监控与告警

### 8.1 Prometheus配置

系统通过`/metrics`端点暴露Prometheus格式的指标。在上述Docker Compose配置中，已经包含了Prometheus服务的配置。主要指标包括：

- HTTP请求指标：`esim_http_request_duration_seconds`、`esim_http_requests_total`、`esim_http_active_requests`
- 数据库指标：`esim_db_query_duration_seconds`
- 缓存指标：`esim_cache_hits_total`、`esim_cache_misses_total`
- eSIM提供商指标：`esim_provider_requests_total`、`esim_provider_request_duration_seconds`
- 异步任务指标：`esim_async_tasks_total`、`esim_async_task_duration_seconds`

> **注意**: 关于如何实现详细的指标采集，特别是追踪特定接口性能的详细指南，请参阅 [Prometheus监控指标实现指南](prometheus-instrumentation.md)。该文档提供了具体的代码示例和实现模式，帮助开发团队正确地添加和使用监控指标。

### 8.2 Grafana配置

Grafana用于可视化Prometheus收集的指标。首次部署后，需要执行以下配置：

1. 访问Grafana界面（http://your-server-ip:3000）
2. 使用默认凭据登录（admin/admin），然后更改密码
3. 添加Prometheus数据源
   - 名称：Prometheus
   - URL：http://prometheus:9090
   - 访问：Server (default)
4. 导入推荐的仪表盘:
   - 系统概览仪表盘
   - API性能仪表盘
   - 数据库性能仪表盘
   - 缓存效率仪表盘
   - 异步任务仪表盘

### 8.3 告警配置

在Prometheus中配置告警规则。创建`/data/esim/prometheus/rules/esim-alerts.yml`文件：

```yaml
groups:
- name: esim-alerts
  rules:
  - alert: HighErrorRate
    expr: rate(esim_http_requests_total{status=~"5.."}[5m]) / rate(esim_http_requests_total[5m]) > 0.05
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "High HTTP error rate (instance {{ $labels.instance }})"
      description: "Error rate is above 5% for the last 5 minutes."

  - alert: SlowResponseTime
    expr: histogram_quantile(0.95, rate(esim_http_request_duration_seconds_bucket[5m])) > 1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Slow response time (instance {{ $labels.instance }})"
      description: "95th percentile of response time is above 1 second."

  - alert: SlowDatabaseQueries
    expr: histogram_quantile(0.95, rate(esim_db_query_duration_seconds_bucket[5m])) > 0.5
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Slow database queries (instance {{ $labels.instance }})"
      description: "95th percentile of database query time is above 0.5 seconds."

  - alert: LowCacheHitRate
    expr: sum(rate(esim_cache_hits_total[5m])) / (sum(rate(esim_cache_hits_total[5m])) + sum(rate(esim_cache_misses_total[5m]))) < 0.7
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Low cache hit rate (instance {{ $labels.instance }})"
      description: "Cache hit rate is below 70% for the last 5 minutes."

  - alert: HighTaskFailureRate
    expr: sum(rate(esim_async_tasks_total{status="failed"}[5m])) / sum(rate(esim_async_tasks_total[5m])) > 0.1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High task failure rate (instance {{ $labels.instance }})"
      description: "Async task failure rate is above 10% for the last 5 minutes."
```

然后更新Prometheus的配置文件，添加规则文件的引用：

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "/etc/prometheus/rules/*.yml"

scrape_configs:
  # ... 保持原有配置不变
```

### 8.4 健康检查

系统提供了`/health`端点用于健康检查。在Docker和Kubernetes环境中，可以使用此端点进行容器健康检查。

## 9\. 缓存策略配置

### 9.1 缓存配置参数

平台实现了多级缓存架构，结合本地内存缓存和Redis分布式缓存，可通过配置文件或环境变量进行配置：

```yaml
cache:
  enabled: true                # 启用缓存
  defaultTTL: "30m"           # 默认缓存项过期时间
  localMaxSize: 10000         # 本地缓存最大项数
  redisKeyPrefix: "esim:"     # Redis键前缀
  preloadOnStart: true        # 是否在启动时预加载数据到缓存
```

环境变量：

- `ESIM_CACHE_ENABLED`
- `ESIM_CACHE_DEFAULTTTL`
- `ESIM_CACHE_LOCALMAXSIZE`
- `ESIM_CACHE_REDISKEYPREFIX`
- `ESIM_CACHE_PRELOADONSTART`

### 9.2 缓存预热配置

在系统启动时可以预加载频繁访问的数据到缓存中，优化冷启动性能：

```yaml
cachePreload:
  userCount: 100          # 预加载活跃用户数量
  esimPackageCount: 50    # 预加载eSIM套餐数量
  rateCount: 20           # 预加载费率数量
  countryCount: 200       # 预加载国家数量
```

环境变量：

- `ESIM_CACHEPRELOAD_USERCOUNT`
- `ESIM_CACHEPRELOAD_ESIMPACKAGECOUNT`
- `ESIM_CACHEPRELOAD_RATECOUNT`
- `ESIM_CACHEPRELOAD_COUNTRYCOUNT`

### 9.3 Redis配置优化

生产环境建议对Redis进行以下优化配置：

```
# Redis配置优化
maxmemory 2gb                 # 最大内存限制
maxmemory-policy volatile-lru # 内存达到上限时优先删除带过期时间的键
tcp-keepalive 60              # 保持连接活动检测时间
databases 2                   # 使用较少的数据库分片
```

这些配置可以在Docker Compose的Redis服务命令行中设置：

```yaml
redis:
  # ... 其他配置 ...
  command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy volatile-lru --tcp-keepalive 60 --databases 2
```

## 10\. 异步任务系统配置

### 10.1 异步任务配置参数

异步任务系统用于处理长时间运行的操作、后台任务和需要重试的关键操作：

```yaml
async:
  workers: 10                  # 工作线程数
  maxRetries: 3                # 最大重试次数
  retryDelays: ["5s", "30s", "2m"] # 重试延迟时间
  queueSize: 100               # 任务队列大小
  taskTTL: "168h"              # 任务数据存活时间(7天)
  cleanerInterval: "1h"        # 清理任务运行间隔
  distributedMode: true        # 是否使用分布式模式
```

环境变量：

- `ESIM_ASYNC_WORKERS`
- `ESIM_ASYNC_MAXRETRIES`
- `ESIM_ASYNC_RETRYDELAYS`
- `ESIM_ASYNC_QUEUESIZE`
- `ESIM_ASYNC_TASKTTL`
- `ESIM_ASYNC_CLEANERINTERVAL`
- `ESIM_ASYNC_DISTRIBUTEDMODE`

### 10.2 任务优先级配置

系统支持任务优先级设置：

```yaml
asyncPriorities:
  high: ["order_process", "esim_activate"]    # 高优先级任务类型
  low: ["stats_update", "cleanup_tasks"]      # 低优先级任务类型
```

环境变量：

- `ESIM_ASYNCPRIORITIES_HIGH`
- `ESIM_ASYNCPRIORITIES_LOW`

### 10.3 Redis队列配置

异步任务系统使用Redis作为分布式任务队列和状态存储。优化Redis配置可以提高异步任务处理性能。

## 11\. 数据库优化配置

### 11.1 PostgreSQL性能配置

为了获得最佳性能，推荐的PostgreSQL配置：

```
# 内存配置
shared_buffers = 1GB          # 或系统内存的25%
work_mem = 32MB              
maintenance_work_mem = 256MB  # 或系统内存的5%
effective_cache_size = 3GB    # 或系统内存的50-75%

# 预写式日志配置
wal_buffers = 16MB
synchronous_commit = off      # 如果可以接受少量数据丢失风险
commit_delay = 1000           # 微秒
commit_siblings = 5

# 查询计划器配置
random_page_cost = 1.1        # 如果使用SSD
effective_io_concurrency = 200 # 如果使用SSD

# 自动清理配置
autovacuum = on
autovacuum_max_workers = 4
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
```

这些配置可以放在挂载到PostgreSQL容器的配置文件中：

```yaml
postgres:
  # ... 其他配置 ...
  volumes:
    - /data/esim/postgres:/var/lib/postgresql/data
    - /data/esim/postgres-conf/postgresql.conf:/etc/postgresql/postgresql.conf
  command: postgres -c config_file=/etc/postgresql/postgresql.conf
```

### 11.2 连接池优化

通过配置文件或环境变量调整数据库连接池参数：

```yaml
database:
  # ... 其他配置 ...
  maxOpenConns: 20           # 最大连接数
  maxIdleConns: 10           # 最大空闲连接数
  connMaxLifetime: "1h"      # 连接最大生命周期
  connMaxIdleTime: "30m"     # 连接最大空闲时间
```

环境变量：

- `ESIM_DATABASE_MAXOPENCONNS`
- `ESIM_DATABASE_MAXIDLECONNS`
- `ESIM_DATABASE_CONNMAXLIFETIME`
- `ESIM_DATABASE_CONNMAXIDLETIME`

### 11.3 查询超时配置

针对查询类型设置不同的超时时间：

```yaml
database:
  # ... 其他配置 ...
  queryTimeouts:
    find: "5s"
    count: "10s"
    create: "3s"
    update: "3s"
    delete: "3s"
    transaction: "15s"
```

环境变量：

- `ESIM_DATABASE_QUERYTIMEOUTS_FIND`
- `ESIM_DATABASE_QUERYTIMEOUTS_COUNT`
- `ESIM_DATABASE_QUERYTIMEOUTS_CREATE`
- `ESIM_DATABASE_QUERYTIMEOUTS_UPDATE`
- `ESIM_DATABASE_QUERYTIMEOUTS_DELETE`
- `ESIM_DATABASE_QUERYTIMEOUTS_TRANSACTION`

## 12\. API限流保护配置

### 12.1 限流配置参数

API限流保护机制用于防止系统过载、抵御恶意请求：

```yaml
rateLimiter:
  enabled: true                # 是否启用限流
  defaultLimit: 60             # 默认限制(每分钟请求数)
  roleLimits:                  # 每个角色的限制
    admin: 300
    reseller: 200
    enterprise: 200
    user: 100
  pathLimits:                  # 每个路径的限制
    "/api/v1/register": 10
    "/api/v1/login": 20
    "/api/v1/webhook/*": 500
  expiry: "1m"                 # 限制窗口
  redisPrefix: "rate_limit:"   # Redis键前缀
```

环境变量：

- `ESIM_RATELIMITER_ENABLED`
- `ESIM_RATELIMITER_DEFAULTLIMIT`
- `ESIM_RATELIMITER_ROLELIMITS_ADMIN`
- `ESIM_RATELIMITER_ROLELIMITS_RESELLER`
- `ESIM_RATELIMITER_ROLELIMITS_ENTERPRISE`
- `ESIM_RATELIMITER_ROLELIMITS_USER`
- `ESIM_RATELIMITER_PATHLIMITS_/API/V1/REGISTER`
- `ESIM_RATELIMITER_PATHLIMITS_/API/V1/LOGIN`
- `ESIM_RATELIMITER_PATHLIMITS_/API/V1/WEBHOOK/*`
- `ESIM_RATELIMITER_EXPIRY`
- `ESIM_RATELIMITER_REDISPREFIX`

### 12.2 全局限流配置

全局限流应用于所有API请求，基于客户端IP地址实现：

```yaml
rateLimiter:
  # ... 其他配置 ...
  globalLimit: 1000            # 每分钟全局请求数限制
```

环境变量：

- `ESIM_RATELIMITER_GLOBALLIMIT`

### 12.3 熔断器配置

熔断器用于防止系统在部分组件故障时被连锁反应拖垮：

```yaml
circuitBreaker:
  enabled: true                # 是否启用熔断器
  threshold: 5                 # 错误阈值
  timeout: "30s"               # 熔断时间
  halfOpenMaxRequests: 2       # 半开状态最大请求数
```

环境变量：

- `ESIM_CIRCUITBREAKER_ENABLED`
- `ESIM_CIRCUITBREAKER_THRESHOLD`
- `ESIM_CIRCUITBREAKER_TIMEOUT`
- `ESIM_CIRCUITBREAKER_HALFOPENMAX"`

## 13\. 备份与恢复

### 13.1 数据库备份

创建定期自动备份脚本`/opt/esim/backup.sh`：

```bash
#!/bin/bash
BACKUP_DIR="/data/esim/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/esim_backup_${TIMESTAMP}.sql"

# 确保备份目录存在
mkdir -p ${BACKUP_DIR}

# 创建备份
docker exec esim-postgres pg_dump -U ${DB_USERNAME} esim > ${BACKUP_FILE}

# 压缩备份
gzip ${BACKUP_FILE}

# 删除7天前的备份
find ${BACKUP_DIR} -name "esim_backup_*.sql.gz" -mtime +7 -delete

# 可选：将备份上传到远程存储
# rclone copy ${BACKUP_FILE}.gz remote:esim-backups/
```

设置执行权限：

```bash
chmod +x /opt/esim/backup.sh
```

创建cron作业进行每日备份：

```bash
# 编辑crontab
crontab -e

# 添加每天凌晨3点运行备份的作业
0 3 * * * /opt/esim/backup.sh >> /var/log/esim-backup.log 2>&1
```

### 13.2 数据恢复

从备份文件恢复数据库：

```bash
# 如果备份文件已压缩
gunzip /data/esim/backups/esim_backup_YYYYMMDD_HHMMSS.sql.gz

# 恢复到数据库
cat /data/esim/backups/esim_backup_YYYYMMDD_HHMMSS.sql | docker exec -i esim-postgres psql -U ${DB_USERNAME} -d esim
```

### 13.3 Redis数据备份

配置Redis自动保存：

```yaml
redis:
  # ... 其他配置 ...
  command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy volatile-lru --save 900 1 --save 300 10 --save 60 10000
```

这配置了Redis在以下情况下执行数据保存：

- 900秒(15分钟)内有至少1个键被修改
- 300秒(5分钟)内有至少10个键被修改
- 60秒(1分钟)内有至少10000个键被修改

## 14\. 故障排除

### 14.1 常见问题

**问题：服务无法启动，日志显示数据库连接错误**

检查：

- 确保PostgreSQL容器正在运行：`docker ps | grep postgres`
- 验证数据库连接配置
- 检查网络连接：`docker exec esim-api ping postgres`

**问题：Redis连接错误**

检查：

- 确保Redis容器正在运行：`docker ps | grep redis`
- 验证Redis连接配置和密码
- 检查网络连接：`docker exec esim-api ping redis`

**问题：API返回500错误**

检查：

- 查看应用日志：`docker logs esim-api`
- 检查Prometheus监控指标
- 验证数据库和Redis连接状态

**问题：缓存命中率低**

检查：

- 观察Grafana中的缓存命中率指标
- 验证TTL设置是否合理
- 确认缓存预热是否正常执行

**问题：API被限流**

检查：

- 查看日志中限流相关记录
- 检查客户端是否发送了过多请求
- 考虑调整限流阈值

### 14.2 查看日志

```bash
# 查看API服务日志
docker logs esim-api

# 实时跟踪日志
docker logs -f esim-api

# 查看特定时间段的日志
docker logs --since=2023-01-01T00:00:00 --until=2023-01-02T00:00:00 esim-api
```

### 14.3 性能分析

启用性能分析器：

```bash
# 临时启用性能分析器
curl -X POST http://your-server:8080/debug/pprof/profile?seconds=30 > profile.out

# 使用Go工具分析
go tool pprof profile.out
```

## 15\. 性能调优

### 15.1 系统资源分配

为每个服务分配足够的资源，根据实际负载调整：

```yaml
services:
  api:
    # ... 其他配置 ...
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
```

### 15.2 定期维护任务

设置定期维护任务：

1. PostgreSQL维护：

```bash
# 创建维护脚本
cat > /opt/esim/db-maintenance.sh << 'EOF'
#!/bin/bash
# 执行VACUUM ANALYZE
docker exec esim-postgres psql -U ${DB_USERNAME} -d esim -c "VACUUM ANALYZE;"

# 更新统计信息
docker exec esim-postgres psql -U ${DB_USERNAME} -d esim -c "ANALYZE;"

# 检查并记录索引膨胀
docker exec esim-postgres psql -U ${DB_USERNAME} -d esim -c "SELECT schemaname, tablename, indexname, pg_size_pretty(pg_relation_size(schemaname||'.'||indexname::text)) as index_size, pg_size_pretty(pg_relation_size(schemaname||'.'||tablename::text)) as table_size FROM pg_indexes ORDER BY pg_relation_size(schemaname||'.'||indexname::text) DESC LIMIT 10;" > /data/esim/maintenance/index_stats_$(date +%Y%m%d).log
EOF

chmod +x /opt/esim/db-maintenance.sh

# 添加到crontab，每周日凌晨2点执行
echo "0 2 * * 0 /opt/esim/db-maintenance.sh >> /var/log/esim-maintenance.log 2>&1" | crontab -
```

1. Redis持久化检查：

```bash
# 创建Redis检查脚本
cat > /opt/esim/redis-check.sh << 'EOF'
#!/bin/bash
# 检查Redis内存使用情况
docker exec esim-redis redis-cli -a ${REDIS_PASSWORD} info memory | grep used_memory_human
# 检查Redis持久化状态
docker exec esim-redis redis-cli -a ${REDIS_PASSWORD} info persistence
EOF

chmod +x /opt/esim/redis-check.sh

# 添加到crontab，每天执行一次
echo "0 1 * * * /opt/esim/redis-check.sh >> /var/log/esim-redis-check.log 2>&1" | crontab -
```

### 15.3 优化启动流程

优化系统启动流程，合理设置启动顺序和健康检查：

```yaml
services:
  postgres:
    # ... 其他配置 ...
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    # ... 其他配置 ...
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 5s
      timeout: 2s
      retries: 10
      start_period: 10s

  api:
    # ... 其他配置 ...
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 附录：常用命令参考

### Docker相关

```bash
# 查看正在运行的容器
docker ps

# 进入容器内部
docker exec -it esim-api /bin/sh

# 查看容器资源使用情况
docker stats

# 查看容器日志
docker logs esim-api

# 清理未使用的容器和镜像
docker system prune -a --volumes
```

### 监控相关

```bash
# 检查Prometheus指标
curl -s http://your-server:8080/metrics | grep esim

# 健康检查
curl -s http://your-server:8080/health
```

### 数据库相关

```bash
# 连接到PostgreSQL
docker exec -it esim-postgres psql -U ${DB_USERNAME} -d esim

# 检查数据库大小
docker exec -it esim-postgres psql -U ${DB_USERNAME} -d esim -c "SELECT pg_size_pretty(pg_database_size('esim'));"

# 查看Redis状态
docker exec -it esim-redis redis-cli -a ${REDIS_PASSWORD} info
```

### 系统维护

```bash
# 重启所有服务
docker compose -f docker-compose.prod.yml restart

# 单独重启API服务
docker compose -f docker-compose.prod.yml restart api

# 查看系统资源使用情况
htop
```