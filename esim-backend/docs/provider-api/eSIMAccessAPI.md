
# eSIM Access API 接口文档

## 项目概述

### 环境和端点

#### 生产环境:

`https://api.esimaccess.com`

图片资源:

`https://static.redteago.com/`

### 认证

请在[在线账户](https://console.esimaccess.com/)中申请您的API密钥。

### 标准

时间代码以UTC表示。国家代码使用ISO Alpha-2标准。数据值以KB为单位。

### 状态

服务器状态通过postman监控[跟踪](https://esimaccess.statuspage.io/)。

### 速率限制

每秒允许8个API请求。

### 错误代码

| 代码 | 消息 |
| --- | --- |
| 000001 | 服务器错误 |
| 000101 | 请求头(必填)为空 |
| 000102 | 错误的请求头格式 |
| 000103 | 不支持此https请求方法(get/post) |
| 000104 | 请求格式无效JSON |
| 000105 | 不包含请求参数(必填) |
| 000106 | 请求参数(必填)为空 |
| 000107 | 请求参数长度不符合要求 |
| 101001 | 请求的时间戳已过期 |
| 101002 | 此IP在黑名单中 |
| 101003 | 请求签名不匹配 |

| 代码 | 消息 |
| --- | --- |
| 200002 | 由于订单状态，不允许此操作 |
| 200005 | 套餐`价格`错误。检查价格 |
| 200006 | 订单总价`金额`错误。检查价格 |
| 200007 | 账户余额不足 |
| 200008 | 订单参数错误，请联系客服 |
| 200009 | 异常订单状态 |
| 200010 | 正在为订单下载资料 |
| 200011 | 套餐可用资料不足，请联系客服 |
| 310201 | bundle.code不存在 |
| 310211 | data_plan_location.id不存在 |
| 310221 | currencyId不存在 |
| 310231 | carrierId不存在 |
| 310241 | `packageCode`不存在 |
| 310243 | 套餐不存在 |
| 310251 | 供应商不存在 |
| 310272 | orderNo不存在 |
| 310403 | 订单中ICCID不存在 |
| 900001 | 系统繁忙，请稍后再试 |

## 认证方法

### 请求头认证

| 名称 | 描述 |
| --- | --- |
| RT-AccessCode | 在您账户中找到的访问密钥，用于签名数据 |
| RT-RequestID | 使用uuid.v4()方法生成的新随机UUID(通用唯一标识符) |
| RT-Signature | 请求的签名(十六进制字符串) |
| RT-Timestamp | 请求发送时间戳(**毫秒**)，字符串格式 |
| SecretKey | 用于签名请求，在您账户中找到 |

### HMAC-SHA256计算:

- signData = Timestamp + RequestID + AccessCode + RequestBody
- signature = HMACSHA256(signData, SecretCode)

```java
// 将RT-Timestamp、RT-RequestID、RT-AccessCode和requestBody连接成一个字符串
// 这个字符串signStr将作为HMAC-SHA256函数中要哈希的数据
String signStr = RT-Timestamp + RT-RequestID + RT-AccessCode + requestBody
// 使用secretKey生成signStr的HMAC-SHA256哈希
// 生成的哈希转换为全小写字符以便标准化
sign = HMACSha256(signStr, secretKey).toLowerCase();
```

### 签名示例

```
Timestamp=1628670421
RequestID=4ce9d9cdac9e4e17b3a2c66c358c1ce2
AccessCode=11111
SecretKey=1111
RequestBody={"imsi":"326543826"}
signStr=16286704214ce9d9cdac9e4e17b3a2c66c358c1ce211111{"imsi":"326543826"}
Signature=7EB765E27DF5373DEA2DBC8C41A7D9557743E46C8054750F3D851B3FD01D0835
```

## API接口

### 获取所有数据套餐

请求所有可用数据套餐列表。可选择按国家或地区过滤。

另外，可请求特定`packageCode`、`slug`或`ICCID`的所有可用充值计划。特定充值套餐只适用于特定计划。通常，国家可以用相同国家的充值套餐充值，地区可以用相同地区的充值套餐充值。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `locationCode` | String | 可选 | 按ISO Alpha-2国家代码过滤<br>`!RG` = 地区<br>`!GL` = 全球 | `JP`<br>`!GL`<br>`!RG` |
| `type` | String | 可选 | `BASE` - 默认产品列表<br>`TOPUP` - 充值产品列表 | `BASE`<br>`TOPUP` |
| `packageCode` | String | 可选 | 与`TOPUP`一起使用，查看`packageCode`的充值套餐 | `JC016` |
| `slug` | String | 可选 | `slug`是`packageCode`的别名 | `AU_1_7` |
| `iccid` | String | 可选 | 与`TOPUP`一起包含`iccid`以查看可用充值计划 | `48584984747372838` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | `null`: 失败。成功包括: `packageList` |  |

| 域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `packageList` | List | 必填 | 可用数据套餐，包括: `packageCode` `name` `price` `currencyCode` `volume` `unusedValidTime` `duration` `durationUnit` `location` `description` `activeType` |  |
| `packageCode` | String | 必填 | 套餐代码 | `JC016` |
| `slug` | String | 必填 | 套餐别名 | `AU_1_7` |
| `name` | String | 必填 | 套餐名称 | `Asia 11 countries 1GB 30 Days` |
| `price` | Integer | 必填 | 套餐价格，值*10,000 (10000 = $1.00) | `10000` |
| `currencyCode` | String | 必填 | 货币代码 | `USD` |
| `volume` | Long | 必填 | 套餐数据量(字节) | `10485760` |
| `smsStatus` | Integer | 必填 | `0`.不支持SMS `1`.API和手机SMS发送 `2`.仅API SMS发送 | `0` |
| `dataType` | Integer | 必填 | `1` 固定流量<br>`2` 每日重置无限流量FUP | `1` |
| `unusedValidTime` | Integer | 必填 | 套餐有效期 | `30` |
| `duration` | Integer | 必填 | 计划有效期 | `1` |
| `durationUnit` | String | 必填 | 时间单位，用于`unusedValidTime`/`duration` | `DAY` |
| `location` | String | 必填 | 套餐使用的ISO Alpha-2国家代码 | `CN,HK,ID,JP,MO,MY,PH,SG,KR,TW,TH,IN,VN,SA,KH,PK,LK` |
| `description` | String | 必填 | 数据套餐描述 | `Asia 11 countries` |
| `activeType` | Integer | 必填 | 激活类型: `1`: 首次安装<br>`2`: 首次网络连接 | `1` |
| `favorite` | Boolean | 必填 | 控制台中收藏的计划 | `false` |
| `retailPrice` | Integer | 必填 | 建议零售价 | `71000` |
| `speed` | String | 必填 | 网络速度 | `3G/4G` |
| `locationNetworkList` | Array | 必填 | `locationName`, `locationLogo` | `Spain`<br>`/img/es.png` |
| `operatorList` | Array | 必填 | `operatorName`,<br>`networkType` | `T-Mobile 4G/5G` |
| `ipExport` | String | 必填 | 数据流量出口国家 | `HK` |
| `supportTopUpType` | Boolean | 必填 | 支持充值 `1` = 否 `2` = 是 | `2` |

#### 请求示例

```json
{
    "locationCode": "",
    "type":"",
    "slug":"NA-3_1_7",
    "packageCode":"",
    "iccid":""
}
```

#### 响应示例

```json
{
    "errorCode": null,
    "errorMsg": null,
    "success": true,
    "obj": {
        "packageList": [
            {
                "packageCode": "CKH491",
                "slug": "NA-3_1_7",
                "name": "North America 1GB 7Days",
                "price": 57000,
                "currencyCode": "USD",
                "volume": 1073741824,
                "smsStatus": 1,
                "dataType": 1,
                "unusedValidTime": 180,
                "duration": 7,
                "durationUnit": "DAY",
                "location": "MX,US,CA",
                "description": "North America 1GB 7Days",
                "activeType": 2,
                "favorite": true,
                "retailPrice": 114000,
                "speed": "3G/4G",
                "locationNetworkList": [
                    {
                        "locationName": "United States",
                        "locationLogo": "/img/flags/us.png",
                        "operatorList": [
                            {
                                "operatorName": "Verizon",
                                "networkType": "5G"
                            },
                            {
                                "operatorName": "T-Mobile",
                                "networkType": "5G"
                            }
                        ]
                    },
                    {
                        "locationName": "Mexico",
                        "locationLogo": "/img/flags/mx.png",
                        "operatorList": [
                            {
                                "operatorName": "Movistar",
                                "networkType": "4G"
                            },
                            {
                                "operatorName": "Telcel",
                                "networkType": "4G"
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

### 订购资料

单独或批量订购资料。成功订购后，SM-DP+服务器将返回`OrderNo`并异步为订单分配资料。

#### 订购步骤

1. 为每个订单提供唯一的`transactionId`。重复的`transactionId`将被识别为相同的请求。
2. 提供您将订购的数据套餐的`packageCode`或`slug`。
3. 提供每个需要的套餐的`count`。
4. 可选价格检查: 提供`price`并乘以`count`得到总成本，提供`amount`。
5. 可选周期: 对于每日计划，包括与计划天数对应的`periodNum`。

成功订单将生成`orderNo`。通过`/api/v1/open/esim/query`端点查询所有分配的资料。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `transactionId` | String | 必填 | 用户生成的唯一交易ID。最大50个字符，utf8mb4。如果请求重试，需要包含；否则，将创建新交易。 | `ABC-210-2s7Fr` |
| `amount` | Long | 可选 | 订单总金额 | `20000` |
| `packageInfoList` | List | 必填 | `packageCode`或`slug`, `count`, `price` |  |

| 域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `packageCode` | String | 必填 | 使用`slug`或`packageCode`订购<br>(首选slug) | `AU_1_7`<br>`JC016` |
| `count` | Integer | 必填 | 要订购的套餐数量 | `2` |
| `price` | Integer | 可选 | 套餐价格，值*10,000 (10000 = $1.00) | `10000` |
| `periodNum` | Integer | 可选 | 每日计划的天数。1-365之间。 | `7` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 包括: `orderNo` |  |

| 域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `orderNo` | String | 必填 | 订单号 | `B22102010075311` |

#### 请求示例

```json
{
    "transactionId":"your_txn_id",
    "amount":15000,
    "packageInfoList": [{
        "packageCode":"7aa948d363",
        "count":1,
        "price":15000
    }]
}
```

#### 响应示例

```json
{
    "errorCode": null,
    "errorMsg": null,
    "success": true,
    "obj": {
        "orderNo": "B23051616050537",
        "transactionId": "Your_txn_id"
    }
}
```

### 查询所有分配的资料

查询分配给合作伙伴的所有eSIM资料详情及其状态。

可以通过`orderNo`、`iccid`或`startTime`和`endTime`范围进行过滤，并提供分页选项。

使用`orderNo`请求新订购的eSIM。在服务器异步分配所有资料后，响应将返回eSIM负载。预计等待时间最长为30秒。

使用`iccid`请求eSIM的状态，包括其当前的`orderUsage`和`eSIMStatus`。

如果资料尚未准备好下载，将返回错误(错误代码为`200010`，表示SM-DP+仍在为订单分配资料)。

#### 了解eSIM资料状态

几个参数的结果可以识别任何eSIM资料的当前状态。例如:

| **eSIM状态** | `smdpStatus` | `esimStatus` | `orderUsage` | `eid` |
| --- | --- | --- | --- | --- |
| **新建** | `RELEASED` | `GOT_RESOURCE` | `0` | `""` |
| **安装** | `ENABLED` | `IN_USE` `GOT_RESOURCE` | `0` | `"890…222"` |
| **使用中** | `ENABLED` `DISABLED` | `IN_USE` | `123` | `"890…222"` |
| **已用完** | `ENABLED` `DISABLED` | `USED_UP` | `999` | `"890…222"` |
| **已删除** | `DELETED` | `USED_UP` `IN_USE` | `999` | `"890…222"` |

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `orderNo` | String | 可选 | 订单号 | `B2210206381924` |
| `iccid` | String | 可选 | eSIM ICCID | `89852246280001113119` |
| `startTime` | String | 可选 | 开始时间(ISO UTC时间) | `2010-06-30T01:20+00:00` |
| `endTime` | String | 可选 | 结束时间(ISO UTC时间) | `2010-06-30T02:20+00:00` |
| `pager` | PageParam | 必填 | 分页参数: `pageSize` `pageNum` |  |

| 域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `pageSize` | Integer | 必填 | 页面大小，取值范围: [5, 500] | `10` |
| `pageNum` | Integer | 必填 | 页码，取值范围: [1, 10000] | `1` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 包括: `esimList` `pager` |  |

| 域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `pager` | PageParam | 必填 | 包括: `pageSize` `pageNum` |  |
| `esimList` | List | 必填 | eSIM资料列表，包括: `esimTranNo` `orderNo` `imsi` `iccid` `ac` `qrCodeUrl` `smdpStatus` `eid` `activeType` `expiredTime` `totalVolume` `totalDuration` `durationUnit` `orderUsage` `esimStatus` `packageList` |  |

| PageParam域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `pageSize` | Integer | 必填 | 页面大小，范围: [5, 500] | `10` |
| `pageNum` | Integer | 必填 | 页码，取值范围: [1, 10000] | `1` |
| `total` | Long | 必填 | 资料总数 | `120` |

| eSIM域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `esimTranNo` | String | 必填 | eSIM交易号 | `22102706381912` |
| `orderNo` | String | 必填 | 订单号 | `B22102706381924` |
| `imsi` | String | 可选 | IMSI | `454006109846571` |
| `iccid` | String | 可选 | ICCID | `89852245280000942210` |
| `msisdn` | String | 可选 | MSISDN | `xxxxx` |
| `smsStatus` | Integer | 必填 | `0` 不支持SMS `1` 可接受手机和API发送的SMS `2` 只接受API发送的SMS | `0` |
| `dataType` | Integer | 必填 | `1` 固定流量<br>`2` 每日重置无限流量FUP | `1` |
| `ac` | String | 必填 | eSIM激活码<br>LPA:1${SM-DP+_ADDRESS}${MATCHING_ID} | `LPA:1$rsp-eu.redteamobile.com$451F9802E6854E3E85FB985235EDB4E5` |
| `qrCodeUrl` | String | 必填 | 二维码URL | [<code>http://static.redtea.io//hedy/qrcodes/image/d6dbada5054a4dfeb941e601327a4b42.png</code>](http://static.redtea.io//hedy/qrcodes/image/d6dbada5054a4dfeb941e601327a4b42.png) |
| `smdpStatus` | String | 必填 | SM-DP+状态: `RELEASED`: 资料准备好下载 `DOWNLOAD`: 资料已下载 `INSTALLATION`: 资料已安装 `ENABLED`: 资料已启用 `DISABLED`: 资料已禁用 `DELETED`: 资料已删除 | `RELEASED` |
| `eid` | String | 可选 | EID |  |
| `activeType` | String | 必填 | 激活类型: `1`: 首次安装<br>`2`: 首次网络连接 | `1` |
| `expiredTime` | DateTime | 必填 | 过期时间 | `2023-03-03T06:20:00+0000` |
| `totalVolume` | Long | 必填 | 套餐中的总数据量(字节) | `1073741824` |
| `totalDuration` | Integer | 必填 | 套餐的总有效期 | `7` |
| `durationUnit` | String | 必填 | 时间单位 | `DAY` |
| `orderUsage` | Long | 必填 | 已使用数据量(字节) | `0` |
| `pin` `puk` `apn` | String | 可选 | PIN, PUK, APN值(如果有) | `329393` |
| `esimStatus` | String | 必填 | `CREATE` 已创建订单 `PAYING` 订户正在为eSIM付款 `PAID` eSIM已付款 `GETTING_RESOURCE` 正在为订单分配eSIM `GOT_RESOURCE` 已为订单分配eSIM `IN_USE` eSIM数据套餐正在使用中 `USED_UP` 套餐中的数据已用完 `UNUSED_EXPIRED` eSIM下载的有效期已过期 `USED_EXPIRED` 订单激活的有效期已过期 `CANCEL` 订单已取消 `SUSPENDED` 已通过suspend端点暂停订单 `REVOKE` 已通过revoke端点撤销订单 | `UNUSED_EXPIRED` |
| `packageList` | List | 必填 | 包括: `packageCode` `duration` `volume` `locationCode` |  |

| eSIM域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `packageCode` | String | 必填 | 套餐ID | `CKH179` |
| `duration` | Integer | 必填 | 订单的有效期 | `7` |
| `volume` | Long | 必填 | 订单中的数据量(字节) | `1073741824` |
| `locationCode` | String | 必填 | 计划的国家代码 | `JP` |

#### 请求示例

```json
{
    "orderNo":"B23120118131854",
    "iccid":"",
    "pager":{
        "pageNum":1,
        "pageSize":20
    }
}
```

#### 响应示例

```json
{
    "success": true,
    "errorCode": "0",
    "errorMsg": null,
    "obj": {
        "esimList": [
            {
                "esimTranNo": "23120118156818",
                "orderNo": "B23120118131854",
                "transactionId": "test344343433",
                "imsi": "232104070077567",
                "iccid": "8943108170000775671",
                "smsStatus": 1,
                "msisdn": "436789040077567",
                "ac": "LPA:1$rsp-eu.redteamobile.com$43DE23C67EE747BCAD6B63E8B67B261F",
                "qrCodeUrl": "https://p.qrsim.net/0fa4f29eb25b4d6c84ff4b8422a1da54.png",
                "shortUrl": "https://p.qrsim.net/0fa4f29eb25b4d6c84ff4b8422a1da54",
                "smdpStatus": "RELEASED",
                "eid": "",
                "activeType": 1,
                "dataType": 1,
                "activateTime": null,
                "expiredTime": "2024-05-29T18:34:17+0000",
                "totalVolume": 5368709120,
                "totalDuration": 30,
                "durationUnit": "DAY",
                "orderUsage": 0,
                "esimStatus": "CANCEL",
                "pin": "",
                "puk": "",
                "apn": "drei.at",
                "packageList": [
                    {
                        "packageName": "Spain 5GB 30Days",
                        "packageCode": "CKH003",
                        "slug": "ES_5_30",
                        "duration": 30,
                        "volume": 5368709120,
                        "locationCode": "ES",
                        "createTime": "2023-12-01T18:34:17+0000"
                    }
                ]
            }
        ],
        "pager": {
            "pageSize": 20,
            "pageNum": 1,
            "total": 1
        }
    }
}
```

### 取消资料

取消未使用的不活动eSIM资料，移除与其关联的所有信息。**退款到您的余额**。

当`esimStatus`为`GOT_RESOURCE`且`smdpStatus`为`RELEASED`时可进行此操作，表示eSIM已创建但未安装在设备上。

用户安装eSIM后无法使用取消端点。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `iccid` | String | 可选 | eSIM ICCID | `89852246280001113119` |
| `esimTranNo` | String | 可选 | 从"查询所有分配的资料"获取<br><br>使用"iccid"或"esimTranNo"，不能同时为空<br><br>推荐使用 | `24111319542101` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 包括 | {} |

#### 请求示例

```json
{
    "esimTranNo": "23120118156818"
}
```

#### 响应示例

```json
{
    "success": true,
    "errorCode": "0",
    "errorMsg": null,
    "obj": {}
}
```

### 暂停资料

请求暂停或暂停向eSIM资料提供数据服务。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `iccid` | String | 可选 | eSIM ICCID | `89852246280001113119` |
| `esimTranNo` | String | 可选 | 从"查询所有分配的资料"获取<br><br>使用"iccid"或"esimTranNo"，不能同时为空<br><br>推荐使用 | `24111319542101` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 包括 | {} |

#### 请求示例

```json
{
    "iccid":"89852245280001138065"
}
```

### 取消暂停资料

请求取消暂停或重新激活向eSIM资料提供数据服务。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `iccid` | String | 可选 | eSIM ICCID | `89852246280001113119` |
| `esimTranNo` | String | 可选 | 从"查询所有分配的资料"获取<br><br>使用"iccid"或"esimTranNo"，不能同时为空<br><br>推荐使用 | `24111319542101` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 包括 | {} |

#### 请求示例

```json
{
    "iccid":"89852245280001138065"
}
```

### 撤销资料

请求关闭并移除活动的eSIM和数据计划。不可退款。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `iccid` | String | 可选 | eSIM ICCID | `89852246280001113119` |
| `esimTranNo` | String | 可选 | 从"查询所有分配的资料"获取<br><br>使用"iccid"或"esimTranNo"，不能同时为空<br><br>推荐使用 | `24111319542101` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 包括 | {} |

#### 请求示例

```json
{
    "iccid":"89852245280001138065"
}
```

### 余额查询

查询商户账户的`balance`。余额用于订购数据资料。

#### 请求参数

无。

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 包括: `balance` |  |

| 域 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `balance` | Long | 必填 | 商户余额，表示*10000 (100000 = $10.00) | `100000` |

#### 响应示例

```json
{
    "success": true,
    "errorCode": "0",
    "errorMsg": null,
    "obj": {
        "balance": 940000
    }
}
```

### 充值

在查询了`iccid`、`esimTranNo`或`packageCode`的可用充值计划后，充值端点允许为现有已安装的eSIM加载新计划。要给计划充值，您需要其ICCID或esimTranNo以及**兼容的**充值数据计划`packageCode`。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `iccid` | String | 可选 | eSIM ICCID (已弃用，使用`esimTranNo`) | `89852246280001113119` |
| `esimTranNo` | String | 可选 | 从"查询所有分配的资料"获取<br><br>使用"iccid"或"esimTranNo"，不能同时为空<br><br>推荐使用 | `24111319542101` |
| `packageCode` | String | 必填 | 充值套餐编号 | `SM001` |
| `amount` | String | 可选 | 套餐价格，如果使用将被验证 | `10000` |
| `transactionId` | String | 必填 | 用户创建的交易ID | `TXN-123` |

#### 响应参数

| obj | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `transactionId` | String | 必填 | 返回的交易ID | `TXN-123` |
| `iccid` | String | 必填 | eSIM的ICCID | `89852245280001354019` |
| `expiredTime` | Long | 必填 | 套餐到期的新日期 | `2023-08-17T17:01:37+0000` |
| `totalVolume` | Long | 必填 | 新的数据量 | `4294967296` |
| `totalDuration` | Integer | 必填 | 新的持续时间(天) | `28` |
| `orderUsage` | Long | 必填 | 总数据使用量 | `207239584` |

#### 请求示例

```json
{
    "esimTranNo":"23072017992029",
    "iccid":"",
    "packageCode":"TOPUP_CKH491",
    "transactionId": "ed8d49cf2da34814a4db7b8be29d06bc"
}
```

#### 响应示例

```json
{
    "errorCode": null,
    "errorMsg": null,
    "success": true,
    "obj": {
        "transactionId": "ed8d49cf2da34814a4db7b8be29d06bc",
        "iccid": "89852245280001354019",
        "expiredTime": "2023-09-07T17:01:37+0000",
        "totalVolume": 7516192768,
        "totalDuration": 49,
        "orderUsage": 6841270782
    }
}
```

### 设置Webhook

通过API调用设置或更新您的webhook URL。您可以在[这里](https://console.esimaccess.com/developer/index)的控制台账户中找到结果。

您也可以使用以下端点查看当前设置的webhook：
**`/api/v1/open/webhook/query`**

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `webhook` | String | 必填 | 要设置的webhook URL | `https://webhook.endpoint.site/unique-webhook` |

#### 响应参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `success` | String | 必填 | `true`: 成功 `false`: 失败 | `true` |
| `errorCode` | String | 可选 | 成功时为`null`或`0`。失败时为错误代码 | `null` |
| `errorMessage` | String | 可选 | 错误代码说明 | `null` |
| `obj` | Object | 可选 | 成功时为空对象 | {} |

#### 请求示例

```json
{"webhook":"https://webhook.endpoint.site/unique-webhook"}
```

#### 响应示例

```json
{
    "success": true,
    "errorCode": "0",
    "errorMsg": null,
    "obj": {}
}
```

### 发送SMS

此端点用于通过`iccid`或`esimTranNo`向eSIM发送SMS。部分网络支持。只有支持接收SMS的已安装eSIM才能使用。/order和/package端点中的`smsStatus`参数指示eSIM是否支持接收SMS(`"smsStatus": 1 or 2`)。目前SMS发送不收费。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `iccid` | String | 可选 | eSIM ICCID | `89852246280001113119` |
| `esimTranNo` | String | 可选 | 从"查询所有分配的资料"获取<br><br>使用"iccid"或"esimTranNo"，不能同时为空<br><br>推荐使用 | `24111319542101` |
| `message` | String(500) | 必填 | SMS消息，最多500个字符 | "感谢您使用我们的eSIM服务" |

#### 请求示例

```json
{
    "esimTranNo":"23072017992029",
    "message":"您的消息！"
}
```

#### 响应示例

```json
{
    "success": true,
    "errorCode": "0",
    "errorMsg": null,
    "obj": {}
}
```

### 使用查询

通过`esimTranNo`查询最多10个eSIM的数据使用情况。返回`dataUsage`数量、计划中的`totalData`以及最近数据使用值更新的`lastUpdateTime`时间戳。

#### 请求参数

| 名称 | 类型 | 必填 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `esimTranNoList` | Array | 必填 | 要查询的eSIM交易号列表 | `["25030303480009"]` |

#### 响应参数

| 字段 | 类型 | 描述 | 示例 |
| --- | --- | --- | --- |
| `esimTranNo` | String | eSIM交易号 | `23072017992029` |
| `dataUsage` | Long | 数据使用量(字节) | `1453344832` |
| `totalData` | Long | 总数据量(字节) | `5368709120` |
| `lastUpdateTime` | String | 最后一次通话记录更新的时间戳。对于基于文件的记录，这是最后一个完整小时的结算时间；对于运营商数据使用通知，这是通知中记录的结算时间；对于运营商的实时通话记录API，这是调用API的时间。 | `2025-03-19T18:00:00+0000` |

#### 请求示例

```json
{
  "esimTranNoList": ["25030303480009"]
}
```

#### 响应示例

```json
{
    "success": true,
    "errorCode": "0",
    "errorMsg": null,
    "obj": {
        "esimUsageList": [
            {
                "esimTranNo": "25031120490003",
                "dataUsage": 1453344832,
                "totalData": 5368709120,
                "lastUpdateTime": "2025-03-19T18:00:00+0000"
            }
        ]
    }
}
```

### 支持的区域

检查我们当前支持的国家和计划代码。

#### 请求参数

无。

#### 响应参数

| 字段 | 类型 | 描述 | 示例 |
| --- | --- | --- | --- |
| `code` | String | 区域代码 | `ES` `NA-3` |
| `name` | String | 区域名称 | `Spain` `North America` |
| `type` | Integer | 区域类型：1表示单国家，2表示多国家 | `1` |
| `subLocationList` | List | 子区域(仅当type = 2时存在) |  |

每个`SubLocation`对象包含：

| 字段 | 类型 | 描述 | 示例 |
| --- | --- | --- | --- |
| `code` | String | 区域代码 |  |
| `name` | String | 区域名称 |  |

#### 请求示例

```json
{}
```

#### 响应示例

```json
{
    "success": true,
    "errorCode": "0",
    "errorMsg": null,
    "obj": {
        "locationList": [
            {
                "code": "ES",
                "name": "Spain",
                "type": 1,
                "subLocationList": null
            }
        ]
    }
}
```

## Webhooks

**在[您的账户](https://console.esimaccess.com/)中设置您的webhook URL以接收POST请求**。

通知包含一个指示事件类别的`notifyType`字段和一个包含特定详情的`content`对象。以下是您可以期待的类型：

1. **`ORDER_STATUS`**
   - **触发：** 当eSIM创建并准备好检索时发送。
   - **关键`content`字段：** `orderStatus`将为"`GOT_RESOURCE`"。
   - **使用示例：** 用于了解您的eSIM订单何时准备好下载。

2. **`ESIM_STATUS`**
   - **触发：** 当单个eSIM的状态在分配后发生变化时发送。这涵盖了各种生命周期事件。
   - **关键`content`字段：**
     - `esimStatus`：指示当前状态。常见值包括：
       - `IN_USE`：eSIM已在设备上安装/激活。
       - `USED_UP`：eSIM数据量已完全消耗。
       - `USED_EXPIRED`：eSIM数据已用完，且已过期。
       - `UNUSED_EXPIRED`：eSIM已过期，但仍有剩余数据。
       - `CANCEL`：eSIM已取消/退款。
       - `REVOKED`：eSIM资料已撤销。
     - `smdpStatus`：SM-DP+服务器状态(例如，`ENABLED`, `DISABLED`, `RELEASED`, `DELETED`, `INSTALLATION`)。
   - **使用示例：** 跟踪特定eSIM的激活、数据耗尽、过期或取消情况，并通知客户。

3. **`DATA_USAGE`**
   - **触发：** 当活动eSIM上的剩余数据量降至预定义阈值(例如，50%, 20%, 10%)以下时发送。
   - **关键`content`字段：**
     - `totalVolume`：总数据量(字节)。
     - `orderUsage`：目前已使用的数据量(字节)。
     - `remain`：剩余数据量(字节)。
     - `remainThreshold`(可选)：可能指示触发通知的阈值(例如，0.5表示50%)。
   - **使用示例：** 主动通知最终用户数据余额不足。

4. **`VALIDITY_USAGE`**
   - **触发：** 当活动eSIM的剩余有效期达到1天时发送。
   - **关键`content`字段：**
     - `remain`：剩余有效期(例如，1)。
     - `durationUnit`：持续时间的单位(例如，"DAY")。
     - `expiredTime`：eSIM将过期的确切时间戳。
     - `totalDuration`：原始有效期。
   - **使用示例：** 警告最终用户他们的计划即将到期。

**注意：** `content`对象结构可能略有不同。请始终检查接收到的载荷以了解每个`notifyType`的所有可用字段。

[查看我们的webhook发送示例和测试触发表单](https://esimaccess.com/docs/what-webhook-notifications-do-you-send/)。

```json
{
    "notifyType": "ORDER_STATUS",
    "content": {
        "orderNo": "B23072016497499",
        "orderStatus": "GOT_RESOURCE"
    }
}
```

```json
{
    "notifyType": "ESIM_STATUS",
    "content": {
        "orderNo": "B23072016497499",
        "transactionId": "Your_txn_id",
        "iccid": "894310817000000003",
        "esimStatus": "IN_USE",
        "smdpStatus": "INSTALLATION"
    }
}
```

```json
{
    "notifyType": "DATA_USAGE",
    "content": {
        "orderNo": "B23072016497499",
        "transactionId": "Your_txn_id",
        "iccid": "894310817000000003",
        "totalVolume": 121313,
        "orderUsage": 232,
        "remain": 12123
    }
}
```

```json
{
    "notifyType": "VALIDITY_USAGE",
    "content": {
        "orderNo": "B23072016497499",
        "transactionId": "Your_txn_id",
        "iccid": "894310817000000003",
        "durationUnit": "DAY",
        "totalDuration": 30,
        "expiredTime": "2024-01-11T08:10:19Z",
        "remain": 1
    }
}
```

```json
{
  "notifyType": "ESIM_STATUS",
  "content": {
    "orderNo": "B24112011030010",
    "esimTranNo": "24112011030010",
    "transactionId": "56352736-1-1",
    "iccid": "898500000048984",
    "esimStatus": "CANCEL",
    "smdpStatus": "RELEASED"
  }
}
```
