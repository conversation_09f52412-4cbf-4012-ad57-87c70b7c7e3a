# Maya Mobile Connect+ API
The Maya Mobile Connectivity API provides methods for managing and automating resources on the Maya Mobile Connectivity Platform, including eSIMs and data plans.

## Version: 1.0.0

**Contact information:**  
Maya Mobile Partner Team  
https://help.maya.net  
<EMAIL>  

### /connectivity/v1/esim

#### POST
##### Summary:

Create eSIM

##### Description:

Use this endpoint to request an eSIM, including QR code and in-app activation credentials.

##### Responses

| Code | Description |
| ---- | ----------- |
| 201 | Created  Returns the properties of the new eSIM, including the associated Data Plan, if created. |
| 500 | Internal Server Error  Unable to complete request, or no eSIM available for requested region. |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221466

#### PATCH
##### Summary:

Change eSIM

##### Description:

Use this endpoint to update an eSIM in your account, including: 

* **Enable** or **Disable** the eSIM's network access.
* Change the **Customer ID** that is associated with the eSIM.
* Update the **Tag** associated with the eSIM.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 400 | Bad Request |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221483

#### GET
##### Summary:

Get eSIM

##### Description:

Use this endpoint to get the current status and details of an eSIM.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 404 | Not Found |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221468/plans

#### GET
##### Summary:

Get eSIM Plans

##### Description:

Get the Data Plans currently attached to an eSIM, including **data usage** for each Data Plan.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 404 | Not Found |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221468/regions

#### GET
##### Summary:

Get eSIM Regions

##### Description:

Get the countries where an eSIM is allowed to access roaming data. The countries allowed are dependent on the country or region that was specified when the eSIM was created.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 404 | Not Found |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221380

#### DELETE
##### Summary:

Delete eSIM

##### Description:

Delete an eSIM from the distributor account. This will immediately terminate any Data Plans attached to the eSIM, and suspend network access.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 400 | Bad Request |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/sms

#### POST
##### Summary:

Send SMS

##### Description:

Send an SMS message (text message) to an installed eSIM.

##### Responses

| Code | Description |
| ---- | ----------- |
| 201 | Created  SMS message sent successfully. |
| 400 | Bad Request - Invalid Request Paremeters |
| 500 | Internal Server Error  Unable to complete request due to Server Error. |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/plan-type

#### POST
##### Summary:

Create Plan Type

##### Description:

Create a Plan Type. Plan Types are templates that are used to define parameters for Data Plans added to eSIMs.

##### Responses

| Code | Description |
| ---- | ----------- |
| 201 | Created |
| 400 | Bad Request - Invalid Request Paremeters |
| 500 | Internal Server Error - Unable to complete request |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/plan-type/Gk3G2dyeaLTV

#### GET
##### Summary:

Get Plan Type

##### Description:

Get a Plan Type and its configuration.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 404 | Not Found |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/account/plan-types

#### GET
##### Summary:

Get All Plan Types

##### Description:

Get all Plan Types active in the distributor account.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 403 | Forbidden |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/plan-type/c15VFKgCEQei

#### DELETE
##### Summary:

Delete Plan Type

##### Description:

Delete a Plan Type from the distributor account. This will NOT affect active Data Plans associated with the Plan Type.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 400 | Bad Request |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221468/plan/Gk3G2dyeaLTV

#### POST
##### Summary:

Create Plan (Top Up)

##### Description:

Use this endpoint to **create a Data Plan (top up)** on an eSIM based on a pre-defined **Product** or **Plan Type** in your account.

The request URI must include:

* The eSIM to attach, specified by ICCID number
* The Product / Plan Type to attach, specified by Product / Plan Type ID 

##### Responses

| Code | Description |
| ---- | ----------- |
| 201 | Created  Returns the newly created Data Plan with unique ID, and configuration options defined by the selected Product or Plan Type. |
| 400 | Bad Request  Invalid Plan Type or eSIM  |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221466/plan/afNtPIkAT6ZG

#### PUT
##### Summary:

Refresh Plan

##### Description:

Use this endpoint to top up the specified eSIM with a new Data Plan using exactly the same paremeters as the specified Data Plan. The data usage and time-based validity period will be reset, and the previous Data Plan will be terminated.

Request URI must include:

* The target eSIM specified by ICCID number.
* The target Data Plan specified by unique ID.

##### Responses

| Code | Description |
| ---- | ----------- |
| 201 | Created  Returns the unique ID and properties for the newly created Data Plan. |
| 400 | Bad Request  Invalid eSIM or no eSIM found. |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/account/networks

#### GET
##### Summary:

Get Network List

##### Description:

Get all roaming networks enabled for the account. Useful for importing network names, MCCMNC codes, and 5G/4G/LTE connectivity support details into an external application.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK  Returns list of roaming networks enabled for the distributor account. |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/esim/8920400000006221468/plan/Saz1Bp9fnW70

#### GET
##### Summary:

Get Plan

##### Description:

Get current status of a Data Plan, including **data usage** and network activation status.

Request URI must include:

* The target eSIM specified by ICCID number.
* The target Data Plan specified by unique ID.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK  Returns the current status and configuration of the requested Data Plan, and the associated Product or Plan Type metadata. |
| 400 | Bad Request  Invalid Data Plan unique ID. |
| 500 | Internal Server Error  Unable to retrieve Data Plan status from network. |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

#### DELETE
##### Summary:

Delete Plan

##### Description:

Detach a Data Plan from an eSIM. This will immediately terminate the Data Plan on the network. The Data Plan cannot be re-attached or re-activated (a new Data Plan must be created).

Request URI must include:

* The target eSIM specified by ICCID number.
* The target Data Plan specified by unique ID.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 400 | Bad Request  Invalid unique ID for target Data Plan. |
| 500 | Internal Server Error  Unable to process Data Plan termination request. |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/account/products?region=europe&country=us

#### GET
##### Summary:

Get All Products

##### Description:

Use this endpoint to retrieve a filtered list of Products in your account.

##### Parameters

| Name | Located in | Description | Required | Schema |
| ---- | ---------- | ----------- | -------- | ---- |
| region | query | europe, apac, latam, caribbean, mena, balkans, caucasus | No | string |
| country | query | ISO2 code for a country, e.g. fr, us, ca | No | string |

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/product/sAOV5Qxb19qF

#### GET
##### Summary:

Get Product

##### Description:

Use this endpoint to get the details of a single Product (Data Plan type) in your account. The endpoint will return the Product speficiations and wholesale price, which will be applied to every Data Plan created with the Product.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/customer/

#### POST
##### Summary:

Create Customer

##### Description:

Use this endpoint to create a Customer record in your Connect+ instance. The customer is for record-keeping purposes only, and can be associated with eSIMs and Data Plans. The customer cannot log in to Connect+ and will not receive any communication from Maya Mobile.

##### Responses

| Code | Description |
| ---- | ----------- |
| 201 | Created |
| 400 | Bad Request |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/customer/tnE9RJgSZdi6

#### GET
##### Summary:

Get Customer

##### Description:

Use this endpoint to get the details of a single Customer in your account, including all **eSIMs** and **Data Plans** assigned to the Customer.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 404 | Not Found |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

#### PATCH
##### Summary:

Update Customer

##### Description:

Use this endpoint to update the details of a Customer in your account.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 400 | Bad Request |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

#### DELETE
##### Summary:

Delete Customer

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |
| 400 | Bad Request |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |

### /connectivity/v1/account/balance

#### GET
##### Summary:

Get Account Balance

##### Description:

Use this endpoint to quickly retrieve the current Credit Balance and Overdraft status of your account.

##### Responses

| Code | Description |
| ---- | ----------- |
| 200 | OK |

##### Security

| Security Schema | Scopes |
| --- | --- |
| basicAuth | |
