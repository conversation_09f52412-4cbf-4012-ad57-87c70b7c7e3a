# Prometheus监控指标实现指南

本文档详细说明如何在eSIM平台中实现和使用Prometheus监控指标，特别关注如何对特定接口进行性能追踪。

## 目录

- [1. 监控指标概述](#1-监控指标概述)
- [2. 基础指标结构](#2-基础指标结构)
- [3. 接口性能追踪实现](#3-接口性能追踪实现)
- [4. 数据库查询监控](#4-数据库查询监控)
- [5. 缓存性能监控](#5-缓存性能监控)
- [6. 异步任务监控](#6-异步任务监控)
- [7. 自定义业务指标](#7-自定义业务指标)
- [8. 查询示例](#8-查询示例)
- [9. 最佳实践](#9-最佳实践)

## 1. 监控指标概述

eSIM平台使用Prometheus进行监控，收集以下几类关键指标：

- HTTP接口性能指标
- 数据库查询性能指标
- 缓存性能指标
- 异步任务处理指标
- 业务指标(如eSIM激活数、订单量等)

所有指标都以`esim_`前缀开头，遵循Prometheus命名规范。

## 2. 基础指标结构

### 2.1 核心指标类型

Prometheus提供四种基本指标类型：

- **Counter**: 只增不减的计数器，用于计算事件发生次数
- **Gauge**: 可增可减的仪表，表示可变的值
- **Histogram**: 直方图，用于测量事件持续时间等分布式数据
- **Summary**: 摘要，类似直方图但提供分位数计算

### 2.2 基础指标初始化

在eSIM平台中，监控指标在`internal/monitoring/metrics.go`文件中集中定义：

```go
package monitoring

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // HTTP请求计数器
    HTTPRequestsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "path", "status"},
    )

    // HTTP请求持续时间
    HTTPRequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "esim_http_request_duration_seconds",
            Help:    "HTTP request duration in seconds",
            Buckets: prometheus.ExponentialBuckets(0.001, 2, 15), // 从1ms到16s的范围
        },
        []string{"method", "path"},
    )

    // 活动请求数量
    HTTPActiveRequests = promauto.NewGauge(
        prometheus.GaugeOpts{
            Name: "esim_http_active_requests",
            Help: "Current number of active HTTP requests",
        },
    )

    // ... 其他核心指标定义
)
```

## 3. 接口性能追踪实现

### 3.1 HTTP中间件实现

使用Echo框架的中间件来对所有HTTP请求进行监控，在`internal/api/middleware/metrics.go`中实现：

```go
package middleware

import (
    "github.com/labstack/echo/v4"
    "github.com/your-org/esim-backend/internal/monitoring"
    "strconv"
    "time"
)

// MetricsMiddleware 创建一个记录HTTP请求指标的中间件
func MetricsMiddleware() echo.MiddlewareFunc {
    return func(next echo.HandlerFunc) echo.HandlerFunc {
        return func(c echo.Context) error {
            // 增加活动请求计数
            monitoring.HTTPActiveRequests.Inc()
            defer monitoring.HTTPActiveRequests.Dec()

            // 开始时间
            start := time.Now()

            // 执行请求处理
            err := next(c)

            // 记录请求持续时间
            duration := time.Since(start).Seconds()
            path := normalizePath(c.Path())
            
            // 获取HTTP状态码
            status := c.Response().Status
            if err != nil {
                // 如果有错误，可能需要特殊处理状态码
                // 例如，可以检查err是否为HTTPError，并提取其状态码
            }

            // 更新请求计数指标
            monitoring.HTTPRequestsTotal.WithLabelValues(
                c.Request().Method,
                path,
                strconv.Itoa(status),
            ).Inc()

            // 更新请求持续时间指标
            monitoring.HTTPRequestDuration.WithLabelValues(
                c.Request().Method,
                path,
            ).Observe(duration)

            return err
        }
    }
}

// normalizePath 规范化路径，去除ID等动态部分
func normalizePath(path string) string {
    // 这里可以实现路径规范化逻辑
    // 例如将 /api/v1/users/123 转换为 /api/v1/users/:id
    // 使用正则表达式或路由解析器来识别路径模式
    
    // 简单示例实现（实际项目中可能需要更复杂的逻辑）
    // return middleware.GetPathWithParamsAsPlaceholders(path)
    return path
}
```

### 3.2 特定接口性能追踪

对于需要特别关注的接口，可以添加更详细的指标：

```go
// 在internal/monitoring/metrics.go中添加特定接口的指标
var (
    // 订单创建性能指标
    OrderCreationDuration = promauto.NewHistogram(
        prometheus.HistogramOpts{
            Name:    "esim_order_creation_duration_seconds",
            Help:    "Order creation operation duration in seconds",
            Buckets: prometheus.ExponentialBuckets(0.01, 2, 10),
        },
    )
    
    // eSIM激活性能指标
    ESIMActivationDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "esim_activation_duration_seconds",
            Help:    "eSIM activation operation duration in seconds",
            Buckets: prometheus.ExponentialBuckets(0.1, 2, 10),
        },
        []string{"provider"},
    )
)
```

在相应的接口处理函数中使用这些指标：

```go
// 在订单创建处理函数中
func (h *OrderHandler) Create(c echo.Context) error {
    start := time.Now()
    defer func() {
        duration := time.Since(start).Seconds()
        monitoring.OrderCreationDuration.Observe(duration)
    }()
    
    // 订单创建逻辑...
    
    return c.JSON(http.StatusCreated, order)
}

// 在eSIM激活处理函数中
func (h *ESIMHandler) Activate(c echo.Context) error {
    provider := c.QueryParam("provider")
    start := time.Now()
    defer func() {
        duration := time.Since(start).Seconds()
        monitoring.ESIMActivationDuration.WithLabelValues(provider).Observe(duration)
    }()
    
    // eSIM激活逻辑...
    
    return c.JSON(http.StatusOK, response)
}
```

### 3.3 分步骤追踪接口性能

对于复杂接口，可以细分不同处理阶段的性能：

```go
// 定义分阶段指标
var (
    APIProcessingStepDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "esim_api_processing_step_duration_seconds",
            Help:    "Duration of each processing step in API handlers",
            Buckets: prometheus.ExponentialBuckets(0.001, 2, 12),
        },
        []string{"handler", "step"},
    )
)

// 在复杂处理函数中使用
func (h *ComplexAPIHandler) Process(c echo.Context) error {
    handlerName := "ComplexAPIHandler.Process"
    
    // 第一阶段：参数验证
    validateStart := time.Now()
    // 验证参数...
    validateDuration := time.Since(validateStart).Seconds()
    monitoring.APIProcessingStepDuration.WithLabelValues(
        handlerName, "validate_params",
    ).Observe(validateDuration)
    
    // 第二阶段：数据库操作
    dbStart := time.Now()
    // 数据库操作...
    dbDuration := time.Since(dbStart).Seconds()
    monitoring.APIProcessingStepDuration.WithLabelValues(
        handlerName, "database_operations",
    ).Observe(dbDuration)
    
    // 第三阶段：第三方API调用
    apiStart := time.Now()
    // 调用第三方API...
    apiDuration := time.Since(apiStart).Seconds()
    monitoring.APIProcessingStepDuration.WithLabelValues(
        handlerName, "third_party_api",
    ).Observe(apiDuration)
    
    // 第四阶段：响应组装
    responseStart := time.Now()
    // 组装响应...
    responseDuration := time.Since(responseStart).Seconds()
    monitoring.APIProcessingStepDuration.WithLabelValues(
        handlerName, "response_assembly",
    ).Observe(responseDuration)
    
    return c.JSON(http.StatusOK, response)
}
```

## 4. 数据库查询监控

### 4.1 数据库查询指标定义

```go
var (
    // 数据库查询持续时间
    DBQueryDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "esim_db_query_duration_seconds",
            Help:    "Database query duration in seconds",
            Buckets: prometheus.ExponentialBuckets(0.001, 2, 12),
        },
        []string{"operation", "entity"},
    )
    
    // 数据库查询错误计数
    DBQueryErrorsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_db_query_errors_total",
            Help: "Total number of database query errors",
        },
        []string{"operation", "entity", "error_type"},
    )
)
```

### 4.2 数据库操作装饰器实现

使用装饰器模式来监控所有数据库操作：

```go
// DBMetricsDecorator 数据库仓储层装饰器
type DBMetricsDecorator struct {
    next Repository // 被装饰的仓储接口
}

// 以User仓储为例
func (d *DBMetricsDecorator) FindByID(ctx context.Context, id string) (*models.User, error) {
    start := time.Now()
    entity := "user"
    operation := "find_by_id"
    
    user, err := d.next.FindByID(ctx, id)
    
    duration := time.Since(start).Seconds()
    monitoring.DBQueryDuration.WithLabelValues(operation, entity).Observe(duration)
    
    if err != nil {
        errorType := "unknown"
        if errors.Is(err, sql.ErrNoRows) {
            errorType = "not_found"
        } else if errors.Is(err, context.DeadlineExceeded) {
            errorType = "timeout"
        }
        
        monitoring.DBQueryErrorsTotal.WithLabelValues(
            operation, entity, errorType,
        ).Inc()
    }
    
    return user, err
}

// 其他仓储方法类似实现...
```

## 5. 缓存性能监控

### 5.1 缓存指标定义

```go
var (
    // 缓存命中计数
    CacheHitsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_cache_hits_total",
            Help: "Total number of cache hits",
        },
        []string{"cache", "key_pattern"},
    )
    
    // 缓存未命中计数
    CacheMissesTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_cache_misses_total",
            Help: "Total number of cache misses",
        },
        []string{"cache", "key_pattern"},
    )
    
    // 缓存错误计数
    CacheErrorsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_cache_errors_total",
            Help: "Total number of cache operation errors",
        },
        []string{"cache", "operation", "error_type"},
    )
    
    // 缓存操作持续时间
    CacheOperationDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "esim_cache_operation_duration_seconds",
            Help:    "Cache operation duration in seconds",
            Buckets: prometheus.ExponentialBuckets(0.0001, 2, 12),
        },
        []string{"cache", "operation"},
    )
)
```

### 5.2 缓存服务实现示例

```go
// 缓存装饰器示例
type CacheMetricsDecorator struct {
    next CacheService
}

func (d *CacheMetricsDecorator) Get(ctx context.Context, key string) (interface{}, bool, error) {
    // 规范化缓存键模式
    keyPattern := extractKeyPattern(key)
    cache := "memory" // 或 "redis" 取决于实际使用的缓存
    
    start := time.Now()
    value, found, err := d.next.Get(ctx, key)
    duration := time.Since(start).Seconds()
    
    monitoring.CacheOperationDuration.WithLabelValues(cache, "get").Observe(duration)
    
    if err != nil {
        errorType := "unknown"
        if errors.Is(err, context.DeadlineExceeded) {
            errorType = "timeout"
        } else if errors.Is(err, redis.Nil) {
            errorType = "not_found"
        }
        
        monitoring.CacheErrorsTotal.WithLabelValues(
            cache, "get", errorType,
        ).Inc()
        
        return value, found, err
    }
    
    if found {
        monitoring.CacheHitsTotal.WithLabelValues(cache, keyPattern).Inc()
    } else {
        monitoring.CacheMissesTotal.WithLabelValues(cache, keyPattern).Inc()
    }
    
    return value, found, nil
}

// 规范化缓存键模式
func extractKeyPattern(key string) string {
    // 例如将 "user:123" 转换为 "user:*"
    parts := strings.Split(key, ":")
    if len(parts) > 1 {
        return parts[0] + ":*"
    }
    return key
}
```

## 6. 异步任务监控

### 6.1 异步任务指标定义

```go
var (
    // 异步任务计数
    AsyncTasksTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_async_tasks_total",
            Help: "Total number of async tasks",
        },
        []string{"task_type", "status"},
    )
    
    // 异步任务持续时间
    AsyncTaskDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "esim_async_task_duration_seconds",
            Help:    "Async task processing duration in seconds",
            Buckets: prometheus.ExponentialBuckets(0.01, 2, 15),
        },
        []string{"task_type"},
    )
    
    // 异步任务队列长度
    AsyncTaskQueueLength = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "esim_async_task_queue_length",
            Help: "Current length of async task queues",
        },
        []string{"queue"},
    )
    
    // 异步任务重试计数
    AsyncTaskRetriesTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_async_task_retries_total",
            Help: "Total number of async task retry attempts",
        },
        []string{"task_type"},
    )
)
```

### 6.2 异步任务处理器实现

```go
// 任务执行装饰器
func TaskExecutorWithMetrics(executor TaskExecutor) TaskExecutor {
    return func(ctx context.Context, task *Task) error {
        // 记录任务开始执行
        monitoring.AsyncTasksTotal.WithLabelValues(task.Type, "processing").Inc()
        
        start := time.Now()
        err := executor(ctx, task)
        duration := time.Since(start).Seconds()
        
        monitoring.AsyncTaskDuration.WithLabelValues(task.Type).Observe(duration)
        
        if err != nil {
            // 记录失败状态
            monitoring.AsyncTasksTotal.WithLabelValues(task.Type, "failed").Inc()
            
            // 如果需要重试
            if task.Retries < task.MaxRetries {
                monitoring.AsyncTaskRetriesTotal.WithLabelValues(task.Type).Inc()
            }
        } else {
            // 记录成功状态
            monitoring.AsyncTasksTotal.WithLabelValues(task.Type, "completed").Inc()
        }
        
        return err
    }
}

// 队列监控定时任务
func monitorTaskQueues() {
    for queue, length := range getQueueLengths() {
        monitoring.AsyncTaskQueueLength.WithLabelValues(queue).Set(float64(length))
    }
}
```

## 7. 自定义业务指标

### 7.1 业务指标定义

```go
var (
    // eSIM激活计数
    ESIMActivationsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_activations_total",
            Help: "Total number of eSIM activations",
        },
        []string{"provider", "status", "country"},
    )
    
    // 活跃eSIM数量
    ActiveESIMsGauge = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "esim_active_cards",
            Help: "Current number of active eSIM cards",
        },
        []string{"provider", "plan_type"},
    )
    
    // 订单计数
    OrdersTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_orders_total",
            Help: "Total number of orders",
        },
        []string{"status", "plan_type", "payment_method"},
    )
    
    // 用户注册计数
    UserRegistrationsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_user_registrations_total",
            Help: "Total number of user registrations",
        },
        []string{"source", "user_type"},
    )
    
    // 付款金额总计
    PaymentAmountTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "esim_payment_amount_total",
            Help: "Total payment amount",
        },
        []string{"currency", "payment_method", "plan_type"},
    )
)
```

### 7.2 业务指标使用示例

```go
// 在eSIM激活业务逻辑中
func (s *ESIMService) Activate(ctx context.Context, req *ActivationRequest) (*ActivationResponse, error) {
    // ... 激活逻辑
    
    // 记录激活事件
    monitoring.ESIMActivationsTotal.WithLabelValues(
        req.Provider, 
        activationStatus, 
        req.Country,
    ).Inc()
    
    // 如果激活成功，增加活跃eSIM计数
    if activationStatus == "success" {
        monitoring.ActiveESIMsGauge.WithLabelValues(
            req.Provider,
            req.PlanType,
        ).Inc()
    }
    
    return resp, err
}

// 在订单创建业务逻辑中
func (s *OrderService) CreateOrder(ctx context.Context, req *OrderRequest) (*Order, error) {
    // ... 订单创建逻辑
    
    // 记录订单
    monitoring.OrdersTotal.WithLabelValues(
        "created",
        req.PlanType,
        req.PaymentMethod,
    ).Inc()
    
    // 记录支付金额
    monitoring.PaymentAmountTotal.WithLabelValues(
        req.Currency,
        req.PaymentMethod,
        req.PlanType,
    ).Add(req.Amount)
    
    return order, nil
}

// 在用户注册业务逻辑中
func (s *UserService) Register(ctx context.Context, req *RegistrationRequest) (*User, error) {
    // ... 用户注册逻辑
    
    // 记录用户注册
    monitoring.UserRegistrationsTotal.WithLabelValues(
        req.Source,
        req.UserType,
    ).Inc()
    
    return user, nil
}
```

## 8. 查询示例

以下是一些有用的Prometheus查询示例，可用于监控接口性能：

### 8.1 HTTP请求相关查询

```
# 每分钟请求速率
rate(esim_http_requests_total[1m])

# 按路径的请求速率
sum(rate(esim_http_requests_total[5m])) by (path)

# 按状态码分组的请求百分比
sum(rate(esim_http_requests_total[5m])) by (status) / sum(rate(esim_http_requests_total[5m]))

# HTTP请求P95响应时间（按路径）
histogram_quantile(0.95, sum(rate(esim_http_request_duration_seconds_bucket[5m])) by (path, le))

# 最慢的5个API端点（按P95响应时间）
topk(5, histogram_quantile(0.95, sum(rate(esim_http_request_duration_seconds_bucket[5m])) by (path, le)))

# 按处理阶段的API请求时间分布
sum(rate(esim_api_processing_step_duration_seconds_sum[5m])) by (handler, step) / sum(rate(esim_api_processing_step_duration_seconds_count[5m])) by (handler, step)
```

### 8.2 数据库查询相关查询

```
# 数据库查询P95响应时间（按操作和实体）
histogram_quantile(0.95, sum(rate(esim_db_query_duration_seconds_bucket[5m])) by (operation, entity, le))

# 数据库错误率
sum(rate(esim_db_query_errors_total[5m])) / sum(rate(esim_db_query_duration_seconds_count[5m]))

# 最慢的数据库操作
topk(5, sum(rate(esim_db_query_duration_seconds_sum[5m])) by (operation, entity) / sum(rate(esim_db_query_duration_seconds_count[5m])) by (operation, entity))
```

### 8.3 缓存性能查询

```
# 缓存命中率
sum(rate(esim_cache_hits_total[5m])) / (sum(rate(esim_cache_hits_total[5m])) + sum(rate(esim_cache_misses_total[5m])))

# 按缓存类型的命中率
sum(rate(esim_cache_hits_total[5m])) by (cache) / (sum(rate(esim_cache_hits_total[5m])) by (cache) + sum(rate(esim_cache_misses_total[5m])) by (cache))

# 按键模式的缓存命中率
sum(rate(esim_cache_hits_total[5m])) by (key_pattern) / (sum(rate(esim_cache_hits_total[5m])) by (key_pattern) + sum(rate(esim_cache_misses_total[5m])) by (key_pattern))
```

### 8.4 业务指标查询

```
# eSIM激活成功率
sum(rate(esim_activations_total{status="success"}[1h])) / sum(rate(esim_activations_total[1h]))

# 按提供商的eSIM激活趋势
sum(rate(esim_activations_total[1h])) by (provider)

# 当前活跃eSIM数量
sum(esim_active_cards) by (provider)

# 按计划类型的订单量趋势
sum(rate(esim_orders_total[1d])) by (plan_type)

# 在给定时间段内的总支付金额（按货币）
sum(increase(esim_payment_amount_total[7d])) by (currency)
```

## 9. 最佳实践

### 9.1 命名和标签规范

- 使用`esim_`前缀保持指标命名一致性
- 遵循Prometheus命名规范：小写字母，下划线分隔单词
- 为counter类型指标名称添加`_total`后缀
- 为持续时间指标添加`_seconds`单位后缀
- 使用有意义且一致的标签

### 9.2 性能注意事项

- 避免高基数标签（如用户ID、会话ID）
- 对路径进行规范化，去除动态ID部分
- 使用合理数量的直方图桶
- 考虑采样率降低高流量接口的监控开销

### 9.3 监控覆盖清单

确保监控以下关键区域：

- 所有公开API端点的请求量和响应时间
- 关键内部操作的处理时间（如订单处理、eSIM激活）
- 数据库查询性能
- 缓存命中率和性能
- 异步任务处理状态和性能
- 关键业务指标（如注册率、激活率、订单量）
- 第三方API调用的性能和错误率
- 系统资源使用情况（内存、CPU、连接数）

### 9.4 告警建议

基于收集的指标设置合理的告警规则：

- HTTP请求错误率超过5%
- P95响应时间超过预设阈值
- 数据库查询错误率异常上升
- 缓存命中率显著下降
- 特定关键接口响应时间增加
- 异步任务队列积压或处理失败率上升
- 业务指标异常（如激活成功率下降）

### 9.5 仪表盘组织建议

组织Grafana仪表盘，按以下方式排列面板：

1. 系统概览（请求量、错误率、响应时间总览）
2. API性能详情（按端点的详细性能指标）
3. 数据库性能监控
4. 缓存性能监控
5. 异步任务监控
6. 业务指标仪表盘
7. 资源使用情况
8. 告警状态概览 