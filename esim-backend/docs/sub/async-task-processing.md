# 异步任务处理系统

## 概述

AuraESIM平台实现了健壮的异步任务处理系统，用于处理长时间运行的操作、后台任务和需要重试的关键操作。该系统解耦了任务提交和执行，提高了系统的可靠性、可扩展性和用户体验。

## 系统架构

### 核心组件

1. **任务管理器（TaskManager）**：负责任务调度、执行和监控的核心组件
2. **任务处理器（TaskProcessor）**：实际执行特定类型任务的组件
3. **任务追踪器（TaskTracker）**：负责存储和检索任务状态的接口
4. **任务队列**：存储待处理任务的先进先出队列

### 工作流程

1. 客户端提交任务，获取唯一任务ID
2. 任务被添加到队列并标记为"待处理"状态
3. 工作线程从队列中获取任务并处理
4. 任务状态实时更新（处理中、已完成、失败等）
5. 客户端可通过任务ID查询任务状态和结果

## 任务生命周期

任务在系统中的生命周期包括以下状态：

- **待处理（Pending）**：任务已提交但尚未开始处理
- **处理中（Processing）**：任务正在被工作线程处理
- **重试中（Retrying）**：任务处理失败，等待重试
- **已完成（Completed）**：任务成功完成
- **已失败（Failed）**：任务最终失败，达到最大重试次数

## 系统特性

### 自动重试机制

当任务处理失败时，系统会：

1. 记录失败原因
2. 递增重试计数
3. 计算下次重试时间（使用指数退避策略）
4. 将任务状态更新为"重试中"

系统默认配置：
- 最大重试次数：3次
- 重试延迟：5秒、30秒、2分钟

### 任务优先级

任务可分配优先级，高优先级任务会被优先处理。这通过多级队列实现：

- 高优先级队列（critical）：关键业务流程任务
- 中优先级队列（normal）：标准任务
- 低优先级队列（low）：后台维护任务

### 分布式任务处理

系统支持在多个服务实例之间分布式处理任务：

1. 使用Redis作为共享任务队列和状态存储
2. 通过领导者选举机制确保调度器只在一个节点上运行
3. 工作线程可在多个节点上并行处理任务

### 任务状态追踪和查询

系统提供全面的任务状态追踪：

1. 实时状态更新
2. 详细的任务元数据（提交时间、重试次数等）
3. 任务结果或错误信息存储
4. 支持通过ID查询单个任务状态
5. 支持按状态或类型批量查询任务

### 任务超时处理

为防止任务无限期运行，系统实现了超时机制：

1. 任务可设置最大执行时间
2. 超过时限的任务会被标记为失败
3. 根据任务策略决定是否重试超时任务

### 任务清理

为防止任务数据无限增长，系统会定期清理：

1. 默认保留已完成/失败任务7天
2. 每小时运行一次清理任务
3. 清理策略可通过配置调整

## 使用方法

### 注册任务处理器

所有任务类型都需要先注册对应的处理器：

```go
// 定义处理器
type EmailSender struct {
    // ...
}

// 实现TaskProcessor接口
func (s *EmailSender) ProcessTask(ctx context.Context, task *esim.TaskStatus) (interface{}, error) {
    // 实现发送邮件的逻辑
    // ...
    return map[string]string{"status": "sent"}, nil
}

// 注册处理器
taskManager.RegisterProcessor("send_email", &EmailSender{})
```

### 提交任务

提交任务到系统执行：

```go
// 准备任务数据
emailData := map[string]string{
    "to": "<EMAIL>",
    "subject": "Welcome to AuraESIM",
    "body": "Thank you for registering...",
}

// 提交任务，获取任务ID
taskID, err := taskManager.QueueTask(
    ctx,
    "send_email",    // 任务类型
    "user_123",      // 参考ID（可选）
    emailData,       // 任务数据
)
```

### 查询任务状态

查询任务状态和结果：

```go
// 获取任务状态
taskStatus, err := taskManager.GetTaskStatus(ctx, taskID)
if err != nil {
    // 处理错误
}

// 检查任务状态
switch taskStatus.Status {
case esim.TaskStatusCompleted:
    // 任务已完成，处理结果
    fmt.Printf("Task completed with result: %v\n", taskStatus.Result)
case esim.TaskStatusFailed:
    // 任务失败，获取错误信息
    fmt.Printf("Task failed: %v\n", taskStatus.Metadata["last_error"])
default:
    // 任务仍在处理中
    fmt.Printf("Task is in status: %s\n", taskStatus.Status)
}
```

### 等待任务完成

等待任务完成（带超时）：

```go
// 等待任务完成，最多等待60秒
taskStatus, err := taskManager.WaitForTask(ctx, taskID, 60*time.Second)
if err != nil {
    // 处理超时或其他错误
}

// 处理任务结果
if taskStatus.Status == esim.TaskStatusCompleted {
    // 使用任务结果
}
```

## 配置选项

任务管理器可通过以下选项配置：

```go
// 自定义配置
opts := &async.TaskManagerOptions{
    Workers:     10,               // 工作线程数
    MaxRetries:  5,                // 最大重试次数
    RetryDelays: []time.Duration{  // 重试延迟时间
        10 * time.Second,
        1 * time.Minute,
        5 * time.Minute,
        15 * time.Minute,
        30 * time.Minute,
    },
}

// 创建任务管理器
taskManager := async.NewTaskManager(taskTracker, redisClient, opts)
```

## 监控指标

系统自动收集以下Prometheus指标：

- `esim_async_tasks_total`：按类型和状态统计的任务总数
- `esim_async_task_duration_seconds`：任务执行时间直方图

这些指标可用于：
- 监控任务处理吞吐量
- 跟踪失败率
- 分析任务执行时间分布
- 识别需要优化的慢任务

## 运维考虑

### 扩展性

要提高系统吞吐量：
- 增加工作线程数（Workers参数）
- 部署更多服务实例以分担负载
- 考虑使用专用的任务处理节点

### 资源管理

- 监控Redis内存使用情况
- 设置适当的任务数据过期时间
- 调整任务清理策略以平衡数据保留和存储需求

### 故障恢复

系统设计考虑了以下故障场景：

- **节点崩溃**：未完成的任务会被其他节点接管
- **处理失败**：自动重试机制处理暂时性失败
- **Redis故障**：应实施Redis高可用策略（哨兵或集群）

### 任务监控

- 设置告警以监控异常失败率
- 监控长时间运行的任务
- 跟踪队列长度，识别可能的积压

## 故障排除

### 常见问题

1. **任务卡在"处理中"状态**
   - 可能原因：处理器崩溃或节点故障
   - 解决方案：实现"卡死任务"检测和恢复机制

2. **任务失败率高**
   - 可能原因：外部服务问题、配置错误
   - 解决方案：检查日志、增加重试次数、添加断路器

3. **任务队列积压**
   - 可能原因：处理速度赶不上提交速度
   - 解决方案：增加工作线程、优化处理器、评估限流策略

### 调试技巧

- 使用`GetTaskStatus`查看任务详情，包括元数据和错误信息
- 检查Redis中的任务数据完整性
- 查看任务相关的日志和指标

## 最佳实践

1. **合理设计任务粒度**
   - 任务应该足够小，可在合理时间内完成
   - 复杂工作流程应拆分为多个相关任务

2. **正确处理幂等性**
   - 任务处理器应确保多次执行相同任务不会导致意外结果
   - 使用唯一引用ID避免任务重复提交

3. **处理并发和竞态**
   - 关联任务间使用锁或事务确保数据一致性
   - 注意共享资源的并发访问

4. **适当设置重试策略**
   - 为不同类型的任务设置适当的重试次数和延迟
   - 考虑任务的紧急性和失败影响

## 小结

AuraESIM的异步任务处理系统提供了强大、灵活的框架，用于处理各种后台操作和长时间运行的任务。通过自动重试、任务追踪和分布式处理等功能，系统确保了高可靠性和可扩展性，同时提供了全面的监控和管理能力。 