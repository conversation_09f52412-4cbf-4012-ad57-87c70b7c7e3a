# 数据库查询优化

## 概述

AuraESIM平台实现了全面的数据库查询优化策略，通过多种技术提高查询性能、降低数据库负载并优化系统整体响应时间。这些优化针对PostgreSQL数据库特性进行了定制，并与平台的业务需求紧密结合。

## 核心优化技术

### 查询装饰器模式

系统采用"装饰器模式"封装数据库查询，提供了一系列优化和增强功能：

```go
// QueryDecorator 查询装饰器，用于支持查询跟踪、缓存和指标收集
type QueryDecorator struct {
    DB *gorm.DB
}
```

这种模式使得我们可以在不修改现有代码的情况下，为数据库查询添加性能跟踪、缓存、超时控制等功能。

### 查询性能跟踪

系统自动收集并记录所有数据库查询的执行时间和相关指标：

```go
callbacks.Query().After("gorm:query").Register("tracing:after_query", func(db *gorm.DB) {
    startTime, ok := db.InstanceGet("start_time")
    if !ok {
        return
    }
    duration := time.Since(startTime.(time.Time)).Seconds()
    monitoring.DatabaseQueryDuration.WithLabelValues("query", table).Observe(duration)
})
```

这些指标通过Prometheus收集，可用于识别慢查询、监控性能趋势和指导优化工作。

### 查询超时机制

为防止长时间运行的查询阻塞系统，实现了上下文超时控制：

```go
// 设置查询超时
timeout, cancel := context.WithTimeout(ctx, 5*time.Second)
defer cancel()

return tx.WithContext(timeout)
```

这确保了即使在高负载情况下，系统也能保持响应，避免资源耗尽。

### 查询索引提示

系统支持通过`OptimizationHints`结构体提供索引使用提示：

```go
// OptimizationHints 用于存储查询优化提示
type OptimizationHints struct {
    ForceIndex       string   // 强制使用索引
    IgnoreIndex      []string // 忽略索引
    MaxExecutionTime int      // 最大执行时间（毫秒）
    UseReplica       bool     // 使用副本
}
```

这使得开发人员可以根据具体查询特性和数据分布情况，提供精确的索引使用指导。

### 查询缓存集成

系统与缓存层紧密集成，提供了便捷的缓存查询功能：

```go
// FindWithCache 使用缓存的查询
func FindWithCache(ctx context.Context, cacheProvider CacheProvider, entity interface{}, cacheKey string, cacheTTL time.Duration, query func() error) error {
    // 尝试从缓存获取，命中则直接返回
    // 未命中则执行查询并更新缓存
}
```

这大大减少了对数据库的重复查询，提高了系统吞吐量。

## 具体优化策略

### 连接池优化

系统通过精细配置数据库连接池参数，平衡资源使用和性能需求：

```go
sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)  // 最大连接数
sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)  // 最大空闲连接数
sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)  // 连接最大生命周期
sqlDB.SetConnMaxIdleTime(30 * time.Minute)  // 连接最大空闲时间
```

这些参数可通过配置文件调整，以适应不同的部署环境和负载特性。

### 针对性查询优化

系统为不同类型的查询操作提供了专门的优化策略：

```go
// optimizedForFind 为查找操作优化查询
func (d *QueryDecorator) optimizedForFind(ctx context.Context, table string) *gorm.DB {
    // 查询优化逻辑...
}

// optimizedForCount 为计数操作优化查询
func (d *QueryDecorator) optimizedForCount(ctx context.Context, table string) *gorm.DB {
    // 计数查询优化逻辑...
}
```

这些优化基于各类操作的特点，应用最适合的策略。

### 事务管理

系统提供了便捷的事务管理工具，确保数据一致性的同时最小化锁定时间：

```go
// Transaction 提供事务支持的工具函数，支持指标收集
func (d *QueryDecorator) Transaction(ctx context.Context, txOpts *sql.TxOptions, fn func(tx *gorm.DB) error) error {
    // 事务执行与性能跟踪...
}
```

这种模式简化了事务处理代码，同时保持了性能监控能力。

### 分页查询优化

系统对分页查询进行了特别优化，提高大数据集浏览效率：

```go
// 分页查询示例
offset := (page - 1) * pageSize
result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
```

结合适当的索引，这种方式可显著提高分页查询性能。

## 索引策略

### 主键索引

所有表都使用UUID或自增ID作为主键，确保高效的记录查找和关联。

### 外键索引

系统自动为外键列创建索引，优化关联查询性能。

### 复合索引

根据查询模式创建复合索引，例如：

```sql
CREATE INDEX idx_esim_status_expiry ON esim (status, expiry_time);
```

这类索引针对特定查询模式（如查找特定状态且即将过期的eSIM）进行了优化。

### 部分索引

对于具有高度选择性的查询条件，系统使用部分索引：

```sql
CREATE INDEX idx_order_completed ON order (created_at) WHERE status = 'completed';
```

这类索引仅包含满足特定条件的记录，大幅减少索引大小并提高性能。

## 查询建模与优化

### 提前条件过滤

系统在查询设计中优先应用高选择性的过滤条件：

```go
query := r.db.WithContext(ctx).Where("status = ? AND start_date <= ? AND end_date > ?",
    string(promotion.StatusActive), now, now)
```

这种设计确保查询尽早减少需要处理的记录数量。

### 优化关联加载

系统使用GORM的预加载功能，在适当情况下一次性加载关联数据：

```go
result := query.Preload("Items").Preload("ESIMs").Offset(offset).Limit(pageSize).Find(&models)
```

这减少了N+1查询问题，提高了复杂数据结构的加载效率。

### 定制SQL表达式

在特定场景下，系统使用原生SQL表达式实现复杂的查询逻辑：

```go
orderClause := fmt.Sprintf("CASE WHEN country = '%s' THEN 0 ELSE 1 END", country)
result := query.Order(orderClause).First(&model)
```

这类优化充分利用了数据库引擎的能力，实现了应用层难以表达的查询逻辑。

## 监控与分析

### 查询性能指标

系统自动收集以下性能指标：

- `esim_db_query_duration_seconds`：按操作类型和表分类的查询耗时直方图
- 各种事务和查询计数指标

### 慢查询识别

通过监控指标，可识别并优化以下类型的慢查询：

- 执行时间超过100ms的查询
- 访问记录数超过1000的查询
- 执行频率高但缓存命中率低的查询

### 性能分析工具

系统支持以下数据库性能分析方法：

- 使用PostgreSQL的EXPLAIN ANALYZE分析执行计划
- 通过查询日志监控SQL性能
- 定期索引使用情况分析

## 使用指南

### 基本使用

```go
// 创建查询装饰器
queryDecorator := database.NewQueryDecorator(db)

// 使用装饰器执行优化查询
result := queryDecorator.optimizedForFind(ctx, "user").
    Where("email = ?", email).
    First(&user)
```

### 添加查询提示

```go
// 创建优化提示
hints := &database.OptimizationHints{
    ForceIndex: "idx_user_email",
    MaxExecutionTime: 1000, // 1秒
}

// 应用提示到查询
query := database.ApplyHints(db, hints).
    Where("status = ?", "active")
```

### 使用查询缓存

```go
// 带缓存的查询
var user User
err := database.FindWithCache(
    ctx,
    cache,           // 缓存提供者
    &user,           // 目标实体
    "user:123",      // 缓存键
    30*time.Minute,  // 缓存TTL
    func() error {   // 查询函数
        return db.Where("id = ?", 123).First(&user).Error
    },
)
```

## 运维建议

### 数据库维护

1. **定期VACUUM**：执行VACUUM ANALYZE以回收空间并更新统计信息
2. **索引维护**：定期重建碎片化的索引
3. **统计信息更新**：确保PostgreSQL的统计收集器正确配置

### 监控设置

1. **设置告警阈值**：
   - 查询响应时间超过500ms
   - 缓存命中率低于60%
   - 数据库连接使用率超过80%

2. **关键指标看板**：
   - 查询响应时间趋势
   - 慢查询频率
   - 索引使用情况
   - 数据库负载

### 性能诊断

当发现查询性能问题时：

1. 使用`EXPLAIN (ANALYZE, BUFFERS)`分析查询计划
2. 检查索引使用情况
3. 检查是否存在表扫描或低效的连接
4. 验证数据库统计信息是否最新
5. 考虑添加或修改索引

## 最佳实践

1. **频繁查询，较少更新的数据**：
   - 使用缓存+索引组合
   - 考虑物化视图

2. **大型数据集的分页查询**：
   - 使用"键集分页"而非OFFSET/LIMIT
   - 记录最后一个ID，使用`WHERE id > last_id LIMIT pageSize`

3. **复杂报表查询**：
   - 使用预聚合数据
   - 定期刷新统计数据表
   - 考虑只读副本

4. **高并发写入表**：
   - 最小化索引数量
   - 使用批量插入
   - 考虑分区表

## 小结

AuraESIM的数据库查询优化策略通过多层次的优化手段，确保了在复杂业务场景下的高性能数据访问。结合缓存系统、查询跟踪和索引优化，平台能够处理高并发请求同时保持响应速度，从而支持业务的高效运转和未来扩展。 