# API限流保护

## 概述

AuraESIM平台实现了多层次的API限流保护机制，用于防止系统过载、抵御恶意请求、确保服务质量和公平分配资源。限流系统基于Redis实现分布式限流，支持按角色、路径和IP进行精细化控制，并集成了熔断器机制以提供额外的系统保护。

## 限流架构

### 核心组件

1. **RateLimiterMiddleware**：实现限流逻辑的Echo中间件
2. **Redis存储**：用于跟踪和统计请求计数的分布式存储
3. **CircuitBreaker**：熔断器机制，防止系统在高错误率时过载
4. **配置系统**：支持不同级别的限流规则配置

### 限流算法

系统实现了基于固定窗口的限流算法：

1. 每个限流键（由用户ID/IP+路径组成）关联一个计数器
2. 计数器在固定时间窗口（默认1分钟）内累计请求数
3. 当请求数超过配置的阈值时，拒绝新的请求
4. 时间窗口结束后，计数器自动重置

## 限流级别

### 全局限流

全局限流应用于所有API请求，基于客户端IP地址实现：

```go
// 添加全局速率限制（每分钟1000个请求）
e.Use(rateLimiterMiddleware.GlobalRateLimit(1000))
```

这是最基本的保护层，防止任何单一IP发送过多请求。

### 角色限流

系统根据用户角色设置不同的限流阈值，默认配置为：

- **管理员（Admin）**：每分钟300个请求
- **代理商（Reseller）**：每分钟200个请求
- **企业用户（Enterprise）**：每分钟200个请求
- **普通用户（User）**：每分钟100个请求

角色限流确保不同类型的用户获得与其需求和重要性相匹配的资源分配。

### 路径限流

对特定API路径设置独立的限流规则，尤其适用于资源密集型端点。默认配置包括：

- **注册接口**：`/api/v1/register` - 每分钟10个请求
- **登录接口**：`/api/v1/login` - 每分钟20个请求
- **Webhook接口**：`/api/v1/webhook/*` - 每分钟500个请求

路径限流保护特定的敏感或高负载API端点不受滥用。

## 熔断机制

除了限流，系统还实现了熔断器模式：

```go
// 添加熔断器（5个错误后触发熔断，熔断时间30秒）
e.Use(rateLimiterMiddleware.CircuitBreaker(5, 30*time.Second))
```

熔断器监控每个API路径的错误率，当检测到连续错误时：

1. 达到阈值（默认5个错误）后，路径被标记为"开路"状态
2. 在熔断期间（默认30秒），所有对该路径的请求立即被拒绝
3. 熔断期过后，系统尝试恢复服务，进入"半开"状态
4. 如果请求成功，恢复到"闭路"状态；如果继续失败，再次开路

熔断机制可有效防止系统在部分组件故障时被连锁反应拖垮。

## 实现细节

### 限流键生成

系统根据请求特征生成限流键：

- 已认证用户：`rate_limit:user:[用户ID]:[路径]`
- 未认证用户：`rate_limit:ip:[IP地址]:[路径]`

这种设计确保了限流的精确性和公平性。

### 限流逻辑

限流实现的核心逻辑如下：

```go
// 检查是否超过速率限制
func (m *RateLimiterMiddleware) checkRateLimit(ctx context.Context, key string, limit int) (bool, int, error) {
    // 获取当前计数
    countStr, err := m.redisClient.Get(ctx, key)
    if err == goredis.Nil {
        countStr = "0" // 键不存在，首次访问
    } else if err != nil {
        return false, 0, err
    }

    count, _ := strconv.Atoi(countStr)
    count++ // 增加计数

    // 保存计数，设置过期时间
    err = m.redisClient.Set(ctx, key, strconv.Itoa(count), m.config.Expiry)
    if err != nil {
        return false, count, err
    }

    // 判断是否超过限制
    return count > limit, count, nil
}
```

### 响应头

系统在响应中添加标准限流相关的HTTP头：

- `X-RateLimit-Limit`：当前限流阈值
- `X-RateLimit-Remaining`：剩余请求数
- `X-RateLimit-Reset`：限流窗口重置时间戳

这些头信息有助于客户端了解其限流状态并适当调整请求策略。

## 配置和定制

### 默认配置

系统提供了合理的默认配置：

```go
func DefaultRateLimiterConfig() *RateLimiterConfig {
    return &RateLimiterConfig{
        DefaultLimit: 60, // 默认每分钟60个请求
        RoleLimits: map[user.Role]int{
            user.RoleAdmin:      300,
            user.RoleReseller:   200,
            user.RoleEnterprise: 200,
            user.RoleUser:       100,
        },
        PathLimits: map[string]int{
            "/api/v1/register":  10,
            "/api/v1/login":     20,
            "/api/v1/webhook/*": 500,
        },
        Expiry: time.Minute, // 限制窗口为1分钟
    }
}
```

### 自定义配置

可以通过配置文件或代码方式调整限流规则：

```go
// 创建自定义配置
config := &middleware.RateLimiterConfig{
    DefaultLimit: 100,
    RoleLimits: map[user.Role]int{
        user.RoleAdmin: 500,
        // 其他角色...
    },
    PathLimits: map[string]int{
        "/api/v1/sensitive-operation": 5,
        // 其他路径...
    },
    Expiry: 2 * time.Minute, // 2分钟窗口
}

// 应用自定义配置
rateLimiterMiddleware := apimiddleware.NewRateLimiterMiddleware(redisClient, config, logger)
```

## 高级功能

### 路径模式匹配

系统支持使用通配符(`*`)匹配路径：

```go
// 匹配所有webhook路径
"/api/v1/webhook/*": 500
```

路径匹配算法如下：

```go
func pathMatch(pattern, path string) bool {
    // 如果模式以 * 结尾，则检查前缀
    if strings.HasSuffix(pattern, "*") {
        return strings.HasPrefix(path, strings.TrimSuffix(pattern, "*"))
    }
    // 否则精确匹配
    return pattern == path
}
```

### 熔断器状态管理

熔断器维护每个路径的状态：

- `errorCounts`：记录每个路径的错误计数
- `circuitOpen`：记录熔断开始时间
- 成功请求会逐渐减少错误计数，实现自动恢复机制

## 运维考虑

### Redis资源

限流系统主要依赖Redis进行计数器存储，需要注意：

1. **内存使用**：每个活跃用户+路径组合都会创建一个Redis键
2. **网络流量**：检查每个请求都需要Redis读写操作
3. **过期策略**：系统自动设置适当的过期时间，避免键积累

### 监控指标

建议监控以下指标：

1. **限流拒绝率**：`拒绝请求数/总请求数`
2. **熔断触发次数**：按路径统计的熔断频率
3. **Redis性能**：命令延迟、内存使用、连接数

### 故障处理

系统对Redis故障有优雅的降级机制：

```go
if err != nil {
    m.logger.Error("Rate limit check failed", 
        zap.Error(err), 
        zap.String("ip", clientIP),
        zap.String("path", path))
    // 发生错误时继续处理请求，不阻止用户
    return next(c)
}
```

在Redis不可用情况下，系统会记录错误并允许请求通过，优先保证服务可用性。

## 故障排除

### 常见限流问题

1. **误判为攻击**：
   - 检查是否是合法的高流量场景
   - 适当调整特定路径或角色的限流阈值

2. **限流不生效**：
   - 确认Redis配置正确
   - 检查中间件注册顺序
   - 验证路径模式是否正确匹配

3. **熔断器频繁触发**：
   - 排查相关服务故障
   - 考虑增加熔断阈值或缩短熔断时间

### 调试技巧

- 利用`X-RateLimit-*`响应头了解限流状态
- 检查Redis中的限流计数器键
- 在开发环境降低限流阈值便于测试

## 客户端策略

为避免客户端受到限流影响，建议以下实践：

1. **请求节流**：控制请求频率，避免突发流量
2. **指数退避**：遇到限流时进行指数退避重试
3. **批量处理**：尽可能合并多个操作到单个请求
4. **缓存响应**：缓存不频繁变化的数据，减少请求次数

## 最佳实践

1. **定期审查限流规则**：
   - 基于实际流量模式调整限流阈值
   - 添加新的敏感路径限流规则

2. **根据业务价值差异化限流**：
   - 为关键业务操作提供更高限额
   - 对非必要功能实施更严格的限制

3. **结合其他安全措施**：
   - 与认证、授权系统协同
   - 部署Web应用防火墙(WAF)作为第一道防线

4. **优雅处理限流响应**：
   - 客户端应正确解释429状态码
   - 实现重试逻辑和用户友好的错误提示

## 小结

AuraESIM的API限流保护系统通过多层次限流和熔断机制，为平台提供了全面的请求流量管理能力。这种设计在确保服务可靠性和可用性的同时，能够公平分配系统资源，并有效防御恶意攻击和突发流量带来的风险。 