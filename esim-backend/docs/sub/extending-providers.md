# 添加新的eSIM提供商集成指南

本文档介绍了如何在AuraESIM系统中添加新的eSIM提供商集成。系统设计为可扩展架构，允许轻松添加新的提供商。

## 步骤概览

1. 创建提供商专用包
2. 实现API客户端
3. 实现提供商接口
4. 注册提供商
5. 添加配置
6. 添加单元测试

## 详细步骤

### 1. 创建提供商专用包

在`internal/providers`目录下为新提供商创建一个子包：

```
internal/providers/
  ├── esimaccess/     # 现有
  ├── mayamobile/     # 现有
  └── newprovider/    # 新提供商
```

### 2. 实现API客户端

每个提供商应该有自己的API客户端实现，负责处理HTTP请求、认证、错误处理等。

```go
// newprovider/client.go
package newprovider

import (
    "context"
    "net/http"
    "time"
    
    "vereal/letsesim/pkg/esim"
)

// Client 是NewProvider API客户端
type Client struct {
    httpClient *http.Client
    config     *esim.ProviderConfig
    baseURL    string
}

// NewClient 创建新的API客户端
func NewClient(config *esim.ProviderConfig) *Client {
    client := &http.Client{
        Timeout: time.Duration(config.TimeoutSeconds) * time.Second,
    }
    
    return &Client{
        httpClient: client,
        config:     config,
        baseURL:    config.BaseURL,
    }
}

// 根据提供商API实现具体的方法
```

### 3. 实现数据模型

为提供商特定的请求和响应创建数据模型：

```go
// newprovider/models.go
package newprovider

import (
    "time"
)

// 定义请求结构

type ESIMCreateRequest struct {
    // 根据提供商API定义字段
}

// 定义响应结构

type ESIMResponse struct {
    // 根据提供商API定义字段
}

// 其他必要的结构体
```

### 4. 实现提供商接口

实现`ESIMProviderInterface`接口，处理转换逻辑：

```go
// newprovider/provider.go
package newprovider

import (
    "context"
    "time"
    
    "vereal/letsesim/pkg/esim"
    "go.uber.org/zap"
)

const ProviderTypeNewProvider = "new_provider"

// Provider 实现ESIMProvider接口
type Provider struct {
    client *Client
    config *esim.ProviderConfig
    logger *zap.Logger
}

// NewProvider 创建新的提供商实例
func NewProvider(configManager esim.ProviderConfigManagerInterface, logger *zap.Logger) (*Provider, error) {
    ctx := context.Background()
    config, err := configManager.GetConfig(ctx, ProviderTypeNewProvider)
    if err != nil {
        if logger != nil {
            logger.Error("Failed to get provider config", 
                zap.Error(err),
                zap.String("provider", ProviderTypeNewProvider))
        }
        return nil, err
    }
    
    client := NewClient(config)
    
    return &Provider{
        client: client,
        config: config,
        logger: logger,
    }, nil
}

// GetProviderInfo 获取提供商信息
func (p *Provider) GetProviderInfo(ctx context.Context) esim.ProviderInfo {
    return esim.ProviderInfo{
        Type:    ProviderTypeNewProvider,
        Name:    "New Provider Name",
        BaseURL: p.config.BaseURL,
        Capabilities: map[string]bool{
            // 根据提供商能力设置
            esim.CapSMS:     true,
            esim.CapTopUp:   true,
            esim.CapCancel:  true,
            esim.CapSuspend: true,
            esim.CapResume:  true,
        },
    }
}

// 实现其他ESIMProviderInterface接口方法
```

### 5. 在工厂和配置中注册提供商

更新`internal/providers/factory.go`添加新的提供商类型：

```go
// internal/providers/factory.go
const (
    ProviderTypeESIMAccess = "esim_access"
    ProviderTypeMayaMobile = "maya_mobile"
    ProviderTypeNewProvider = "new_provider" // 添加新类型
)
```

在`internal/providers/config.go`添加默认配置：

```go
// internal/providers/config.go

// InitializeDefaultConfigs 方法中添加
m.configs[ProviderTypeNewProvider] = &esim.ProviderConfig{
    Type:    ProviderTypeNewProvider,
    Enabled: true,
    BaseURL: "https://api.newprovider.com",
    WebhookConfig: esim.WebhookConfig{
        URL:          "",
        Secret:       "",
        ProviderType: ProviderTypeNewProvider,
        EventTypes:   []string{},
    },
    RateLimitPerSec: 10,
    TimeoutSeconds:  30,
    RetryConfig: esim.RetryConfig{
        MaxRetries:       3,
        RetryInterval:    time.Second * 1,
        MaxRetryInterval: time.Second * 5,
    },
    AdditionalConfig: map[string]interface{}{},
}
```

更新`internal/providers/provider_setup.go`注册提供商：

```go
// internal/providers/provider_setup.go

// SetupProviders 方法中添加
newProvider, err := NewNewProvider(configManager)
if err != nil {
    return err
}
if err := factory.RegisterProvider(ctx, newProvider); err != nil {
    return err
}

// 添加工厂方法
func NewNewProvider(configManager esim.ProviderConfigManagerInterface, logger *zap.Logger) (esim.ESIMProviderInterface, error) {
    return newprovider.NewProvider(configManager, logger)
}
```

### 6. 添加单元测试

为新提供商实现添加单元测试，确保正确处理API调用：

```go
// newprovider/provider_test.go
package newprovider

import (
    "context"
    "testing"
    "time"
    
    "vereal/letsesim/pkg/esim"
    
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
    "go.uber.org/zap"
)

// MockClient 是Client的模拟实现
type MockClient struct {
    mock.Mock
}

// 实现MockClient方法

// 测试用例
func TestGetProviderInfo(t *testing.T) {
    // 测试实现
}

func TestCreateESIM(t *testing.T) {
    // 测试实现
}

// 其他测试用例
```

## 最佳实践

1. **错误处理**：使用`esim.NewESIMError`创建结构化错误，包含错误代码、描述和上下文。

2. **日志记录**：使用提供的logger记录所有API调用的详细信息，特别是在出错时。

3. **字段映射**：创建明确的将提供商特定格式转换为通用格式的函数，如`ConvertToESIM`、`ConvertToPackage`等。

4. **配置验证**：在`NewProvider`中验证配置是否完整和有效。

5. **重试机制**：对关键API调用实现重试机制。

6. **单元测试**：为所有重要功能编写单元测试，使用mock模拟外部依赖。

7. **文档**：为新实现添加文档，说明支持的功能、限制和特殊考虑因素。

遵循这些步骤和最佳实践，您可以轻松地将新的eSIM提供商集成到AuraESIM系统中。 