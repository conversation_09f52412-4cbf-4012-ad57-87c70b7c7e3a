# 缓存策略优化

## 概述

AuraESIM平台实现了多级缓存架构，以提高系统性能和响应速度，同时减轻数据库负载。此架构结合了本地内存缓存和Redis分布式缓存的优势，为系统提供了高效、可扩展的缓存解决方案。

## 缓存架构

### 多级缓存设计

AuraESIM采用了两级缓存设计：

1. **本地内存缓存（Level 1）**：基于LRU（最近最少使用）算法的内存缓存，提供最快速的数据访问
2. **Redis分布式缓存（Level 2）**：提供跨服务实例的共享缓存层，支持更大的数据量和持久化

此设计遵循"缓存双写"和"缓存旁路"模式，确保数据一致性和高可用性。

### 缓存组件

系统包含以下缓存组件：

- **LocalCache**：基于golang-lru的本地内存缓存实现
- **RedisCache**：基于Redis的分布式缓存实现
- **HybridCache**：结合本地缓存和Redis缓存的混合缓存实现

## 关键特性

### 自动过期机制

- 支持为每个缓存项设置不同的TTL（存活时间）
- 默认TTL可在配置中全局设置
- 支持永久缓存（TTL为0）

### 缓存命中率监控

- 通过Prometheus指标自动记录缓存命中和未命中
- 按缓存类型分类统计（本地/Redis）
- 提供实时监控和历史趋势分析

### 批量删除能力

- 支持按模式（pattern）批量删除缓存
- 本地缓存使用前缀匹配模拟模式删除
- Redis缓存使用原生的KEYS命令实现模式匹配

### 缓存预热机制

- 系统启动时自动加载常用数据到缓存
- 支持按优先级预热不同类型的数据
- 避免系统启动后的"冷启动"问题

## 使用方法

### 基本使用

```go
// 1. 获取缓存实例（通常通过依赖注入提供）
cache := app.GetCache() // hybridCache实例

// 2. 从缓存获取数据
var user User
found, err := cache.Get(ctx, "user:123", &user)
if err != nil {
    // 处理错误
}
if found {
    // 使用缓存的用户数据
} else {
    // 从数据库获取用户数据并更新缓存
    user = getUserFromDB(123)
    cache.Set(ctx, "user:123", user, 30*time.Minute)
}
```

### 使用辅助函数

系统提供了`CachedFetcher`助手函数，简化缓存操作：

```go
var user User
err := cache.CachedFetcher(
    ctx,
    cache,
    "user:123",
    func() (interface{}, error) {
        // 当缓存未命中时执行
        return getUserFromDB(123)
    },
    &user,
    30*time.Minute,
)
```

### 生成缓存键

使用`PrefixedKeyGenerator`创建一致的缓存键：

```go
// 创建用户相关的缓存键生成器
userKeyGen := cache.PrefixedKeyGenerator("user")

// 生成带用户ID的缓存键
key := userKeyGen(123) // 结果为 "esim:user:123"
```

### 与数据库查询结合

使用`FindWithCache`函数简化带缓存的数据库查询：

```go
var user User
err := database.FindWithCache(
    ctx,
    cache,
    &user,
    "user:123",
    30*time.Minute,
    func() error {
        return db.Where("id = ?", 123).First(&user).Error
    },
)
```

## 配置参数

缓存系统可通过`Config`结构体配置：

```go
type Config struct {
    Enabled      bool          // 是否启用缓存
    DefaultTTL   time.Duration // 默认过期时间
    LocalMaxSize int           // 本地缓存最大项数
}
```

典型配置：

```go
cacheConfig := &cache.Config{
    Enabled:      true,
    DefaultTTL:   30 * time.Minute,
    LocalMaxSize: 10000,
}
```

## 运维考虑

### 内存管理

本地缓存使用LRU算法自动管理内存使用，但仍需注意：

- 为`LocalMaxSize`设置合理值，避免内存溢出
- 监控内存使用情况，根据负载调整缓存大小
- 对大型对象使用较短的TTL

### Redis配置

为Redis缓存优化性能：

- 配置适当的`maxmemory`和内存策略（推荐：volatile-lru）
- 启用持久化以防数据丢失（AOF或RDB）
- 在高负载环境中考虑Redis集群

### 监控与告警

重要的监控指标：

- 缓存命中率（通过`esim_cache_hits_total`/`esim_cache_misses_total`计算）
- Redis内存使用率
- Redis连接数
- 慢操作日志

建议告警配置：

- 缓存命中率低于70%
- Redis内存使用超过80%
- 缓存操作错误率上升

### 缓存失效策略

有几种常见的缓存失效策略：

- **定时过期**：系统自动应用TTL
- **主动失效**：在数据变更时主动删除相关缓存
- **批量失效**：使用`DeletePattern`在大规模数据变更时批量清除缓存

## 最佳实践

1. **使用正确的TTL**：为不同类型的数据设置合理的过期时间
   - 频繁变化的数据使用较短TTL
   - 静态数据可使用较长TTL

2. **一致的键命名**：使用`PrefixedKeyGenerator`确保一致的键命名规则

3. **处理缓存击穿**：
   - 对热点数据使用互斥锁防止缓存击穿
   - 实现简单的熔断机制，防止数据库过载

4. **定期审查缓存效率**：
   - 监控缓存命中率
   - 分析经常未命中的查询
   - 调整缓存策略和TTL

5. **控制缓存大小**：
   - 只缓存必要的数据
   - 避免缓存大量的冷数据
   - 定期清理过期和不常用的缓存

## 故障排除

### 常见问题

1. **缓存命中率低**
   - 检查TTL是否设置得太短
   - 验证缓存键生成是否一致
   - 确认预热机制正常运作

2. **Redis连接问题**
   - 检查网络连接
   - 验证认证设置
   - 检查Redis服务器状态

3. **内存使用过高**
   - 调整本地缓存大小
   - 减少缓存对象大小
   - 优化缓存键（避免过长的键名）

### 调试技巧

- 启用Redis慢日志分析缓慢操作
- 使用Redis监控命令（`MONITOR`）观察缓存操作
- 检查`info`命令输出以获取Redis性能信息

## 小结

AuraESIM的多级缓存架构通过结合本地缓存和分布式缓存的优势，提供了高性能、高可用性的数据访问层。通过合理配置和监控，可以显著提高系统响应速度，减轻数据库负载，提升整体用户体验。 