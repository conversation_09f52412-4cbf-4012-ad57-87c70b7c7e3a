# 系统监控与统计

## 概述

AuraESIM平台集成了基于Prometheus的全面监控系统，用于收集和分析各种系统指标，包括HTTP请求、数据库查询、缓存使用、第三方服务集成等关键性能指标。该监控系统对于了解系统运行状况、识别性能瓶颈、预测潜在问题以及辅助容量规划至关重要。

## 工作原理

### Prometheus指标收集

系统使用Prometheus客户端库实现了以下类型的指标收集：

1. **计数器（Counter）**：单调递增的计数器，用于记录事件总数，如请求数、错误数等
2. **仪表盘（Gauge）**：可上可下的指标，用于记录当前状态，如活跃连接数、内存使用量等
3. **直方图（Histogram）**：对数据分布进行采样，如请求持续时间、响应大小等
4. **摘要（Summary）**：类似直方图，但能计算滑动时间窗口内的分位数

### 指标端点

系统提供了`/metrics`端点，Prometheus服务器可以定期抓取该端点获取当前指标。

### 监控中间件

为了自动收集HTTP请求相关指标，系统实现了Echo框架中间件，对每个请求进行跟踪。

## 已实现的关键指标

### HTTP请求指标

- `esim_http_request_duration_seconds`：HTTP请求处理时间（直方图）
- `esim_http_requests_total`：HTTP请求总数（计数器）
- `esim_http_active_requests`：当前活跃的HTTP请求数（仪表盘）

### 数据库指标

- `esim_db_query_duration_seconds`：数据库查询耗时（直方图）

### 缓存指标

- `esim_cache_hits_total`：缓存命中次数（计数器）
- `esim_cache_misses_total`：缓存未命中次数（计数器）

### eSIM提供商指标

- `esim_provider_requests_total`：eSIM提供商请求总数（计数器）
- `esim_provider_request_duration_seconds`：eSIM提供商请求耗时（直方图）

### 异步任务指标

- `esim_async_tasks_total`：异步任务总数（计数器）
- `esim_async_task_duration_seconds`：异步任务处理耗时（直方图）

### 业务指标

- `esim_business_metrics_total`：业务相关指标（计数器）

## 健康检查

系统通过`/health`端点提供健康检查，返回系统当前状态。健康检查可用于以下场景：

- Kubernetes的liveness和readiness探针
- 负载均衡器的后端健康检查
- 监控系统的可用性检查

## 部署与配置

### Prometheus配置

以下是Prometheus服务器的基本配置示例：

```yaml
scrape_configs:
  - job_name: 'esim-api'
    scrape_interval: 15s
    metrics_path: /metrics
    static_configs:
      - targets: ['esim-api:8080']
```

### Grafana集成

可以将Prometheus作为数据源添加到Grafana，创建可视化仪表盘。建议的仪表盘包括：

1. **HTTP请求概览**：请求率、响应时间、错误率
2. **数据库性能**：查询时间分布、查询次数
3. **缓存效率**：命中率、未命中率
4. **提供商集成**：请求率、失败率、响应时间
5. **系统资源**：CPU、内存、网络使用情况

## 运维注意事项

### 存储要求

Prometheus数据量会随时间增长，需要规划足够的存储空间，并配置合理的数据保留策略。

### 告警设置

建议在Prometheus或Grafana中配置以下告警：

1. 高错误率：`rate(esim_http_requests_total{status=~"5.."}[5m]) / rate(esim_http_requests_total[5m]) > 0.05`
2. 响应时间过长：`histogram_quantile(0.95, rate(esim_http_request_duration_seconds_bucket[5m])) > 1`
3. 数据库查询缓慢：`histogram_quantile(0.95, rate(esim_db_query_duration_seconds_bucket[5m])) > 0.5`
4. 提供商请求失败率高：`rate(esim_provider_requests_total{status="failed"}[5m]) / rate(esim_provider_requests_total[5m]) > 0.1`

### 故障排查

通过指标快速定位问题：

1. 检查HTTP状态码分布，识别错误率上升
2. 分析请求持续时间，找出瓶颈
3. 查看数据库查询性能，识别缓慢的查询
4. 检查第三方集成性能，识别潜在问题

## 扩展与定制

### 添加自定义业务指标

可以通过调用`monitoring.RecordBusinessMetric`函数添加任何业务相关指标：

```go
monitoring.RecordBusinessMetric("new_users", 1)
monitoring.RecordBusinessMetric("orders_completed", float64(orderCount))
```

### 添加新的监控维度

如需添加新的指标类型，可在`pkg/monitoring/metrics.go`中注册新的Prometheus指标。

## 最佳实践

1. **保持指标简单**：确保指标命名直观，易于理解
2. **选择正确的指标类型**：对于累计数值使用Counter，当前状态使用Gauge，分布数据使用Histogram
3. **添加有意义的标签**：使用标签区分不同维度，但避免基数过高
4. **定期审查指标使用情况**：删除不再使用的指标，避免资源浪费
5. **关注SLI/SLO**：基于指标定义服务水平指标(SLI)和目标(SLO)

## 小结

监控系统是AuraESIM平台的核心组成部分，通过精心设计的指标收集和可视化，可以确保系统的稳定性、可靠性，并为性能优化提供数据支持。通过持续监控，运维团队可以主动发现并解决问题，而不是被动响应故障。 