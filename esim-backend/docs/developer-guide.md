# eSIM聚合平台开发说明

## 项目概述

eSIM聚合平台是一个基于Go语言开发的后端服务系统，旨在集成多个eSIM提供商的API，提供统一的接口供客户使用。该平台采用Echo框架构建RESTful API，使用PostgreSQL作为数据库，Redis作为缓存，并通过Docker容器化部署。

### 技术栈

- 语言：Go（1.24.x）
- 框架：Echo（轻量级RESTful API框架）
- 数据库：PostgreSQL（使用GORM进行ORM和迁移）
- 缓存：Redis（用于高性能缓存）
- 容器：Docker（Docker Compose用于开发，单容器用于生产）
- 日志：Zap（结构化、高性能日志记录）
- 配置：Viper（环境变量和默认值）
- 依赖注入：基于接口的依赖注入模式
- 安全：JWT（认证和授权）
- 监控：Prometheus（指标收集和监控）
- 限流：Redis-based rate limiting

## 当前实现状态

目前，平台已经完成了基础架构的搭建，并集成了两个主要的eSIM提供商：eSIM Access和Maya Mobile。具体实现情况如下：

 1. **架构设计**：已完成项目的整体架构设计，包括目录结构、接口定义和数据模型设计。
 2. **提供商集成**：已集成eSIM Access和Maya Mobile两个提供商的API。
 3. **统一接口**：已定义并实现了统一的接口，用于封装不同提供商的API差异。
 4. **分层架构**：实现了完整的分层架构，包括API、服务层、仓储层和领域模型。
 5. **异步处理**：实现了异步处理机制，用于处理长时间运行的任务。
 6. **身份验证**：实现了基于JWT的身份验证系统。
 7. **用户管理**：实现了用户管理模块，包括注册、登录和权限控制。
 8. **订单管理**：实现了订单管理模块，用于跟踪和管理eSIM订购过程。
 9. **代理商系统**：实现了代理商管理模块，支持多级代理商结构。
10. **促销活动**：实现了促销活动管理，支持各种促销策略。
11. **积分系统**：实现了用户积分管理，支持积分充值和消费。
12. **企业管理**：实现了企业用户管理，支持部门和员工管理。
13. **费率管理**：实现了多级费率管理，支持全局、代理商和企业级别的费率设置。
14. **API限流**：实现了基于Redis的分布式API限流保护。
15. **系统监控**：集成了Prometheus监控，实现了关键指标的收集和监控。
16. **缓存优化**：实现了多级缓存策略，提高系统性能。
17. **数据库优化**：实现了查询优化和索引优化，提高数据库性能。

## 代码结构说明

项目遵循标准的Go项目结构，主要分为以下几个部分：

- `cmd/api/`：应用程序入口点
  - `main.go`：主应用程序入口
- `config/`：配置加载
  - `config.go`：Viper配置设置
- `internal/`：核心业务逻辑（不导出）
  - `api/`：API路由和处理器
    - `handlers/`：HTTP请求处理程序
    - `middleware/`：中间件函数
    - `router.go`：路由定义
  - `service/`：业务逻辑层
  - `repository/`：数据访问层
  - `domain/`：领域模型和业务规则
    - `user/`：用户相关模型和业务规则
    - `order/`：订单相关模型和业务规则
    - `reseller/`：代理商相关模型和业务规则
    - `promotion/`：促销相关模型和业务规则
    - `credit/`：积分相关模型和业务规则
    - `enterprise/`：企业相关模型和业务规则
    - `rate/`：费率相关模型和业务规则
    - `esim/`：eSIM相关模型和业务规则
  - `providers/`：eSIM提供商集成
    - `esimaccess/`：eSIM Access提供商实现
    - `mayamobile/`：Maya Mobile提供商实现
    - `factory.go`：提供商工厂，用于创建和管理提供商实例
    - `config.go`：提供商配置
    - `provider_setup.go`：提供商初始化和注册
  - `async/`：异步任务处理
  - `di/`：依赖注入
- `pkg/`：可重用包
- `migrations/`：数据库迁移脚本
- `docker/`：Docker相关配置
- `docs/`：项目文档

## 已集成的eSIM提供商

### 1\. eSIM Access

eSIM Access是一个全球eSIM提供商，支持多种数据套餐类型和多国家覆盖。其主要功能包括：

- 数据套餐查询
- eSIM订购
- 资料查询
- 资料状态管理（暂停、恢复、撤销）
- 余额查询
- eSIM充值
- SMS发送
- 使用情况查询

### 2\. Maya Mobile

Maya Mobile提供了一套完整的eSIM和数据计划管理API，主要功能包括：

- eSIM创建和管理
- 数据计划类型定义
- 数据计划管理（创建、刷新、删除）
- 网络列表查询
- 客户管理
- 账户余额查询

## 系统功能模块

### 用户管理

- 用户注册、登录和身份验证
- 用户权限控制
- 用户信息管理

#### JWT认证实现

本项目使用JWT令牌进行用户认证，主要实现包括：

1. **JWT令牌服务 (**`pkg/jwt/jwt.go`)

   - 生成JWT令牌：`GenerateToken`方法用于登录成功后生成包含用户ID、邮箱和角色信息的令牌
   - 验证JWT令牌：`ValidateToken`方法用于验证令牌的有效性并解析其中的用户信息
   - 令牌使用HS256算法签名，包含过期时间、签发时间等标准声明

2. **认证中间件 (**`internal/api/middleware/auth.go`)

   - 用户认证中间件 (`UserAuth`)：验证所有需要用户登录的路由
   - 管理员认证中间件 (`AdminAuth`)：在用户认证基础上添加管理员角色验证
   - 代理商API认证中间件 (`ResellerAPIAuth`)：针对代理商API的专用认证
   - 企业认证中间件 (`EnterpriseAuth`)：针对企业用户的专用认证

3. **用户登录流程**

   - 用户通过`/api/v1/login`接口提交邮箱和密码
   - 服务端验证凭据并生成JWT令牌
   - 令牌通过HTTP响应返回给客户端
   - 客户端在后续请求中通过`Authorization: Bearer <token>`头传递令牌
   - 认证中间件验证令牌并从中提取用户信息用于后续处理

4. **安全最佳实践**

   - 在生产环境中使用强随机密钥替换默认JWT密钥
   - 定期轮换JWT密钥
   - 令牌有效期设置合理时间
   - 敏感API通过HTTPS传输保护令牌
   - 考虑添加刷新令牌机制，避免频繁要求用户重新登录

### 订单管理

- 订单创建和处理
- 订单状态跟踪
- 订单历史查询
- 支付集成

### 代理商系统

- 代理商注册和管理
- 多级代理商结构
- 佣金计算和分配
- 代理商报表
- API密钥管理

### 企业管理系统

- 企业用户管理
- 部门管理
- 员工管理
- eSIM分配和回收
- 企业级权限控制

### 积分系统

- 积分充值和消费
- 积分交易历史记录
- 积分余额查询
- 代理商积分充值

### 费率管理

- 全局费率设置
- 代理商级别费率
- 企业级别费率
- 费率优先级规则

### 促销活动

- 促销活动创建和管理
- 优惠券系统
- 折扣规则配置
- 活动效果分析

### API限流保护

系统实现了多层次的API限流保护机制，详细说明请参考[API限流保护文档](api-rate-limiting.md)。

主要特性包括：

- 基于Redis的分布式限流
- 多级限流策略（全局、角色、路径）
- 熔断器机制
- 可配置的限流规则

### 系统监控

系统集成了Prometheus监控，实现了关键指标的收集和监控，详细说明请参考[系统监控文档](system-monitoring.md)。

### 缓存策略

系统实现了多级缓存策略，提高系统性能，详细说明请参考[缓存策略优化文档](cache-strategy-optimization.md)。

### 异步任务处理

系统实现了异步任务处理机制，用于处理长时间运行的任务，详细说明请参考[异步任务处理文档](async-task-processing.md)。

## 如何添加新的eSIM提供商

添加新的eSIM提供商的详细指南请参考[添加新的eSIM提供商集成指南](extending-providers.md)。

## 数据库优化

系统实现了数据库查询优化和索引优化，提高数据库性能，详细说明请参考[数据库查询优化文档](database-query-optimization.md)。

## 后续开发计划

为了进一步完善eSIM聚合平台，计划在以下几个方面进行开发：

1. **添加更多提供商**：继续集成更多eSIM提供商，扩大服务覆盖范围。
2. **完善支付系统**：集成更多支付方式，提高支付的便捷性和安全性。
3. **增强监控和报警**：完善系统监控，增加自动报警机制。
4. **实现智能路由**：基于成本、性能和可靠性等因素，实现智能提供商选择。
5. **实现多语言支持**：支持多语言界面和提示信息。

## 代码风格和规范

开发过程中，请遵循以下规范：

1. 遵循Go的编码规范，保持代码风格一致。
2. 使用接口进行抽象，实现依赖注入，提高代码可测试性。
3. 编写详细的注释，说明函数、结构体的用途和使用方法。
4. 实现全面的错误处理，不忽略任何错误。
5. 编写单元测试和集成测试，确保代码质量。
6. 使用上下文（context）管理请求生命周期，支持取消和超时。
7. 使用事务确保数据一致性，特别是在涉及多个数据库操作的场景。

### 部署

详细的部署指南请参考[部署文档](deploy.md)。

## 常见问题解答

1. **如何处理提供商API变更**？

   提供商API变更时，只需更新相应的服务实现，而不影响统一接口的使用。系统的分层架构确保了这种灵活性。

2. **如何处理大量并发请求**？

   系统使用了异步处理机制、高效的缓存策略和API限流机制，可以有效处理大量并发请求，保持良好的性能。

## 结论

AuraESIM平台已经实现了完整的基础架构和核心功能，包括提供商集成、用户管理、订单处理、代理商系统、企业管理、积分系统、费率管理、促销活动管理、API限流保护、系统监控和性能优化。系统采用了分层架构和模块化设计，提供了高度的可扩展性和可维护性。未来将继续完善功能，提高系统性能和用户体验，为客户提供更加全面和便捷的eSIM服务。