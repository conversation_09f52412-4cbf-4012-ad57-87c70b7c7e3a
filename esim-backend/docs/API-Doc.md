# Let's eSIM API 文档

本文档描述了Let's eSIM平台的API接口。

## 目录

- [认证方式](#%E8%AE%A4%E8%AF%81%E6%96%B9%E5%BC%8F)
- [错误处理](#%E9%94%99%E8%AF%AF%E5%A4%84%E7%90%86)
- [用户类型与权限](#%E7%94%A8%E6%88%B7%E7%B1%BB%E5%9E%8B%E4%B8%8E%E6%9D%83%E9%99%90)
- [公共路由](#%E5%85%AC%E5%85%B1%E8%B7%AF%E7%94%B1)
- [用户路由](#%E7%94%A8%E6%88%B7%E8%B7%AF%E7%94%B1)
- [提供商路由](#%E6%8F%90%E4%BE%9B%E5%95%86%E8%B7%AF%E7%94%B1)
- [代理商路由](#%E4%BB%A3%E7%90%86%E5%95%86%E8%B7%AF%E7%94%B1)
- [企业代理商路由](#%E4%BC%81%E4%B8%9A%E4%BB%A3%E7%90%86%E5%95%86%E8%B7%AF%E7%94%B1)
- [eSIM路由](#esim%E8%B7%AF%E7%94%B1)
- [订单路由](#%E8%AE%A2%E5%8D%95%E8%B7%AF%E7%94%B1)
- [积分路由](#%E7%A7%AF%E5%88%86%E8%B7%AF%E7%94%B1)
- [促销路由](#%E4%BF%83%E9%94%80%E8%B7%AF%E7%94%B1)
- [费率路由](#%E8%B4%B9%E7%8E%87%E8%B7%AF%E7%94%B1)
- [管理员路由](#%E7%AE%A1%E7%90%86%E5%91%98%E8%B7%AF%E7%94%B1)
- [Webhook路由](#webhook%E8%B7%AF%E7%94%B1)

## 认证方式

API支持多种认证机制：

- **个人用户/企业员工认证**
  - 采用JWT令牌认证
  - 令牌有效期：24小时
  - 通过登录接口`POST /api/v1/auth/login`获取

- **代理商/企业代理商认证**
  - 方式一（Web前端）：JWT令牌认证
  - 方式二（服务端集成）：API密钥认证，需同时提供`X-API-Key`和`X-API-Secret`
  - API密钥无过期时间，但可由管理员重置

- **管理员认证**
  - 采用JWT令牌认证，具有admin权限
  - 令牌有效期：24小时

### JWT令牌认证

对于需要JWT令牌认证的接口，请求时需在HTTP头部包含：

```
Authorization: Bearer {token}
```

其中`{token}`为登录接口返回的JWT令牌。

### API密钥认证

代理商/企业代理商服务端集成时，请求时需在HTTP头部包含：

```
X-API-Key: {api_key}
X-API-Secret: {api_secret}
```

API密钥和密钥密文由管理员创建并提供给代理商/企业代理商。

## 错误处理

API遵循HTTP标准状态码，同时提供更详细的错误信息：

### 错误响应格式

所有API错误返回统一格式：

```json
{
  "code": 400,          // HTTP状态码
  "message": "错误信息",  // 错误描述
  "details": {          // 可选，详细错误信息
    "field": "错误字段",
    "reason": "错误原因"
  },
  "requestId": "请求ID"  // 可选，用于跟踪和调试
}
```

### 常见HTTP状态码

| 状态码 | 说明 | 示例场景 |
|------|------|--------|
| 200 | 成功 | 成功获取资源 |
| 201 | 已创建 | 成功创建资源 |
| 400 | 请求参数错误 | 缺少必需参数、参数格式错误 |
| 401 | 认证失败 | 未提供认证信息、认证信息无效 |
| 403 | 权限不足 | 无权访问特定资源 |
| 404 | 资源不存在 | 请求的ID不存在 |
| 409 | 资源冲突 | 资源已存在、状态冲突 |
| 422 | 请求格式正确但无法处理 | 业务逻辑验证失败 |
| 429 | 请求过于频繁 | 超出API访问限制 |
| 500 | 服务器内部错误 | 服务器异常 |

### 常见业务错误代码

除HTTP状态码外，API可能在详细错误信息中返回以下业务错误代码：

| 错误代码 | 说明 | HTTP状态码 |
|---------|------|-----------|
| INVALID_PARAM | 无效参数 | 400 |
| MISSING_PARAM | 缺少必需参数 | 400 |
| INVALID_AUTH | 认证信息无效 | 401 |
| TOKEN_EXPIRED | 令牌已过期 | 401 |
| PERMISSION_DENIED | 权限不足 | 403 |
| RESOURCE_NOT_FOUND | 资源不存在 | 404 |
| DUPLICATE_RESOURCE | 资源已存在 | 409 |
| INSUFFICIENT_BALANCE | 余额不足 | 422 |
| INVALID_STATUS | 状态无效 | 422 |
| RATE_LIMIT_EXCEEDED | 超出访问限制 | 429 |
| SERVER_ERROR | 服务器内部错误 | 500 |

### 错误处理最佳实践

1. 始终检查HTTP状态码，确保请求成功（2xx）
2. 对于4xx错误，根据错误信息调整请求参数或认证信息
3. 对于429错误，实现退避重试机制
4. 对于500错误，可以重试，但应限制重试次数

## 用户类型与权限

| 类型 | 说明 |
| --- | --- |
| user | 个人用户 |
| reseller | 代理商，拥有下属用户和esim管理能力 |
| enterprise | 企业代理商，拥有reseller全部能力+企业员工/部门/批量分配能力 |
| admin | 平台管理员 |

## 公共路由

这些路由不需要认证即可访问。

### 用户注册

- **URL**: `/api/v1/auth/register`
- **方法**: `POST`
- **状态**: 已实现
- **请求体**:

  ```json
  {
    "email": "<EMAIL>",         // 必需，有效的邮箱地址
    "password": "password123",           // 必需，至少8个字符
    "name": "用户名",                     // 必需，用户显示名称
    "mobile": "13800138000"              // 可选，手机号码
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "user_id",                     // 用户唯一标识符
    "email": "<EMAIL>",         // 用户邮箱
    "name": "用户名",                     // 用户显示名称
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",  // JWT令牌
    "createdAt": "2023-01-01T00:00:00Z"  // 创建时间
  }
  ```

- **错误响应**: 
  - `400 Bad Request`: 请求参数无效
    ```json
    {
      "code": 400,
      "message": "邮箱格式无效"
    }
    ```
  - `409 Conflict`: 邮箱已被注册
    ```json
    {
      "code": 409,
      "message": "该邮箱已被注册"
    }
    ```

### 用户登录

- **URL**: `/api/v1/auth/login`
- **方法**: `POST`
- **状态**: 已实现
- **请求体**:

  ```json
  {
    "email": "<EMAIL>",         // 必需，已注册的邮箱地址
    "password": "password123"            // 必需，账户密码
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "user_id",                      // 用户ID
    "email": "<EMAIL>",          // 用户邮箱
    "name": "用户名",                      // 用户显示名称
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // JWT访问令牌，有效期24小时
    "role": "USER"                        // 用户角色：USER, RESELLER, ENTERPRISE, ADMIN
  }
  ```

- **错误响应**:
  - `401 Unauthorized`: 认证失败
    ```json
    {
      "code": 401,
      "message": "邮箱或密码错误"
    }
    ```
  - `403 Forbidden`: 账户已被禁用
    ```json
    {
      "code": 403,
      "message": "账户已被禁用，请联系管理员"
    }
    ```

### 密码重置

- **URL**: `/api/v1/auth/password/reset`
- **方法**: `POST`
- **状态**: 已实现
- **请求体**:

  ```json
  {
    "email": "<EMAIL>"          // 必需，已注册的邮箱地址
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "message": "重置密码邮件已发送到您的邮箱",
    "expiresIn": 3600                    // 重置链接有效期（秒）
  }
  ```

- **错误响应**:
  - `404 Not Found`: 邮箱未注册
    ```json
    {
      "code": 404,
      "message": "该邮箱未注册"
    }
    ```
  - `429 Too Many Requests`: 请求频率过高
    ```json
    {
      "code": 429,
      "message": "请求过于频繁，请稍后再试",
      "retryAfter": 300                  // 建议重试时间（秒）
    }
    ```

### 验证重置令牌

- **URL**: `/api/v1/auth/password/reset/verify`
- **方法**: `GET`
- **状态**: 已实现
- **参数**:
  - `token` (查询参数): 重置令牌，必需
- **响应**: `200 OK`

  ```json
  {
    "valid": true,                       // 令牌是否有效
    "email": "<EMAIL>"          // 关联的邮箱（令牌有效时提供）
  }
  ```

### 完成密码重置

- **URL**: `/api/v1/auth/password/reset/complete`
- **方法**: `POST`
- **状态**: 已实现
- **请求体**:

  ```json
  {
    "token": "reset_token",              // 必需，重置令牌
    "password": "new_password"           // 必需，新密码，至少8个字符
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "message": "密码重置成功",
    "canLogin": true                     // 是否可以立即使用新密码登录
  }
  ```

- **错误响应**:
  - `400 Bad Request`: 密码不符合要求
    ```json
    {
      "code": 400,
      "message": "密码至少需要8个字符"
    }
    ```
  - `401 Unauthorized`: 令牌无效或已过期
    ```json
    {
      "code": 401,
      "message": "重置令牌无效或已过期"
    }
    ```

## 用户路由

这些路由需要用户认证。

### 获取用户资料

- **URL**: `/api/v1/users/me`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **响应**: `200 OK`

  ```json
  {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "用户名",
    "mobile": "13800138000",
    "role": "USER",
    "status": "ACTIVE",
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z"
  }
  ```

### 更新用户资料

- **URL**: `/api/v1/users/me`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要用户认证
- **请求体**:

  ```json
  {
    "name": "新用户名",                  // 可选
    "mobile": "13900139000"              // 可选
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "新用户名",
    "mobile": "13900139000",
    "role": "USER",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-10T09:00:00Z"
  }
  ```

### 修改密码

- **URL**: `/api/v1/users/me/password`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要用户认证
- **请求体**:

  ```json
  {
    "currentPassword": "old_password",   // 必需
    "newPassword": "new_password"        // 必需，至少8个字符
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "message": "密码修改成功"
  }
  ```

### 获取用户eSIM列表

- **URL**: `/api/v1/users/me/esims`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
- **响应**: `200 OK`

  ```json
  {
    "esims": [
      {
        "id": "esim_123",
        "iccid": "8988228801234567890",
        "status": "ACTIVE",
        "activationCode": "LPA:1$...",
        "qrCodeURL": "https://example.com/qr/123",
        "dataVolume": **********,
        "usedData": **********,
        "validityDays": 30,
        "expiryTime": "2023-02-01T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 5,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

### 获取用户订单列表

- **URL**: `/api/v1/users/me/orders`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `startDate` (查询参数): 开始日期过滤，格式：YYYY-MM-DD
  - `endDate` (查询参数): 结束日期过滤，格式：YYYY-MM-DD
- **响应**: `200 OK`

  ```json
  {
    "orders": [
      {
        "id": "order_123",
        "providerType": "esim_access",
        "status": "COMPLETED",
        "totalAmount": 8910,
        "currency": "CNY",
        "createdAt": "2023-01-15T12:00:00Z",
        "completedAt": "2023-01-15T12:05:00Z",
        "esimCount": 1,
        "esimStatus": {
          "ACTIVE": 1
        }
      }
    ],
    "pagination": {
      "total": 10,
      "page": 1,
      "pageSize": 10,
      "startDate": "2023-01-01",
      "endDate": "2023-01-31"
    }
  }
  ```

## 代理商路由

这些路由需要代理商认证。

### 获取代理商资料

- **URL**: `/api/v1/resellers/me`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **响应**: `200 OK`

  ```json
  {
    "id": "reseller_123",
    "name": "优质代理商",
    "email": "<EMAIL>",
    "contactPerson": "王经理",
    "contactPhone": "13900139000",
    "status": "ACTIVE",
    "apiKey": "api_key_123",
    "commissionRate": 0.15,
    "createdAt": "2022-01-01T00:00:00Z"
  }
  ```

### 更新代理商资料

- **URL**: `/api/v1/resellers/me`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **请求体**:

  ```json
  {
    "name": "超级代理商",
    "contactPerson": "李经理",
    "contactPhone": "13911139111"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "reseller_123",
    "name": "超级代理商",
    "email": "<EMAIL>",
    "contactPerson": "李经理",
    "contactPhone": "13911139111",
    "status": "ACTIVE",
    "apiKey": "api_key_123",
    "commissionRate": 0.15,
    "createdAt": "2022-01-01T00:00:00Z",
    "updatedAt": "2023-01-16T10:00:00Z"
  }
  ```

### 获取余额

- **URL**: `/api/v1/resellers/me/balance`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **响应**: `200 OK`

  ```json
  {
    "balance": 50000,
    "currency": "USD",
    "lastUpdated": "2023-01-16T11:00:00Z"
  }
  ```

### 获取用户列表

- **URL**: `/api/v1/resellers/users`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `search` (查询参数): 搜索关键词
- **响应**: `200 OK`

  ```json
  {
    "users": [
      {
        "id": "user_456",
        "name": "张客户",
        "email": "<EMAIL>",
        "mobile": "13800138000",
        "status": "ACTIVE",
        "createdAt": "2023-01-10T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

### 创建用户

- **URL**: `/api/v1/resellers/users`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **请求体**:

  ```json
  {
    "name": "李新客",
    "email": "<EMAIL>",
    "mobile": "13922239222",
    "password": "password123"
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "user_789",
    "name": "李新客",
    "email": "<EMAIL>",
    "mobile": "13922239222",
    "status": "ACTIVE",
    "createdAt": "2023-01-16T12:00:00Z"
  }
  ```

### 为用户充值积分

- **URL**: `/api/v1/resellers/users/{id}/credits`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **参数**:
  - `id` (路径参数): 用户ID，必需
- **请求体**:

  ```json
  {
    "amount": 100,
    "description": "代理商充值"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "transaction": {
      "id": "trans_456",
      "amount": 100,
      "balance": 1100,
      "type": "DEPOSIT",
      "description": "由代理商 超级代理商 充值",
      "status": "COMPLETED",
      "createdAt": "2023-01-16T13:00:00Z"
    },
    "credit": {
      "id": "credit_456",
      "balance": 1100,
      "currency": "USD"
    }
  }
  ```

### 查询/管理代理商促销码

- **URL**: `/api/v1/resellers/promotions`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `status` (查询参数): 状态过滤，可选值："ACTIVE", "INACTIVE", "EXPIRED"
- **响应**: `200 OK`

  ```json
  {
    "promotions": [
      {
        "id": "promo_123",
        "code": "RESELLER10",
        "description": "代理商专属折扣",
        "type": "PERCENTAGE",
        "value": 10,
        "minOrderValue": 5000,
        "maxDiscount": 1000,
        "startDate": "2023-01-01T00:00:00Z",
        "endDate": "2023-12-31T23:59:59Z",
        "usageLimit": 100,
        "currentUsage": 10,
        "status": "ACTIVE",
        "createdAt": "2023-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 10,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

### 创建代理商促销码

- **URL**: `/api/v1/resellers/promotions`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **请求体**:

  ```json
  {
    "code": "NEWCUSTOMER20",
    "description": "新客户专享折扣",
    "type": "PERCENTAGE",
    "value": 20,
    "minOrderValue": 3000,
    "maxDiscount": 500,
    "startDate": "2023-02-01",
    "endDate": "2023-02-28",
    "usageLimit": 50
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "promo_456",
    "code": "NEWCUSTOMER20",
    "description": "新客户专享折扣",
    "type": "PERCENTAGE",
    "value": 20,
    "minOrderValue": 3000,
    "maxDiscount": 500,
    "startDate": "2023-02-01T00:00:00Z",
    "endDate": "2023-02-28T23:59:59Z",
    "usageLimit": 50,
    "currentUsage": 0,
    "status": "ACTIVE",
    "createdAt": "2023-01-17T00:00:00Z"
  }
  ```

### 代理商费率管理

- **URL**: `/api/v1/resellers/rates`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **参数**:
  - `packageId` (查询参数): 套餐ID过滤
  - `locationCode` (查询参数): 地区代码过滤
- **响应**: `200 OK`

  ```json
  {
    "rates": [
      {
        "id": "rate_123",
        "packageId": "pkg_123",
        "locationCode": "EU",
        "rate": 8900,
        "currency": "CNY",
        "updatedAt": "2023-01-10T00:00:00Z"
      }
    ]
  }
  ```

## 企业代理商路由

这些路由需要企业代理商认证。

### 员工管理

#### 获取员工列表

- **URL**: `/api/v1/enterprises/employees`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `departmentId` (查询参数): 部门ID过滤
  - `search` (查询参数): 搜索关键词
- **响应**: `200 OK`

  ```json
  {
    "employees": [
      {
        "id": "user_123",
        "name": "张三",
        "email": "<EMAIL>",
        "mobile": "13800138000",
        "departmentId": "dept_123",
        "departmentName": "技术部",
        "status": "ACTIVE",
        "createdAt": "2023-01-10T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

#### 创建员工

- **URL**: `/api/v1/enterprises/employees`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **请求体**:

  ```json
  {
    "name": "李四",
    "email": "<EMAIL>",
    "mobile": "13900139000",
    "departmentId": "dept_123",
    "password": "password123"
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "user_456",
    "name": "李四",
    "email": "<EMAIL>",
    "mobile": "13900139000",
    "departmentId": "dept_123",
    "departmentName": "技术部",
    "status": "ACTIVE",
    "createdAt": "2023-01-17T12:00:00Z"
  }
  ```

#### 获取员工详情

- **URL**: `/api/v1/enterprises/employees/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `id` (路径参数): 员工ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "user_123",
    "name": "张三",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "departmentId": "dept_123",
    "departmentName": "技术部",
    "status": "ACTIVE",
    "createdAt": "2023-01-10T00:00:00Z",
    "lastLoginAt": "2023-01-16T08:30:00Z"
  }
  ```

#### 更新员工信息

- **URL**: `/api/v1/enterprises/employees/{id}`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `id` (路径参数): 员工ID，必需
- **请求体**:

  ```json
  {
    "name": "张三丰",
    "mobile": "13800138001",
    "departmentId": "dept_456"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "user_123",
    "name": "张三丰",
    "email": "<EMAIL>",
    "mobile": "13800138001",
    "departmentId": "dept_456",
    "departmentName": "研发部",
    "status": "ACTIVE",
    "updatedAt": "2023-01-17T14:00:00Z"
  }
  ```

#### 删除员工

- **URL**: `/api/v1/enterprises/employees/{id}`
- **方法**: `DELETE`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `id` (路径参数): 员工ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "user_123",
    "message": "员工已成功删除"
  }
  ```

### 部门管理

#### 获取部门列表

- **URL**: `/api/v1/enterprises/departments`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **响应**: `200 OK`

  ```json
  {
    "departments": [
      {
        "id": "dept_123",
        "name": "技术部",
        "description": "负责技术研发",
        "employeeCount": 15,
        "createdAt": "2023-01-01T00:00:00Z"
      },
      {
        "id": "dept_456",
        "name": "研发部",
        "description": "负责产品研发",
        "employeeCount": 10,
        "createdAt": "2023-01-01T00:00:00Z"
      }
    ]
  }
  ```

#### 创建部门

- **URL**: `/api/v1/enterprises/departments`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **请求体**:

  ```json
  {
    "name": "销售部",
    "description": "负责产品销售"
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "dept_789",
    "name": "销售部",
    "description": "负责产品销售",
    "employeeCount": 0,
    "createdAt": "2023-01-17T15:00:00Z"
  }
  ```

#### 获取部门详情

- **URL**: `/api/v1/enterprises/departments/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `id` (路径参数): 部门ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "dept_123",
    "name": "技术部",
    "description": "负责技术研发",
    "employeeCount": 15,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
  ```

#### 更新部门

- **URL**: `/api/v1/enterprises/departments/{id}`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `id` (路径参数): 部门ID，必需
- **请求体**:

  ```json
  {
    "name": "技术研发部",
    "description": "负责核心技术研发"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "dept_123",
    "name": "技术研发部",
    "description": "负责核心技术研发",
    "updatedAt": "2023-01-17T16:00:00Z"
  }
  ```

#### 删除部门

- **URL**: `/api/v1/enterprises/departments/{id}`
- **方法**: `DELETE`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `id` (路径参数): 部门ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "dept_123",
    "message": "部门已成功删除"
  }
  ```

### eSIM批量管理

#### 批量分配eSIM

- **URL**: `/api/v1/enterprises/esims/assignments`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **请求体**:

  ```json
  {
    "employeeIds": ["user_123", "user_456"],
    "esimIds": ["esim_123", "esim_456", "esim_789"]
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "assignedCount": 3,
    "successfulAssignments": [
      {
        "employeeId": "user_123",
        "esimIds": ["esim_123", "esim_456"]
      },
      {
        "employeeId": "user_456",
        "esimIds": ["esim_789"]
      }
    ],
    "message": "eSIM分配成功"
  }
  ```

#### 批量回收eSIM

- **URL**: `/api/v1/enterprises/esims/assignments`
- **方法**: `DELETE`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **请求体**:

  ```json
  {
    "esimIds": ["esim_123", "esim_456"]
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "reclaimedCount": 2,
    "reclaimedEsims": ["esim_123", "esim_456"],
    "message": "eSIM回收成功"
  }
  ```

#### 查询员工eSIM列表

- **URL**: `/api/v1/enterprises/employees/{id}/esims`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **参数**:
  - `id` (路径参数): 员工ID，必需
- **响应**: `200 OK`

  ```json
  {
    "esims": [
      {
        "id": "esim_123",
        "iccid": "8988228801234567890",
        "status": "ACTIVE",
        "activationCode": "LPA:1$...",
        "qrCodeURL": "https://example.com/qr/123",
        "dataVolume": **********,
        "usedData": **********,
        "validityDays": 30,
        "expiryTime": "2023-02-15T00:00:00Z"
      }
    ]
  }
  ```

### 企业费率管理

- **URL**: `/api/v1/enterprises/rates`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **响应**: `200 OK`

  ```json
  {
    "rates": [
      {
        "id": "rate_456",
        "packageId": "pkg_123",
        "locationCode": "EU",
        "rate": 8500,
        "currency": "CNY",
        "updatedAt": "2023-01-15T00:00:00Z"
      }
    ]
  }
  ```

## 积分路由

这些路由需要用户认证。

### 获取用户积分

- **URL**: `/api/v1/credits`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **响应**: `200 OK`

  ```json
  {
    "id": "credit_123",
    "balance": 10000,
    "currency": "USD",
    "lastUpdated": "2023-01-15T14:00:00Z"
  }
  ```

### 充值积分

- **URL**: `/api/v1/credits`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要用户认证
- **请求体**:

  ```json
  {
    "amount": 100,
    "description": "充值100美元"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "transaction": {
      "id": "trans_123",
      "amount": 100,
      "balance": 10100,
      "type": "DEPOSIT",
      "description": "充值100美元",
      "status": "COMPLETED",
      "createdAt": "2023-01-15T14:30:00Z"
    },
    "credit": {
      "id": "credit_123",
      "balance": 10100,
      "currency": "USD"
    }
  }
  ```

### 获取交易历史

- **URL**: `/api/v1/credits/transactions`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `page`: 页码，默认为1
  - `pageSize`: 每页记录数，默认为10，最大为100
- **响应**: `200 OK`

  ```json
  {
    "transactions": [
      {
        "id": "trans_123",
        "amount": 100,
        "balance": 10100,
        "type": "DEPOSIT",
        "typeDesc": "充值",
        "description": "充值100美元",
        "createdAt": "2023-01-15T14:30:00Z"
      },
      {
        "id": "trans_124",
        "amount": -50,
        "balance": 10050,
        "type": "ORDER_PAYMENT",
        "typeDesc": "订单支付",
        "description": "订单order_123支付",
        "orderId": "order_123",
        "createdAt": "2023-01-15T15:00:00Z"
      }
    ],
    "pagination": {
      "total": 20,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

### 获取交易详情

- **URL**: `/api/v1/credits/transactions/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): 交易ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "trans_123",
    "amount": 100,
    "balance": 10100,
    "type": "DEPOSIT",
    "typeDesc": "充值",
    "description": "充值100美元",
    "orderId": "",
    "status": "COMPLETED",
    "createdAt": "2023-01-15T14:30:00Z",
    "updatedAt": "2023-01-15T14:30:00Z"
  }
  ```

### 管理员添加/扣除用户积分

- **URL**: `/api/v1/admin/user/:id/credit`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **请求体**:

  ```json
  {
    "amount": 50.00,    // 正数表示增加，负数表示扣除
    "description": "管理员操作"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "transaction": {
      "id": "trans123",
      "amount": 50.00,
      "balance": 150.50,
      "type": "DEPOSIT",  // 或 "WITHDRAWAL"
      "description": "管理员操作",
      "status": "COMPLETED",
      "createdAt": "2025-03-31T12:34:56Z"
    },
    "credit": {
      "id": "credit123",
      "balance": 150.50,
      "currency": "USD"
    }
  }
  ```

## 错误代码

以下错误代码适用于积分路由：

| 代码 | 说明 |
| --- | --- |
| INSUFFICIENT_BALANCE | 余额不足 |
| USER_NOT_FOUND | 用户不存在 |
| TRANSACTION_NOT_FOUND | 交易记录不存在 |
| CREDIT_NOT_FOUND | 积分账户不存在 |
| INVALID_AMOUNT | 无效的金额 |

## 交易类型

积分交易类型包括：

| 类型 | 说明 |
| --- | --- |
| DEPOSIT | 充值 |
| WITHDRAWAL | 提现 |
| ORDER_PAYMENT | 订单支付 |
| REFUND | 退款 |

## 促销路由

这些路由需要用户认证。

### 验证促销码

- **URL**: `/api/v1/promotions/validate`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要用户认证
- **请求体**:

  ```json
  {
    "code": "WELCOME10",
    "packageId": "pkg_123",
    "amount": 9900
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "valid": true,
    "discount": 990,
    "message": "有效的促销码，可获得10%折扣"
  }
  ```

### 应用促销码

- **URL**: `/api/v1/promotions`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要用户认证
- **请求体**:

  ```json
  {
    "code": "WELCOME10",
    "packageId": "pkg_123",
    "amount": 9900
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "promotionId": "promo_123",
    "code": "WELCOME10",
    "discount": 990,
    "finalAmount": 8910,
    "message": "促销码应用成功"
  }
  ```

## 费率路由

### 查询代理商费率

- **URL**: `/api/v1/reseller/rates`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **参数**:
  - `packageId` (查询参数): 套餐ID过滤
  - `country` (查询参数): 国家过滤
- **响应**: `200 OK`

  ```json
  {
    "rates": [
      {
        "packageId": "套餐ID",
        "country": "CN",
        "rate": 900,
        "currency": "CNY",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ]
  }
  ```

### 设置代理商费率

- **URL**: `/api/v1/reseller/rate`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要代理商认证
- **请求体**:

  ```json
  {
    "packageId": "套餐ID",
    "country": "CN",
    "rate": 900
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "packageId": "套餐ID",
    "country": "CN",
    "rate": 900,
    "currency": "CNY",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
  ```

### 查询/设置企业代理商费率

- **URL**: `/api/v1/enterprise/rates` (GET)
- **URL**: `/api/v1/enterprise/rate` (POST)
- **方法**: `GET`/`POST`
- **状态**: 已实现
- **认证**: 需要企业代理商认证
- **请求体/响应**: 同代理商费率

### 查询/设置管理员全局费率

- **URL**: `/api/v1/admin/rates` (GET)
- **URL**: `/api/v1/admin/rate` (POST)
- **方法**: `GET`/`POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **请求体/响应**: 同代理商费率

## 管理员路由

这些路由需要管理员认证。

### 用户管理

#### 获取用户列表

- **URL**: `/api/v1/admin/users`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `role` (查询参数): 角色过滤，可选值："USER", "RESELLER", "ENTERPRISE", "ADMIN"
  - `status` (查询参数): 状态过滤，可选值："ACTIVE", "INACTIVE", "SUSPENDED"
  - `search` (查询参数): 搜索关键词
- **响应**: `200 OK`

  ```json
  {
    "users": [
      {
        "id": "user_123",
        "name": "张三",
        "email": "<EMAIL>",
        "mobile": "13800138000",
        "role": "USER",
        "status": "ACTIVE",
        "createdAt": "2023-01-01T00:00:00Z",
        "lastLoginAt": "2023-01-15T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

#### 为用户添加积分

- **URL**: `/api/v1/admin/users/{id}/credits`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 用户ID，必需
- **请求体**:

  ```json
  {
    "amount": 100,
    "description": "管理员充值"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "transaction": {
      "id": "trans_123",
      "amount": 100,
      "balance": 1100,
      "type": "DEPOSIT",
      "description": "管理员充值",
      "status": "COMPLETED",
      "createdAt": "2023-01-18T10:00:00Z"
    },
    "credit": {
      "id": "credit_123",
      "balance": 1100,
      "currency": "USD"
    }
  }
  ```

### 促销管理

#### 创建促销码

- **URL**: `/api/v1/admin/promotions`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **请求体**:

  ```json
  {
    "code": "GLOBAL20",
    "description": "全局促销活动20%折扣",
    "type": "PERCENTAGE",
    "value": 20,
    "minOrderValue": 5000,
    "maxDiscount": 1000,
    "startDate": "2023-02-01",
    "endDate": "2023-02-28",
    "usageLimit": 1000,
    "scope": "GLOBAL"
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "promo_123",
    "code": "GLOBAL20",
    "description": "全局促销活动20%折扣",
    "type": "PERCENTAGE",
    "value": 20,
    "minOrderValue": 5000,
    "maxDiscount": 1000,
    "startDate": "2023-02-01T00:00:00Z",
    "endDate": "2023-02-28T23:59:59Z",
    "usageLimit": 1000,
    "currentUsage": 0,
    "status": "ACTIVE",
    "scope": "GLOBAL",
    "createdAt": "2023-01-18T11:00:00Z"
  }
  ```

#### 获取促销码列表

- **URL**: `/api/v1/admin/promotions`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `status` (查询参数): 状态过滤，可选值："ACTIVE", "INACTIVE", "EXPIRED"
  - `scope` (查询参数): 范围过滤，可选值："GLOBAL", "RESELLER", "ENTERPRISE"
- **响应**: `200 OK`

  ```json
  {
    "promotions": [
      {
        "id": "promo_123",
        "code": "GLOBAL20",
        "description": "全局促销活动20%折扣",
        "type": "PERCENTAGE",
        "value": 20,
        "minOrderValue": 5000,
        "maxDiscount": 1000,
        "startDate": "2023-02-01T00:00:00Z",
        "endDate": "2023-02-28T23:59:59Z",
        "usageLimit": 1000,
        "currentUsage": 0,
        "status": "ACTIVE",
        "scope": "GLOBAL",
        "createdAt": "2023-01-18T11:00:00Z"
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

#### 获取促销码详情

- **URL**: `/api/v1/admin/promotions/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 促销码ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "promo_123",
    "code": "GLOBAL20",
    "description": "全局促销活动20%折扣",
    "type": "PERCENTAGE",
    "value": 20,
    "minOrderValue": 5000,
    "maxDiscount": 1000,
    "startDate": "2023-02-01T00:00:00Z",
    "endDate": "2023-02-28T23:59:59Z",
    "usageLimit": 1000,
    "currentUsage": 0,
    "status": "ACTIVE",
    "scope": "GLOBAL",
    "createdAt": "2023-01-18T11:00:00Z",
    "updatedAt": "2023-01-18T11:00:00Z"
  }
  ```

#### 更新促销码

- **URL**: `/api/v1/admin/promotions/{id}`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 促销码ID，必需
- **请求体**:

  ```json
  {
    "description": "全局促销活动30%折扣",
    "value": 30,
    "minOrderValue": 3000,
    "maxDiscount": 1500,
    "endDate": "2023-03-31",
    "status": "ACTIVE"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "promo_123",
    "description": "全局促销活动30%折扣",
    "value": 30,
    "minOrderValue": 3000,
    "maxDiscount": 1500,
    "endDate": "2023-03-31T23:59:59Z",
    "status": "ACTIVE",
    "updatedAt": "2023-01-18T12:00:00Z"
  }
  ```

#### 删除促销码

- **URL**: `/api/v1/admin/promotions/{id}`
- **方法**: `DELETE`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 促销码ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "promo_123",
    "message": "促销码已成功删除"
  }
  ```

### 代理商管理

#### 创建代理商

- **URL**: `/api/v1/admin/resellers`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **请求体**:

  ```json
  {
    "name": "超级代理商",
    "email": "<EMAIL>",
    "contactPerson": "王经理",
    "contactPhone": "13900139000",
    "password": "password123",
    "commissionRate": 0.15
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "reseller_123",
    "name": "超级代理商",
    "email": "<EMAIL>",
    "contactPerson": "王经理",
    "contactPhone": "13900139000",
    "commissionRate": 0.15,
    "status": "PENDING",
    "apiKey": "api_key_123",
    "apiSecret": "api_secret_xyz",
    "createdAt": "2023-01-18T13:00:00Z"
  }
  ```

#### 获取代理商列表

- **URL**: `/api/v1/admin/resellers`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `status` (查询参数): 状态过滤，可选值："ACTIVE", "INACTIVE", "PENDING"
  - `search` (查询参数): 搜索关键词
- **响应**: `200 OK`

  ```json
  {
    "resellers": [
      {
        "id": "reseller_123",
        "name": "超级代理商",
        "email": "<EMAIL>",
        "contactPerson": "王经理",
        "contactPhone": "13900139000",
        "status": "ACTIVE",
        "commissionRate": 0.15,
        "createdAt": "2023-01-18T13:00:00Z"
      }
    ],
    "pagination": {
      "total": 20,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

#### 获取代理商详情

- **URL**: `/api/v1/admin/resellers/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 代理商ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "reseller_123",
    "name": "超级代理商",
    "email": "<EMAIL>",
    "contactPerson": "王经理",
    "contactPhone": "13900139000",
    "status": "ACTIVE",
    "commissionRate": 0.15,
    "balance": 50000,
    "currency": "USD",
    "apiKey": "api_key_123",
    "createdAt": "2023-01-18T13:00:00Z",
    "updatedAt": "2023-01-18T13:00:00Z"
  }
  ```

#### 更新代理商

- **URL**: `/api/v1/admin/resellers/{id}`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 代理商ID，必需
- **请求体**:

  ```json
  {
    "name": "顶级代理商",
    "contactPerson": "李经理",
    "contactPhone": "13911139111",
    "commissionRate": 0.18
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "reseller_123",
    "name": "顶级代理商",
    "contactPerson": "李经理",
    "contactPhone": "13911139111",
    "commissionRate": 0.18,
    "updatedAt": "2023-01-18T14:00:00Z"
  }
  ```

#### 更新代理商状态

- **URL**: `/api/v1/admin/resellers/{id}/status`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 代理商ID，必需
- **请求体**:

  ```json
  {
    "status": "ACTIVE"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "reseller_123",
    "status": "ACTIVE",
    "message": "代理商状态已更新",
    "updatedAt": "2023-01-18T15:00:00Z"
  }
  ```

#### 重新生成API密钥

- **URL**: `/api/v1/admin/resellers/{id}/api-keys`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 代理商ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "reseller_123",
    "apiKey": "new_api_key_456",
    "apiSecret": "new_api_secret_xyz",
    "message": "API密钥已重新生成",
    "updatedAt": "2023-01-18T16:00:00Z"
  }
  ```

#### 添加代理商余额

- **URL**: `/api/v1/admin/resellers/{id}/balance`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 代理商ID，必需
- **请求体**:

  ```json
  {
    "amount": 5000,
    "description": "管理员充值"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "reseller_123",
    "previousBalance": 50000,
    "newBalance": 55000,
    "amount": 5000,
    "currency": "USD",
    "message": "余额已成功添加",
    "transactionId": "trans_456",
    "updatedAt": "2023-01-18T17:00:00Z"
  }
  ```

### 企业代理商管理

#### 获取企业代理商列表

- **URL**: `/api/v1/admin/enterprises`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `status` (查询参数): 状态过滤，可选值："ACTIVE", "INACTIVE", "PENDING"
  - `search` (查询参数): 搜索关键词
- **响应**: `200 OK`

  ```json
  {
    "enterprises": [
      {
        "id": "enterprise_123",
        "name": "大型企业客户",
        "email": "<EMAIL>",
        "contactPerson": "赵总",
        "contactPhone": "13922239222",
        "status": "ACTIVE",
        "createdAt": "2023-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 5,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

#### 获取企业代理商详情

- **URL**: `/api/v1/admin/enterprises/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 企业代理商ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "enterprise_123",
    "name": "大型企业客户",
    "email": "<EMAIL>",
    "contactPerson": "赵总",
    "contactPhone": "13922239222",
    "status": "ACTIVE",
    "commissionRate": 0.12,
    "balance": 100000,
    "currency": "USD",
    "apiKey": "api_key_789",
    "departmentCount": 5,
    "employeeCount": 50,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-10T00:00:00Z"
  }
  ```

#### 更新企业代理商

- **URL**: `/api/v1/admin/enterprises/{id}`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 企业代理商ID，必需
- **请求体**:

  ```json
  {
    "name": "特大型企业客户",
    "contactPerson": "钱总",
    "contactPhone": "13933339333",
    "commissionRate": 0.15
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "enterprise_123",
    "name": "特大型企业客户",
    "contactPerson": "钱总",
    "contactPhone": "13933339333",
    "commissionRate": 0.15,
    "updatedAt": "2023-01-19T10:00:00Z"
  }
  ```

### 全局费率管理

#### 获取全局费率列表

- **URL**: `/api/v1/admin/rates`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `packageId` (查询参数): 套餐ID过滤
  - `locationCode` (查询参数): 地区代码过滤
- **响应**: `200 OK`

  ```json
  {
    "rates": [
      {
        "id": "rate_789",
        "packageId": "pkg_123",
        "locationCode": "EU",
        "rate": 9900,
        "currency": "CNY",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ]
  }
  ```

#### 创建全局费率

- **URL**: `/api/v1/admin/rates`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **请求体**:

  ```json
  {
    "packageId": "pkg_456",
    "locationCode": "US",
    "rate": 7900,
    "currency": "CNY"
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "rate_012",
    "packageId": "pkg_456",
    "locationCode": "US",
    "rate": 7900,
    "currency": "CNY",
    "createdAt": "2023-01-19T11:00:00Z"
  }
  ```

#### 更新全局费率

- **URL**: `/api/v1/admin/rates/{id}`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 费率ID，必需
- **请求体**:

  ```json
  {
    "rate": 7500,
    "currency": "CNY"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "rate_012",
    "packageId": "pkg_456",
    "locationCode": "US",
    "rate": 7500,
    "currency": "CNY",
    "updatedAt": "2023-01-19T12:00:00Z"
  }
  ```

#### 删除全局费率

- **URL**: `/api/v1/admin/rates/{id}`
- **方法**: `DELETE`
- **状态**: 已实现
- **认证**: 需要管理员认证
- **参数**:
  - `id` (路径参数): 费率ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "rate_012",
    "message": "费率已成功删除"
  }
  ```

## Webhook路由

### 处理提供商Webhook

- **URL**: `/api/v1/webhooks/{provider}`
- **方法**: `POST`
- **状态**: 已实现
- **参数**:
  - `provider` (路径参数): 提供商标识符，必需
- **描述**: 接收提供商的事件通知，如eSIM状态变更、数据使用更新等
- **响应**: `200 OK`

  ```json
  {
    "success": true,
    "message": "Webhook已成功处理"
  }
  ```

## 提供商路由

这些路由需要用户认证。

### 获取提供商列表

- **URL**: `/api/v1/providers`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **响应**: `200 OK`

  ```json
  {
    "providers": [
      {
        "type": "esim_access",
        "name": "ESIMAccess",
        "capabilities": {
          "SMS": true,
          "TOP_UP": true
        }
      },
      {
        "type": "maya_mobile",
        "name": "MayaMobile",
        "capabilities": {
          "SMS": false,
          "TOP_UP": true
        }
      }
    ]
  }
  ```

### 获取套餐列表

- **URL**: `/api/v1/providers/{provider}/packages`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `provider` (路径参数): 提供商标识符，必需
  - `locationCode` (查询参数): 地区代码过滤，可选
  - `type` (查询参数): 套餐类型过滤，可选
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10，最大50
- **响应**: `200 OK`

  ```json
  {
    "packages": [
      {
        "id": "pkg_123",
        "name": "欧洲30天5GB",
        "description": "欧洲地区30天有效期5GB流量套餐",
        "price": 9900,
        "currency": "CNY",
        "dataVolume": **********,
        "validityDays": 30,
        "locationCodes": ["EU", "DE", "FR"],
        "supportsSMS": true,
        "dataType": "LIMITED",
        "networkTypes": ["4G", "5G"],
        "supportTopUp": true
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

### 获取套餐详情

- **URL**: `/api/v1/providers/{provider}/packages/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `provider` (路径参数): 提供商标识符，必需
  - `id` (路径参数): 套餐ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "pkg_123",
    "name": "欧洲30天5GB",
    "description": "欧洲地区30天有效期5GB流量套餐",
    "price": 9900,
    "currency": "CNY",
    "dataVolume": **********,
    "validityDays": 30,
    "locationCodes": ["EU", "DE", "FR"],
    "supportsSMS": true,
    "dataType": "LIMITED",
    "networkTypes": ["4G", "5G"],
    "supportTopUp": true
  }
  ```

### 获取支持区域

- **URL**: `/api/v1/providers/{provider}/regions`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `provider` (路径参数): 提供商标识符，必需
- **响应**: `200 OK`

  ```json
  {
    "regions": [
      {
        "code": "EU",
        "name": "欧洲",
        "type": 2,
        "subLocations": [
          {
            "code": "DE",
            "name": "德国"
          },
          {
            "code": "FR",
            "name": "法国"
          }
        ]
      }
    ]
  }
  ```

## eSIM路由

这些路由需要用户认证。

### 获取用户eSIM列表

- **URL**: `/api/v1/esims`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `page` (查询参数): 页码，默认1
  - `pageSize` (查询参数): 每页数量，默认10
  - `status` (查询参数): 状态过滤，可选值："ACTIVE", "INACTIVE", "EXPIRED"
- **响应**: `200 OK`

  ```json
  {
    "esims": [
      {
        "id": "esim_123",
        "iccid": "8988228801234567890",
        "status": "ACTIVE",
        "activationCode": "LPA:1$...",
        "qrCodeURL": "https://example.com/qr/123",
        "dataVolume": **********,
        "usedData": **********,
        "validityDays": 30,
        "expiryTime": "2023-02-01T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 5,
      "page": 1,
      "pageSize": 10
    }
  }
  ```

### 获取eSIM详情

- **URL**: `/api/v1/esims/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): eSIM ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "esim_123",
    "iccid": "8988228801234567890",
    "status": "ACTIVE",
    "activationCode": "LPA:1$...",
    "qrCodeURL": "https://example.com/qr/123",
    "dataVolume": **********,
    "usedData": **********,
    "validityDays": 30,
    "expiryTime": "2023-02-01T00:00:00Z",
    "sms": {
      "supported": true,
      "msisdn": "+447123456789",
      "apiOnly": true
    }
  }
  ```

### 更新eSIM状态

- **URL**: `/api/v1/esims/{id}/status`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): eSIM ID，必需
- **请求体**:
  ```json
  {
    "status": "SUSPENDED",
    "reason": "用户请求暂停服务"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "esim_123",
    "status": "SUSPENDED",
    "message": "eSIM已成功暂停",
    "updatedAt": "2023-01-15T10:30:00Z"
  }
  ```

### 充值eSIM数据

- **URL**: `/api/v1/esims/{id}/data`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): eSIM ID，必需
- **请求体**:

  ```json
  {
    "packageCode": "pkg_456",
    "paymentMethod": "CREDIT"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "esim_123",
    "transactionId": "txn_789",
    "expiredTime": "2023-03-01T00:00:00Z",
    "totalVolume": **********0,
    "totalDuration": 30,
    "message": "eSIM充值成功",
    "updatedAt": "2023-01-15T11:00:00Z"
  }
  ```

### 发送短信

- **URL**: `/api/v1/esims/{id}/messages`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): eSIM ID，必需
- **请求体**:

  ```json
  {
    "phoneNumber": "+8613800138000",
    "message": "您好，这是一条测试短信"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "esim_123",
    "smsId": "sms_456",
    "statusCode": "200",
    "status": "SENT",
    "message": "短信发送成功",
    "sentAt": "2023-01-15T11:30:00Z"
  }
  ```

### 获取eSIM用量

- **URL**: `/api/v1/esims/{id}/usage`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): eSIM ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "esim_123",
    "dataUsage": **********,
    "totalData": **********,
    "usagePercentage": 20,
    "lastUpdateTime": "2023-01-15T10:00:00Z"
  }
  ```

## 订单路由

这些路由需要用户认证。

### 创建订单

- **URL**: `/api/v1/orders`
- **方法**: `POST`
- **状态**: 已实现
- **认证**: 需要用户认证
- **请求体**:

  ```json
  {
    "providerType": "esim_access",
    "packageId": "pkg_123",
    "quantity": 1,
    "promotionCode": "WELCOME10"
  }
  ```
- **响应**: `201 Created`

  ```json
  {
    "id": "order_123",
    "providerType": "esim_access",
    "status": "PROCESSING",
    "totalAmount": 8910,
    "currency": "CNY",
    "items": [
      {
        "packageId": "pkg_123",
        "packageName": "欧洲30天5GB",
        "quantity": 1,
        "unitPrice": 9900,
        "currency": "CNY",
        "dataVolume": **********,
        "validityDays": 30
      }
    ],
    "createdAt": "2023-01-15T12:00:00Z"
  }
  ```

### 获取订单详情

- **URL**: `/api/v1/orders/{id}`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): 订单ID，必需
- **响应**: `200 OK`

  ```json
  {
    "id": "order_123",
    "providerType": "esim_access",
    "transactionId": "txn_456",
    "providerOrderNo": "ext_789",
    "status": "COMPLETED",
    "totalAmount": 8910,
    "currency": "CNY",
    "items": [
      {
        "packageId": "pkg_123",
        "packageName": "欧洲30天5GB",
        "quantity": 1,
        "unitPrice": 9900,
        "currency": "CNY",
        "dataVolume": **********,
        "validityDays": 30
      }
    ],
    "esims": [
      {
        "iccid": "8988228801234567890",
        "esimTranNo": "esim_123",
        "status": "ACTIVE",
        "activationUrl": "LPA:1$...",
        "qrCodeUrl": "https://example.com/qr/123",
        "dataVolume": **********,
        "validityDays": 30,
        "expiryDate": "2023-02-15T00:00:00Z"
      }
    ],
    "createdAt": "2023-01-15T12:00:00Z",
    "updatedAt": "2023-01-15T12:05:00Z",
    "completedAt": "2023-01-15T12:05:00Z",
    "promotionId": "promo_123"
  }
  ```

### 获取订单列表

- **URL**: `/api/v1/orders`
- **方法**: `GET`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `page`: 页码，默认1
  - `pageSize`: 每页数量，默认10，最大50
  - `startDate`: 开始日期过滤，格式：YYYY-MM-DD
  - `endDate`: 结束日期过滤，格式：YYYY-MM-DD
- **响应**: `200 OK`

  ```json
  {
    "orders": [
      {
        "id": "order_123",
        "providerType": "esim_access",
        "status": "COMPLETED",
        "totalAmount": 8910,
        "currency": "CNY",
        "createdAt": "2023-01-15T12:00:00Z",
        "completedAt": "2023-01-15T12:05:00Z",
        "esimCount": 1,
        "esimStatus": {
          "ACTIVE": 1
        }
      }
    ],
    "pagination": {
      "total": 10,
      "page": 1,
      "pageSize": 10,
      "startDate": "2023-01-01",
      "endDate": "2023-01-31"
    }
  }
  ```

### 更新订单状态

- **URL**: `/api/v1/orders/{id}/status`
- **方法**: `PUT`
- **状态**: 已实现
- **认证**: 需要用户认证
- **参数**:
  - `id` (路径参数): 订单ID
- **请求体**:
  ```json
  {
    "status": "CANCELLED",
    "reason": "用户请求取消"
  }
  ```
- **响应**: `200 OK`

  ```json
  {
    "id": "order_123",
    "status": "CANCELLED",
    "message": "订单已成功取消",
    "updatedAt": "2023-01-15T13:00:00Z"
  }
  ```