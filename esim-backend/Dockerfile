FROM golang:1.22 AS builder

WORKDIR /app

# 复制go.mod和go.sum
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -o /app/esim-api ./cmd/api

FROM alpine:3.18

WORKDIR /app

# 安装必要的依赖
RUN apk --no-cache add ca-certificates tzdata

# 从构建阶段复制可执行文件
COPY --from=builder /app/esim-api /app/
COPY --from=builder /app/config /app/config

# 创建非root用户
RUN adduser -D -g '' appuser
USER appuser

# 暴露端口
EXPOSE 8080

# 设置时区
ENV TZ=UTC

# 设置命令
ENTRYPOINT ["/app/esim-api"]