package esim

import (
	"fmt"
)

// ESIMError 统一错误结构
type ESIMError struct {
	Code             string
	Message          string
	HTTPStatus       int
	ProviderResponse interface{} // 原始响应
	RequestID        string
}

// 实现error接口
func (e *ESIMError) Error() string {
	return fmt.Sprintf("[%s] %s (HTTP %d, RequestID: %s)",
		e.Code, e.Message, e.HTTPStatus, e.RequestID)
}

// 错误码常量
const (
	// 通用错误
	ErrInvalidParams  = "INVALID_PARAMS"
	ErrNotFound       = "NOT_FOUND"
	ErrAuthFailed     = "AUTH_FAILED"
	ErrRateLimited    = "RATE_LIMITED"
	ErrServerError    = "SERVER_ERROR"
	ErrNotImplemented = "NOT_IMPLEMENTED"
	ErrTimeout        = "TIMEOUT"

	// 业务错误
	ErrInsufficientBalance = "INSUFFICIENT_BALANCE"
	ErrInvalidStatus       = "INVALID_STATUS"
	ErrESIMNotAvailable    = "ESIM_NOT_AVAILABLE"
	ErrOperationNotAllowed = "OPERATION_NOT_ALLOWED"

	// 提供商特定错误
	ErrProviderSpecific = "PROVIDER_SPECIFIC"
)

// HTTP状态码映射
var errorHTTPStatusMap = map[string]int{
	ErrInvalidParams:       400,
	ErrAuthFailed:          401,
	ErrNotFound:            404,
	ErrRateLimited:         429,
	ErrServerError:         500,
	ErrNotImplemented:      501,
	ErrInsufficientBalance: 402,
	ErrInvalidStatus:       400,
	ErrESIMNotAvailable:    404,
	ErrOperationNotAllowed: 403,
	ErrProviderSpecific:    400,
}

// NewESIMError 创建新的ESIMError
func NewESIMError(code, message string, providerResponse interface{}, requestID string) *ESIMError {
	httpStatus, ok := errorHTTPStatusMap[code]
	if !ok {
		httpStatus = 500
	}

	return &ESIMError{
		Code:             code,
		Message:          message,
		HTTPStatus:       httpStatus,
		ProviderResponse: providerResponse,
		RequestID:        requestID,
	}
}

// IsNotFound 判断是否是资源不存在错误
func IsNotFound(err error) bool {
	if esimErr, ok := err.(*ESIMError); ok {
		return esimErr.Code == ErrNotFound
	}
	return false
}

// IsAuthError 判断是否是认证错误
func IsAuthError(err error) bool {
	if esimErr, ok := err.(*ESIMError); ok {
		return esimErr.Code == ErrAuthFailed
	}
	return false
}

// IsInsufficientBalance 判断是否是余额不足错误
func IsInsufficientBalance(err error) bool {
	if esimErr, ok := err.(*ESIMError); ok {
		return esimErr.Code == ErrInsufficientBalance
	}
	return false
}
