package esim

import (
	"context"
	"time"
)

// ESIMProviderInterface 定义了eSIM服务提供商需要实现的所有方法
// 不同的提供商通过实现这个接口来提供统一的eSIM功能
type ESIMProviderInterface interface {
	// 提供商元数据
	GetProviderInfo(ctx context.Context) ProviderInfo

	// 套餐相关
	ListPackages(ctx context.Context, params PackageQueryParams) ([]Package, *PaginationResult, error)
	GetPackageDetails(ctx context.Context, packageId string) (*Package, error)

	// eSIM 管理
	CreateESIM(ctx context.Context, order *ESIMOrder) (*ESIMOrderResult, error)
	GetESIMDetails(ctx context.Context, esimID ESIMIdentifier) (*ESIM, error)
	ListESIMs(ctx context.Context, params ESIMQueryParams) (*ESIMListResult, error)

	// eSIM 状态管理
	CancelESIM(ctx context.Context, esimID ESIMIdentifier) error
	SuspendESIM(ctx context.Context, esimID ESIMIdentifier) error
	ResumeESIM(ctx context.Context, esimID ESIMIdentifier) error
	RevokeESIM(ctx context.Context, esimID ESIMIdentifier) error

	// 充值相关
	TopUpESIM(ctx context.Context, params TopUpParams) (*TopUpResult, error)

	// SMS 服务
	SendSMS(ctx context.Context, params SMSParams) error

	// 用量查询
	GetESIMUsage(ctx context.Context, esimID ESIMIdentifier) (*ESIMUsage, error)

	// 账户管理
	GetAccountBalance(ctx context.Context) (*AccountBalance, error)

	// 区域支持
	ListSupportedRegions(ctx context.Context) ([]Region, error)

	// Webhook 处理
	ProcessWebhookEvent(ctx context.Context, payload []byte, headers map[string][]string) (*WebhookEvent, error)
}

// ESIMProviderFactoryInterface 负责创建和管理Provider实例
type ESIMProviderFactoryInterface interface {
	// 获取所有注册的提供商
	GetProviders(ctx context.Context) []ProviderInfo

	// 通过类型获取提供商实例
	GetProvider(ctx context.Context, providerType string) (ESIMProviderInterface, error)

	// 注册新提供商
	RegisterProvider(ctx context.Context, provider ESIMProviderInterface) error
}

// ProviderConfigManagerInterface 管理提供商配置
type ProviderConfigManagerInterface interface {
	// 获取提供商配置
	GetConfig(ctx context.Context, providerType string) (*ProviderConfig, error)

	// 更新提供商配置
	UpdateConfig(ctx context.Context, config *ProviderConfig) error

	// 启用/禁用提供商
	SetProviderEnabled(ctx context.Context, providerType string, enabled bool) error

	// ListConfigs 获取所有提供商配置
	ListConfigs(ctx context.Context) ([]*ProviderConfig, error)
}

// AsyncTaskTracker 管理异步任务
type AsyncTaskTracker interface {
	// 注册异步任务
	RegisterTask(ctx context.Context, taskType string, referenceID string) (string, error)

	// 更新任务状态
	UpdateTaskStatus(ctx context.Context, taskID string, status string, result interface{}) error

	// 获取任务状态
	GetTaskStatus(ctx context.Context, taskID string) (*TaskStatus, error)

	// 异步任务轮询
	PollForCompletion(ctx context.Context, taskID string, timeout time.Duration) (*TaskStatus, error)
}

// WebhookHandler 处理来自提供商的Webhook事件
type WebhookHandler interface {
	// 注册 Webhook 处理函数
	RegisterEventHandler(eventType string, handler func(ctx context.Context, event *WebhookEvent) error)

	// 处理 Webhook 请求
	HandleWebhook(ctx context.Context, providerType string, payload []byte, headers map[string][]string) error

	// 获取 Webhook 配置
	GetWebhookConfig(ctx context.Context, providerType string) (*WebhookConfig, error)
}

// PackageInfo 套餐信息
type PackageInfo struct {
	ID           string   `json:"id"`           // 套餐ID或SKU
	Name         string   `json:"name"`         // 套餐名称
	Description  string   `json:"description"`  // 套餐描述
	Countries    []string `json:"countries"`    // 支持的国家列表
	DataAmount   int64    `json:"dataAmount"`   // 数据量(MB)
	ValidityDays int      `json:"validityDays"` // 有效天数
	Price        float64  `json:"price"`        // 价格
	Currency     string   `json:"currency"`     // 货币
	NetworkTypes []string `json:"networkTypes"` // 支持的网络类型
	SupportSMS   bool     `json:"supportSMS"`   // 是否支持短信
	SupportTopUp bool     `json:"supportTopUp"` // 是否支持充值
}

// OrderInfo 订单信息
type OrderInfo struct {
	OrderID       string    `json:"orderId"`                 // 订单ID
	ProviderRefID string    `json:"providerRefId,omitempty"` // 提供商参考ID
	Status        string    `json:"status"`                  // 订单状态：PENDING, COMPLETED, FAILED
	CreatedAt     time.Time `json:"createdAt"`               // 创建时间
	CompletedAt   time.Time `json:"completedAt,omitempty"`   // 完成时间
	ESIMs         []ESIM    `json:"esims,omitempty"`         // 订单中的eSIM
}

// UsageInfo 使用情况信息
type UsageInfo struct {
	ICCID            string    `json:"iccid"`                      // ICCID
	DataUsed         int64     `json:"dataUsed"`                   // 已使用数据量(MB)
	DataTotal        int64     `json:"dataTotal"`                  // 总数据量(MB)
	DataRemaining    int64     `json:"dataRemaining"`              // 剩余数据量(MB)
	ValidDays        int       `json:"validDays"`                  // 有效天数
	DaysUsed         int       `json:"daysUsed"`                   // 已使用天数
	DaysRemaining    int       `json:"daysRemaining"`              // 剩余天数
	CurrentProvider  string    `json:"currentProvider,omitempty"`  // 当前网络提供商
	CurrentCountry   string    `json:"currentCountry,omitempty"`   // 当前国家
	LastUpdateTime   time.Time `json:"lastUpdateTime"`             // 最后更新时间
	ConnectionStatus string    `json:"connectionStatus,omitempty"` // 连接状态
}

// OrderRequest 订单请求
type OrderRequest struct {
	PackageID   string                 `json:"packageId"`             // 套餐ID
	Quantity    int                    `json:"quantity"`              // 数量
	ReferenceNo string                 `json:"referenceNo,omitempty"` // 参考编号
	Countries   []string               `json:"countries,omitempty"`   // 指定国家（如果支持）
	Metadata    map[string]interface{} `json:"metadata,omitempty"`    // 元数据
}

// TopUpRequest 充值请求
type TopUpRequest struct {
	ICCID       string `json:"iccid"`                 // ICCID
	PackageID   string `json:"packageId"`             // 套餐ID
	ReferenceNo string `json:"referenceNo,omitempty"` // 参考编号
}

// SMSRequest 短信请求
type SMSRequest struct {
	ICCID   string `json:"iccid"`          // ICCID
	Message string `json:"message"`        // 短信内容
	From    string `json:"from,omitempty"` // 发送者（如果支持）
}

// BalanceInfo 余额信息
type BalanceInfo struct {
	Balance     float64 `json:"balance"`     // 余额
	Currency    string  `json:"currency"`    // 货币
	CreditLimit float64 `json:"creditLimit"` // 信用额度
}
