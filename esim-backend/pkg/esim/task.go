package esim

import (
	"context"
	"time"
)

// TaskTracker 任务追踪器接口
type TaskTracker interface {
	// RegisterTask 注册新的异步任务
	RegisterTask(ctx context.Context, taskType string, referenceID string) (string, error)

	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(ctx context.Context, taskID string, status string, result interface{}) error

	// GetTaskStatus 获取任务状态
	GetTaskStatus(ctx context.Context, taskID string) (*TaskStatus, error)

	// PollForCompletion 轮询任务完成
	PollForCompletion(ctx context.Context, taskID string, timeout time.Duration) (*TaskStatus, error)
}
