package esim

import (
	"time"
)

// ProviderInfo 提供商基本信息
type ProviderInfo struct {
	Type         string          // 提供商类型标识符
	Name         string          // 提供商名称
	BaseURL      string          // API基础URL
	Capabilities map[string]bool // 支持的功能特性
}

// ProviderConfig 提供商配置信息
type ProviderConfig struct {
	Type             string
	Enabled          bool
	APIKey           string
	APISecret        string
	BaseURL          string
	WebhookConfig    WebhookConfig
	RateLimitPerSec  int
	TimeoutSeconds   int
	RetryConfig      RetryConfig
	AdditionalConfig map[string]interface{}
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries       int
	RetryInterval    time.Duration
	MaxRetryInterval time.Duration
}

// WebhookConfig Webhook配置
type WebhookConfig struct {
	URL          string
	Secret       string
	ProviderType string
	EventTypes   []string
}

// ESIMIdentifier eSIM标识统一结构，支持不同厂商的标识方式
type ESIMIdentifier struct {
	ICCID      string // eSIM的ICCID
	ESIMTranNo string // eSIM交易号
	OrderNo    string // 订单号
}

// PackageQueryParams 套餐查询参数
type PackageQueryParams struct {
	LocationCode string // 国家/地区代码
	Type         string // BASE 或 TOPUP
	PackageCode  string // 套餐代码
	Slug         string // 套餐别名
	ICCID        string // 用于查询可用充值计划
	Region       string // 区域过滤
	Country      string // 国家过滤
	PageSize     int    // 分页大小
	PageNum      int    // 页码
}

// Package 套餐信息
type Package struct {
	ID               string
	Name             string
	Description      string
	Price            int64
	Currency         string
	DataVolume       int64                  // 数据量(字节)
	ValidityDays     int                    // 有效期(天)
	LocationCodes    []string               // 支持的国家/地区代码
	SupportsSMS      bool                   // 是否支持短信
	DataType         string                 // 固定流量或无限流量
	NetworkTypes     []string               // 网络类型(4G/5G等)
	SupportTopUp     bool                   // 是否支持充值
	ProviderSpecific map[string]interface{} // 提供商特定数据
}

// ESIMOrder eSIM订单
type ESIMOrder struct {
	TransactionID    string
	PackageID        string                 // packageCode或productId
	Count            int                    // 数量
	CustomerID       string                 // 可选，客户标识
	ProviderSpecific map[string]interface{} // 提供商特定参数
}

// ESIMOrderResult eSIM订单结果
type ESIMOrderResult struct {
	OrderNo       string
	TransactionID string
	ESIMs         []ESIM // 已分配的eSIM列表
	IsPending     bool   // 是否需要异步查询
	TaskID        string // 异步任务ID
}

// ESIM eSIM详情
type ESIM struct {
	Identifier       ESIMIdentifier
	Status           ESIMStatus
	ActivationCode   string                 // 激活码
	QRCodeURL        string                 // 二维码URL
	DataVolume       int64                  // 总数据量(字节)
	UsedData         int64                  // 已使用数据(字节)
	ValidityDays     int                    // 有效期(天)
	ExpiryTime       time.Time              // 到期时间
	ActiveType       int                    // 激活类型
	EID              string                 // EID，安装后才有
	Packages         []Package              // 关联的套餐
	PIN              string                 // 可选
	PUK              string                 // 可选
	APN              string                 // 可选
	SMS              SMSCapability          // 短信能力
	ProviderSpecific map[string]interface{} // 提供商特定数据
}

// ESIMStatus eSIM状态
type ESIMStatus struct {
	Code            string // 状态代码
	SMDPStatus      string // SM-DP+状态
	InstalledStatus string // 安装状态
	Description     string // 状态描述
}

// SMSCapability SMS能力
type SMSCapability struct {
	Supported bool
	MSISDN    string // 号码
	APIOnly   bool   // 是否仅支持API发送
}

// ESIMQueryParams eSIM查询参数
type ESIMQueryParams struct {
	OrderNo    string
	ICCID      string
	ESIMTranNo string
	StartTime  time.Time
	EndTime    time.Time
	PageSize   int
	PageNum    int
	CustomerID string
}

// ESIMListResult eSIM列表结果
type ESIMListResult struct {
	ESIMs      []ESIM
	Pagination PaginationResult
}

// PaginationResult 分页结果
type PaginationResult struct {
	Total    int64
	PageSize int
	PageNum  int
}

// TopUpParams 充值参数
type TopUpParams struct {
	ESIMID        ESIMIdentifier
	PackageID     string
	TransactionID string
	Amount        int64 // 可选价格验证
}

// TopUpResult 充值结果
type TopUpResult struct {
	TransactionID string
	ExpiryTime    time.Time
	TotalVolume   int64
	OrderUsage    int64
}

// SMSParams SMS参数
type SMSParams struct {
	ESIMID  ESIMIdentifier
	Message string
}

// ESIMUsage eSIM用量
type ESIMUsage struct {
	DataUsage      int64
	TotalData      int64
	LastUpdateTime time.Time
}

// AccountBalance 账户余额
type AccountBalance struct {
	Balance        int64
	Currency       string
	HasOverdraft   bool
	OverdraftLimit int64
}

// Region 区域信息
type Region struct {
	Code         string
	Name         string
	Type         int // 1:单国家 2:多国家
	SubLocations []SubLocation
}

// SubLocation 子区域信息
type SubLocation struct {
	Code string
	Name string
}

// WebhookEvent Webhook事件
type WebhookEvent struct {
	EventType      string // ORDER_STATUS, ESIM_STATUS, DATA_USAGE, VALIDITY_USAGE
	ProviderType   string // 提供商类型
	Timestamp      time.Time
	Payload        map[string]interface{} // 原始负载
	ESIMIdentifier ESIMIdentifier         // 相关eSIM标识
	Data           interface{}            // 根据类型解析的数据
}

// TaskStatus 异步任务状态
type TaskStatus struct {
	TaskID      string
	Type        string
	ReferenceID string
	Status      string // PENDING, PROCESSING, RETRYING, COMPLETED, FAILED
	Result      interface{}
	Error       *ESIMError
	Metadata    map[string]interface{} // 任务元数据，用于存储重试信息等
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

// 定义常量
const (
	// 任务状态
	TaskStatusPending    = "PENDING"
	TaskStatusProcessing = "PROCESSING"
	TaskStatusRetrying   = "RETRYING"
	TaskStatusCompleted  = "COMPLETED"
	TaskStatusFailed     = "FAILED"

	// 事件类型
	EventTypeOrderStatus   = "ORDER_STATUS"
	EventTypeESIMStatus    = "ESIM_STATUS"
	EventTypeDataUsage     = "DATA_USAGE"
	EventTypeValidityUsage = "VALIDITY_USAGE"

	// 功能特性标识
	CapSMS            = "SMS"
	CapTopUp          = "TOP_UP"
	CapCancel         = "CANCEL"
	CapSuspend        = "SUSPEND"
	CapResume         = "RESUME"
	CapRevoke         = "REVOKE"
	CapWebhook        = "WEBHOOK"
	CapMultiESIM      = "MULTI_ESIM"
	CapCustomerManage = "CUSTOMER_MANAGE"
)
