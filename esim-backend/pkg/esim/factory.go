package esim

import (
	"context"
	"fmt"
	"sync"
)

// DefaultProviderFactory 默认的提供商工厂实现
type DefaultProviderFactory struct {
	providers     map[string]ESIMProviderInterface
	providerInfos []ProviderInfo
	mu            sync.RWMutex
	configManager ProviderConfigManagerInterface
}

// NewDefaultProviderFactory 创建默认提供商工厂
func NewDefaultProviderFactory() *DefaultProviderFactory {
	return &DefaultProviderFactory{
		providers:     make(map[string]ESIMProviderInterface),
		providerInfos: []ProviderInfo{},
	}
}

// GetProviders 获取所有注册的提供商
func (f *DefaultProviderFactory) GetProviders(ctx context.Context) []ProviderInfo {
	f.mu.RLock()
	defer f.mu.RUnlock()

	return f.providerInfos
}

// GetProvider 通过类型获取提供商实例
func (f *DefaultProviderFactory) GetProvider(ctx context.Context, providerType string) (ESIMProviderInterface, error) {
	f.mu.RLock()
	provider, exists := f.providers[providerType]
	f.mu.RUnlock()

	if !exists {
		return nil, NewESIMError(
			ErrNotFound,
			fmt.Sprintf("Provider %s not found", providerType),
			nil,
			"",
		)
	}

	return provider, nil
}

// RegisterProvider 注册新提供商
func (f *DefaultProviderFactory) RegisterProvider(ctx context.Context, provider ESIMProviderInterface) error {
	if provider == nil {
		return NewESIMError(
			ErrInvalidParams,
			"Provider cannot be nil",
			nil,
			"",
		)
	}

	info := provider.GetProviderInfo(ctx)

	f.mu.Lock()
	defer f.mu.Unlock()

	// 检查是否已存在
	if _, exists := f.providers[info.Type]; exists {
		return NewESIMError(
			ErrInvalidParams,
			fmt.Sprintf("Provider %s already registered", info.Type),
			nil,
			"",
		)
	}

	// 注册提供商
	f.providers[info.Type] = provider
	f.providerInfos = append(f.providerInfos, info)

	return nil
}
