package logger

import (
	"os"
	"path/filepath"

	"vereal/letsesim/config"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// NewLogger 创建新的日志记录器
func NewLogger(cfg *config.LogConfig) (*zap.Logger, error) {
	// 解析日志级别
	var zapLevel zapcore.Level
	if err := zapLevel.UnmarshalText([]byte(cfg.Level)); err != nil {
		zapLevel = zapcore.InfoLevel
	}

	// 创建编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.TimeEncoderOfLayout(cfg.TimeFormat),
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 创建编码器
	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 创建日志写入器
	var writeSyncer zapcore.WriteSyncer
	if cfg.Output == "stdout" {
		writeSyncer = zapcore.AddSync(os.Stdout)
	} else if cfg.Output == "stderr" {
		writeSyncer = zapcore.AddSync(os.Stderr)
	} else {
		// 确保目录存在
		logDir := filepath.Dir(cfg.Output)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return nil, err
		}

		// 创建一个lumberjack日志轮转实例
		lumberjackLogger := &lumberjack.Logger{
			Filename:   cfg.Output,
			MaxSize:    cfg.MaxSize,    // 单个文件最大尺寸，单位MB
			MaxBackups: cfg.MaxBackups, // 最大保留旧文件数
			MaxAge:     cfg.MaxAge,     // 文件最大保留天数
			Compress:   cfg.Compress,   // 是否压缩
			LocalTime:  cfg.LocalTime,  // 使用本地时间
		}
		writeSyncer = zapcore.AddSync(lumberjackLogger)
	}

	// 创建核心
	core := zapcore.NewCore(
		encoder,
		writeSyncer,
		zapLevel,
	)

	// 创建日志记录器
	logger := zap.New(
		core,
		zap.AddCaller(),
		zap.AddCallerSkip(1),
		zap.AddStacktrace(zapcore.ErrorLevel),
	)

	return logger, nil
}
