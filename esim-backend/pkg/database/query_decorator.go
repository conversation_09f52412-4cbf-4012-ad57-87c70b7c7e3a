package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"vereal/letsesim/pkg/monitoring"
)

// QueryDecorator 查询装饰器，用于支持查询跟踪、缓存和指标收集
type QueryDecorator struct {
	DB *gorm.DB
}

// NewQueryDecorator 创建新的查询装饰器
func NewQueryDecorator(db *gorm.DB) *QueryDecorator {
	return &QueryDecorator{
		DB: db,
	}
}

// withTracing 启用GORM查询跟踪
func (d *QueryDecorator) withTracing(ctx context.Context, operation, table string) *gorm.DB {
	// 创建带有跟踪功能的新数据库会话
	tx := d.DB.WithContext(ctx).Session(&gorm.Session{
		Logger: d.DB.Logger.LogMode(logger.Info),
	})

	// 使用回调在查询前后收集指标
	callbacks := tx.Callback()

	// 测量查询持续时间的回调
	callbacks.Create().Before("gorm:create").Register("tracing:before_create", func(db *gorm.DB) {
		db.InstanceSet("start_time", time.Now())
	})

	callbacks.Create().After("gorm:create").Register("tracing:after_create", func(db *gorm.DB) {
		startTime, ok := db.InstanceGet("start_time")
		if !ok {
			return
		}
		duration := time.Since(startTime.(time.Time)).Seconds()
		monitoring.DatabaseQueryDuration.WithLabelValues("create", table).Observe(duration)
	})

	callbacks.Query().Before("gorm:query").Register("tracing:before_query", func(db *gorm.DB) {
		db.InstanceSet("start_time", time.Now())
	})

	callbacks.Query().After("gorm:query").Register("tracing:after_query", func(db *gorm.DB) {
		startTime, ok := db.InstanceGet("start_time")
		if !ok {
			return
		}
		duration := time.Since(startTime.(time.Time)).Seconds()
		monitoring.DatabaseQueryDuration.WithLabelValues("query", table).Observe(duration)
	})

	callbacks.Update().Before("gorm:update").Register("tracing:before_update", func(db *gorm.DB) {
		db.InstanceSet("start_time", time.Now())
	})

	callbacks.Update().After("gorm:update").Register("tracing:after_update", func(db *gorm.DB) {
		startTime, ok := db.InstanceGet("start_time")
		if !ok {
			return
		}
		duration := time.Since(startTime.(time.Time)).Seconds()
		monitoring.DatabaseQueryDuration.WithLabelValues("update", table).Observe(duration)
	})

	callbacks.Delete().Before("gorm:delete").Register("tracing:before_delete", func(db *gorm.DB) {
		db.InstanceSet("start_time", time.Now())
	})

	callbacks.Delete().After("gorm:delete").Register("tracing:after_delete", func(db *gorm.DB) {
		startTime, ok := db.InstanceGet("start_time")
		if !ok {
			return
		}
		duration := time.Since(startTime.(time.Time)).Seconds()
		monitoring.DatabaseQueryDuration.WithLabelValues("delete", table).Observe(duration)
	})

	return tx
}

// optimizedForFind 为查找操作优化查询
func (d *QueryDecorator) optimizedForFind(ctx context.Context, table string) *gorm.DB {
	// 查询优化示例，可根据具体需求调整:
	// 1. 启用查询缓存
	// 2. 设置合理的预加载策略
	// 3. 设置查询超时
	// 4. 添加索引提示
	tx := d.withTracing(ctx, "find", table)

	// 设置查询超时
	timeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return tx.WithContext(timeout)
}

// optimizedForCount 为计数操作优化查询
func (d *QueryDecorator) optimizedForCount(ctx context.Context, table string) *gorm.DB {
	tx := d.withTracing(ctx, "count", table)

	// 对计数查询的优化，例如：
	// 1. 不查询不必要的字段
	// 2. 使用索引
	return tx
}

// optimizedForCreate 为创建操作优化查询
func (d *QueryDecorator) optimizedForCreate(ctx context.Context, table string) *gorm.DB {
	return d.withTracing(ctx, "create", table)
}

// optimizedForUpdate 为更新操作优化查询
func (d *QueryDecorator) optimizedForUpdate(ctx context.Context, table string) *gorm.DB {
	return d.withTracing(ctx, "update", table)
}

// optimizedForDelete 为删除操作优化查询
func (d *QueryDecorator) optimizedForDelete(ctx context.Context, table string) *gorm.DB {
	return d.withTracing(ctx, "delete", table)
}

// Transaction 提供事务支持的工具函数，支持指标收集
func (d *QueryDecorator) Transaction(ctx context.Context, txOpts *sql.TxOptions, fn func(tx *gorm.DB) error) error {
	start := time.Now()
	err := ExecWithTransaction(ctx, d.DB, txOpts, fn)
	duration := time.Since(start).Seconds()
	monitoring.DatabaseQueryDuration.WithLabelValues("transaction", "all").Observe(duration)
	return err
}

// FindWithCache 使用缓存的查询
// entity: 要查询的实体类型
// cacheKey: 缓存键
// cacheTTL: 缓存有效期
// query: 查询函数
func FindWithCache(ctx context.Context, cacheProvider CacheProvider, entity interface{}, cacheKey string, cacheTTL time.Duration, query func() error) error {
	// 如果没有提供缓存，直接执行查询
	if cacheProvider == nil {
		return query()
	}

	// 尝试从缓存获取
	found, err := cacheProvider.Get(ctx, cacheKey, entity)
	if err != nil {
		return fmt.Errorf("cache error: %w", err)
	}

	// 缓存命中，直接返回
	if found {
		return nil
	}

	// 缓存未命中，执行查询
	if err := query(); err != nil {
		return err
	}

	// 将查询结果存入缓存
	if err := cacheProvider.Set(ctx, cacheKey, entity, cacheTTL); err != nil {
		// 缓存设置错误不应中断流程，仅记录错误
		fmt.Printf("Failed to set cache: %v\n", err)
	}

	return nil
}

// CacheProvider 缓存提供者接口
type CacheProvider interface {
	Get(ctx context.Context, key string, dest interface{}) (bool, error)
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
}

// OptimizationHints 用于存储查询优化提示
type OptimizationHints struct {
	ForceIndex       string   // 强制使用索引
	IgnoreIndex      []string // 忽略索引
	MaxExecutionTime int      // 最大执行时间（毫秒）
	UseReplica       bool     // 使用副本
}

// ApplyHints 将优化提示应用到查询
func ApplyHints(db *gorm.DB, hints *OptimizationHints) *gorm.DB {
	if hints == nil {
		return db
	}

	tx := db.Session(&gorm.Session{})

	if hints.ForceIndex != "" {
		tx = tx.Clauses(gorm.Expr(fmt.Sprintf("FORCE INDEX(%s)", hints.ForceIndex)))
	}

	if len(hints.IgnoreIndex) > 0 {
		ignoreStr := "IGNORE INDEX("
		for i, idx := range hints.IgnoreIndex {
			if i > 0 {
				ignoreStr += ","
			}
			ignoreStr += idx
		}
		ignoreStr += ")"
		tx = tx.Clauses(gorm.Expr(ignoreStr))
	}

	if hints.MaxExecutionTime > 0 {
		tx = tx.Clauses(gorm.Expr(fmt.Sprintf("/*+ MAX_EXECUTION_TIME(%d) */", hints.MaxExecutionTime)))
	}

	// 在实际部署中，如果使用读写分离，可以在这里添加路由到副本的逻辑

	return tx
}
