package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"vereal/letsesim/config"
)

// NewDB 连接到数据库
func NewDB(cfg *config.DatabaseConfig) (*gorm.DB, error) {
	// 设置GORM配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		PrepareStmt:          true,
		AllowGlobalUpdate:    false,
		DisableAutomaticPing: false,
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(cfg.GetDSN()), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)
	// 设置连接最大空闲时间，默认为30分钟
	sqlDB.SetConnMaxIdleTime(30 * time.Minute)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(ctx); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

// WithTransaction 提供事务支持的工具函数
func WithTransaction(db *gorm.DB, fn func(tx *gorm.DB) error) error {
	return db.Transaction(fn)
}

// ExecWithTransaction 执行事务操作的工具函数
// 参数:
// - ctx: 上下文
// - db: 数据库连接
// - txOpts: 事务选项，可选
// - fn: 事务函数
func ExecWithTransaction(ctx context.Context, db *gorm.DB, txOpts *sql.TxOptions, fn func(tx *gorm.DB) error) error {
	tx := db.WithContext(ctx)
	if txOpts != nil {
		tx = tx.Begin(txOpts)
	} else {
		tx = tx.Begin()
	}

	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // 重新抛出panic
		}
	}()

	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// IsRecordNotFoundError 判断是否为记录未找到错误
func IsRecordNotFoundError(err error) bool {
	return err == gorm.ErrRecordNotFound
}
