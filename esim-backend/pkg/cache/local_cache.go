package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"vereal/letsesim/pkg/monitoring"

	lru "github.com/hashicorp/golang-lru/v2"
)

// cacheItem 本地缓存项
type cacheItem struct {
	Value      interface{} // 缓存值
	Expiration time.Time   // 过期时间
}

// isExpired 检查缓存项是否已过期
func (i *cacheItem) isExpired() bool {
	return !i.Expiration.IsZero() && time.Now().After(i.Expiration)
}

// localCache 基于内存的本地缓存实现
type localCache struct {
	cache  *lru.Cache[string, *cacheItem] // LRU缓存
	mu     sync.RWMutex                   // 用于并发控制的互斥锁
	config *Config                        // 缓存配置
}

// NewLocalCache 创建新的本地内存缓存
func NewLocalCache(config *Config) (Cache, error) {
	if config == nil {
		config = &Config{
			Enabled:      true,
			DefaultTTL:   30 * time.Minute,
			LocalMaxSize: 1000,
		}
	}

	lruCache, err := lru.New[string, *cacheItem](config.LocalMaxSize)
	if err != nil {
		return nil, fmt.Errorf("failed to create LRU cache: %w", err)
	}

	return &localCache{
		cache:  lruCache,
		config: config,
	}, nil
}

// Get 从本地缓存获取数据
func (c *localCache) Get(ctx context.Context, key string, dest interface{}) (bool, error) {
	if !c.config.Enabled {
		return false, nil
	}

	c.mu.RLock()
	item, exists := c.cache.Get(key)
	c.mu.RUnlock()

	if !exists {
		monitoring.CacheMisses.WithLabelValues(CacheTypeLocal).Inc()
		return false, nil
	}

	// 检查是否过期
	if item.isExpired() {
		c.mu.Lock()
		c.cache.Remove(key)
		c.mu.Unlock()
		monitoring.CacheMisses.WithLabelValues(CacheTypeLocal).Inc()
		return false, nil
	}

	// 序列化/反序列化以确保返回值的副本
	data, err := json.Marshal(item.Value)
	if err != nil {
		return false, fmt.Errorf("failed to marshal cached item: %w", err)
	}

	if err := json.Unmarshal(data, dest); err != nil {
		return false, fmt.Errorf("failed to unmarshal to destination: %w", err)
	}

	monitoring.CacheHits.WithLabelValues(CacheTypeLocal).Inc()
	return true, nil
}

// Set 设置本地缓存数据
func (c *localCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	if !c.config.Enabled {
		return nil
	}

	// 计算过期时间
	var expiration time.Time
	if ttl > 0 {
		expiration = time.Now().Add(ttl)
	} else if c.config.DefaultTTL > 0 {
		expiration = time.Now().Add(c.config.DefaultTTL)
	}

	item := &cacheItem{
		Value:      value,
		Expiration: expiration,
	}

	c.mu.Lock()
	c.cache.Add(key, item)
	c.mu.Unlock()

	return nil
}

// Delete 删除本地缓存数据
func (c *localCache) Delete(ctx context.Context, key string) error {
	if !c.config.Enabled {
		return nil
	}

	c.mu.Lock()
	c.cache.Remove(key)
	c.mu.Unlock()

	return nil
}

// DeletePattern 根据模式删除本地缓存
// 注意：本地缓存不支持模式删除，此实现是基于前缀匹配的简单实现
func (c *localCache) DeletePattern(ctx context.Context, pattern string) error {
	if !c.config.Enabled {
		return nil
	}

	// 由于LRU缓存不支持按模式查询，我们需要遍历所有键
	// 这在大型缓存中可能效率不高
	keysToDelete := make([]string, 0)

	c.mu.RLock()
	for _, key := range c.cache.Keys() {
		// 这里我们使用简单的前缀匹配，这不是真正的模式匹配
		// 在生产环境中可能需要更复杂的模式匹配实现
		if len(pattern) > 0 && pattern[len(pattern)-1] == '*' {
			prefix := pattern[:len(pattern)-1]
			if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
				keysToDelete = append(keysToDelete, key)
			}
		} else if key == pattern {
			keysToDelete = append(keysToDelete, key)
		}
	}
	c.mu.RUnlock()

	c.mu.Lock()
	for _, key := range keysToDelete {
		c.cache.Remove(key)
	}
	c.mu.Unlock()

	return nil
}

// Clear 清空本地缓存
func (c *localCache) Clear(ctx context.Context) error {
	if !c.config.Enabled {
		return nil
	}

	c.mu.Lock()
	c.cache.Purge()
	c.mu.Unlock()

	return nil
}

// Type 返回缓存类型
func (c *localCache) Type() string {
	return CacheTypeLocal
}

// hybridCache 混合缓存实现 (本地 + Redis)
type hybridCache struct {
	local  Cache
	remote Cache
	config *Config
}

// NewHybridCache 创建新的混合缓存
func NewHybridCache(local, remote Cache, config *Config) Cache {
	return &hybridCache{
		local:  local,
		remote: remote,
		config: config,
	}
}

// Get 从混合缓存获取数据
func (c *hybridCache) Get(ctx context.Context, key string, dest interface{}) (bool, error) {
	if !c.config.Enabled {
		return false, nil
	}

	// 首先尝试从本地缓存获取
	found, err := c.local.Get(ctx, key, dest)
	if err != nil {
		// 本地缓存错误不应妨碍从远程缓存获取
		// 仅记录错误
		fmt.Printf("Local cache error: %v\n", err)
	}

	if found {
		return true, nil
	}

	// 如果本地缓存未命中，尝试从远程缓存获取
	found, err = c.remote.Get(ctx, key, dest)
	if err != nil {
		return false, err
	}

	if found {
		// 从远程缓存获取成功，将数据填充到本地缓存
		if err := c.local.Set(ctx, key, dest, 0); err != nil {
			// 本地缓存设置错误不应中断流程
			// 仅记录错误
			fmt.Printf("Failed to set local cache: %v\n", err)
		}
		return true, nil
	}

	return false, nil
}

// Set 设置混合缓存数据
func (c *hybridCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	if !c.config.Enabled {
		return nil
	}

	// 同时设置本地和远程缓存
	if err := c.local.Set(ctx, key, value, ttl); err != nil {
		return fmt.Errorf("failed to set local cache: %w", err)
	}

	if err := c.remote.Set(ctx, key, value, ttl); err != nil {
		return fmt.Errorf("failed to set remote cache: %w", err)
	}

	return nil
}

// Delete 删除混合缓存数据
func (c *hybridCache) Delete(ctx context.Context, key string) error {
	if !c.config.Enabled {
		return nil
	}

	// 同时从本地和远程缓存删除
	if err := c.local.Delete(ctx, key); err != nil {
		return fmt.Errorf("failed to delete from local cache: %w", err)
	}

	if err := c.remote.Delete(ctx, key); err != nil {
		return fmt.Errorf("failed to delete from remote cache: %w", err)
	}

	return nil
}

// DeletePattern 根据模式删除混合缓存
func (c *hybridCache) DeletePattern(ctx context.Context, pattern string) error {
	if !c.config.Enabled {
		return nil
	}

	// 同时从本地和远程缓存删除
	if err := c.local.DeletePattern(ctx, pattern); err != nil {
		return fmt.Errorf("failed to delete pattern from local cache: %w", err)
	}

	if err := c.remote.DeletePattern(ctx, pattern); err != nil {
		return fmt.Errorf("failed to delete pattern from remote cache: %w", err)
	}

	return nil
}

// Clear 清空混合缓存
func (c *hybridCache) Clear(ctx context.Context) error {
	if !c.config.Enabled {
		return nil
	}

	// 同时清空本地和远程缓存
	if err := c.local.Clear(ctx); err != nil {
		return fmt.Errorf("failed to clear local cache: %w", err)
	}

	if err := c.remote.Clear(ctx); err != nil {
		return fmt.Errorf("failed to clear remote cache: %w", err)
	}

	return nil
}

// Type 返回缓存类型
func (c *hybridCache) Type() string {
	return CacheTypeHybrid
}
