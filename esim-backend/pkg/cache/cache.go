package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"vereal/letsesim/pkg/monitoring"
	"vereal/letsesim/pkg/redis"
)

// Config 缓存配置
type Config struct {
	Enabled      bool          // 是否启用缓存
	DefaultTTL   time.Duration // 默认过期时间
	LocalMaxSize int           // 本地缓存最大项数
}

// CacheType 缓存类型
const (
	CacheTypeLocal  = "local"  // 本地内存缓存
	CacheTypeRedis  = "redis"  // Redis缓存
	CacheTypeHybrid = "hybrid" // 混合缓存
)

// KeyGenerator 用于生成缓存键的函数类型
type KeyGenerator func(args ...interface{}) string

// Cache 缓存接口
type Cache interface {
	// Get 从缓存中获取数据
	Get(ctx context.Context, key string, dest interface{}) (found bool, err error)

	// Set 设置缓存数据
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error

	// Delete 删除缓存数据
	Delete(ctx context.Context, key string) error

	// DeletePattern 根据模式删除多个缓存
	DeletePattern(ctx context.Context, pattern string) error

	// Clear 清空所有缓存
	Clear(ctx context.Context) error

	// Type 返回缓存类型
	Type() string
}

// redisCache Redis缓存实现
type redisCache struct {
	client *redis.Client
	config *Config
}

// NewRedisCache 创建新的Redis缓存
func NewRedisCache(client *redis.Client, config *Config) Cache {
	return &redisCache{
		client: client,
		config: config,
	}
}

// Get 从Redis缓存获取数据
func (c *redisCache) Get(ctx context.Context, key string, dest interface{}) (bool, error) {
	if !c.config.Enabled {
		return false, nil
	}

	exists, err := c.client.Exists(ctx, key)
	if err != nil {
		return false, fmt.Errorf("failed to check key existence: %w", err)
	}

	if !exists {
		monitoring.CacheMisses.WithLabelValues(CacheTypeRedis).Inc()
		return false, nil
	}

	err = c.client.GetJSON(ctx, key, dest)
	if err != nil {
		return false, fmt.Errorf("failed to get from cache: %w", err)
	}

	monitoring.CacheHits.WithLabelValues(CacheTypeRedis).Inc()
	return true, nil
}

// Set 设置Redis缓存数据
func (c *redisCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	if !c.config.Enabled {
		return nil
	}

	if ttl == 0 {
		ttl = c.config.DefaultTTL
	}

	return c.client.SetJSON(ctx, key, value, ttl)
}

// Delete 删除Redis缓存数据
func (c *redisCache) Delete(ctx context.Context, key string) error {
	if !c.config.Enabled {
		return nil
	}

	return c.client.Delete(ctx, key)
}

// DeletePattern 根据模式删除Redis缓存
func (c *redisCache) DeletePattern(ctx context.Context, pattern string) error {
	if !c.config.Enabled {
		return nil
	}

	keys, err := c.client.Keys(ctx, pattern)
	if err != nil {
		return fmt.Errorf("failed to get keys by pattern: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	return c.client.Delete(ctx, keys...)
}

// Clear 清空Redis缓存
func (c *redisCache) Clear(ctx context.Context) error {
	// 注意：在生产环境中这可能是一个危险操作
	// 考虑限制清除只影响应用程序使用的键
	if !c.config.Enabled {
		return nil
	}

	keys, err := c.client.Keys(ctx, "esim:*")
	if err != nil {
		return fmt.Errorf("failed to get all keys: %w", err)
	}

	if len(keys) == 0 {
		return nil
	}

	return c.client.Delete(ctx, keys...)
}

// Type 返回缓存类型
func (c *redisCache) Type() string {
	return CacheTypeRedis
}

// CachedFetcher 带缓存的数据获取
// fetcher: 数据获取函数
// key: 缓存键
// dest: 目标结构，用于存储结果
// ttl: 可选的缓存过期时间
func CachedFetcher(ctx context.Context, cache Cache, key string, fetcher func() (interface{}, error), dest interface{}, ttl time.Duration) error {
	// 尝试从缓存获取
	found, err := cache.Get(ctx, key, dest)
	if err != nil {
		return fmt.Errorf("failed to get from cache: %w", err)
	}

	// 缓存命中，直接返回
	if found {
		return nil
	}

	// 缓存未命中，调用fetcher获取数据
	result, err := fetcher()
	if err != nil {
		return fmt.Errorf("failed to fetch data: %w", err)
	}

	// 将结果存入目标结构
	resultBytes, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(resultBytes, dest); err != nil {
		return fmt.Errorf("failed to unmarshal result to destination: %w", err)
	}

	// 将结果存入缓存
	if err := cache.Set(ctx, key, result, ttl); err != nil {
		return fmt.Errorf("failed to set cache: %w", err)
	}

	return nil
}

// PrefixedKeyGenerator 创建带前缀的键生成器
func PrefixedKeyGenerator(prefix string) KeyGenerator {
	return func(args ...interface{}) string {
		parts := make([]string, len(args)+1)
		parts[0] = prefix

		for i, arg := range args {
			parts[i+1] = fmt.Sprintf("%v", arg)
		}

		return fmt.Sprintf("esim:%s", JoinParts(parts, ":"))
	}
}

// JoinParts 连接字符串切片，用分隔符分隔
func JoinParts(parts []string, separator string) string {
	var result string
	for i, part := range parts {
		if i > 0 {
			result += separator
		}
		result += part
	}
	return result
}

// WithExpiry 为缓存键设置过期时间
type WithExpiry struct {
	Value interface{}
	TTL   time.Duration
}

// NewWithExpiry 创建新的带过期时间的值
func NewWithExpiry(value interface{}, ttl time.Duration) *WithExpiry {
	return &WithExpiry{
		Value: value,
		TTL:   ttl,
	}
}
