package jwt

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// 常量定义
const (
	RoleUser       = "user"
	RoleReseller   = "reseller"
	RoleEnterprise = "enterprise"
	RoleAdmin      = "admin"
)

// 错误定义
var (
	ErrInvalidToken         = errors.New("invalid token")
	ErrTokenExpired         = errors.New("token has expired")
	ErrInvalidSigningMethod = errors.New("unexpected signing method")
	ErrInvalidClaims        = errors.New("invalid token claims")
)

// CustomClaims 自定义JWT声明
type CustomClaims struct {
	UserID string `json:"user_id"`
	Email  string `json:"email"`
	Role   string `json:"role"`
	jwt.RegisteredClaims
}

// RefreshClaims 刷新令牌声明
type RefreshClaims struct {
	UserID string `json:"user_id"`
	jwt.RegisteredClaims
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"` // 过期时间（秒）
}

// TokenService JWT令牌服务
type TokenService struct {
	secretKey            string
	accessTokenDuration  time.Duration
	refreshTokenDuration time.Duration
}

// NewTokenService 创建新的令牌服务
func NewTokenService(secretKey string, accessTokenDuration time.Duration) *TokenService {
	return &TokenService{
		secretKey:            secretKey,
		accessTokenDuration:  accessTokenDuration,
		refreshTokenDuration: accessTokenDuration * 2, // 刷新令牌有效期是访问令牌的两倍
	}
}

// NewTokenServiceWithOptions 创建新的令牌服务并指定刷新令牌有效期
func NewTokenServiceWithOptions(secretKey string, accessTokenDuration, refreshTokenDuration time.Duration) *TokenService {
	return &TokenService{
		secretKey:            secretKey,
		accessTokenDuration:  accessTokenDuration,
		refreshTokenDuration: refreshTokenDuration,
	}
}

// GenerateToken 生成JWT令牌
func (s *TokenService) GenerateToken(userID, email, role string) (string, error) {
	now := time.Now()
	expiresAt := now.Add(s.accessTokenDuration)

	claims := CustomClaims{
		UserID: userID,
		Email:  email,
		Role:   role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "esim-backend",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(s.secretKey))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return signedToken, nil
}

// GenerateTokenPair 生成访问令牌和刷新令牌对
func (s *TokenService) GenerateTokenPair(userID, email, role string) (*TokenPair, error) {
	// 生成访问令牌
	accessToken, err := s.GenerateToken(userID, email, role)
	if err != nil {
		return nil, err
	}

	// 生成刷新令牌
	refreshToken, err := s.generateRefreshToken(userID)
	if err != nil {
		return nil, err
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.accessTokenDuration.Seconds()),
	}, nil
}

// generateRefreshToken 生成刷新令牌
func (s *TokenService) generateRefreshToken(userID string) (string, error) {
	now := time.Now()
	expiresAt := now.Add(s.refreshTokenDuration)

	claims := RefreshClaims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "esim-backend",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(s.secretKey))
	if err != nil {
		return "", fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return signedToken, nil
}

// ValidateToken 验证JWT令牌
func (s *TokenService) ValidateToken(tokenString string) (*CustomClaims, error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&CustomClaims{},
		func(token *jwt.Token) (interface{}, error) {
			// 验证签名算法
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, ErrInvalidSigningMethod
			}
			return []byte(s.secretKey), nil
		},
	)

	if err != nil {
		// 检查是否为过期错误
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrTokenExpired
		}
		return nil, err
	}

	if !token.Valid {
		return nil, ErrInvalidToken
	}

	claims, ok := token.Claims.(*CustomClaims)
	if !ok {
		return nil, ErrInvalidClaims
	}

	return claims, nil
}

// RefreshAccessToken 使用刷新令牌生成新的访问令牌
func (s *TokenService) RefreshAccessToken(refreshToken string) (*TokenPair, error) {
	// 解析刷新令牌
	token, err := jwt.ParseWithClaims(
		refreshToken,
		&RefreshClaims{},
		func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, ErrInvalidSigningMethod
			}
			return []byte(s.secretKey), nil
		},
	)

	if err != nil {
		// 检查是否为过期错误
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrTokenExpired
		}
		return nil, err
	}

	if !token.Valid {
		return nil, ErrInvalidToken
	}

	claims, ok := token.Claims.(*RefreshClaims)
	if !ok {
		return nil, ErrInvalidClaims
	}

	// 此处应该从数据库获取用户信息
	// 为简化示例，假设我们知道用户的邮箱和角色
	// 实际实现时，应该基于userID查询用户信息
	userID := claims.UserID

	// TODO: 在实际实现中，应该从数据库中获取用户的邮箱和角色
	// 并检查刷新令牌是否在黑名单中
	email := "<EMAIL>" // 应从数据库获取
	role := RoleUser               // 应从数据库获取

	// 生成新的令牌对
	return s.GenerateTokenPair(userID, email, role)
}

// ParseTokenWithoutValidation 解析令牌但不验证签名（用于日志和调试）
func (s *TokenService) ParseTokenWithoutValidation(tokenString string) (*jwt.Token, error) {
	parser := jwt.NewParser(jwt.WithoutClaimsValidation())
	return parser.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.secretKey), nil
	})
}

// GetExpirationTime 获取令牌的过期时间
func (s *TokenService) GetExpirationTime(tokenString string) (time.Time, error) {
	token, err := s.ParseTokenWithoutValidation(tokenString)
	if err != nil {
		return time.Time{}, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return time.Time{}, ErrInvalidClaims
	}

	expClaim, ok := claims["exp"]
	if !ok {
		return time.Time{}, errors.New("token has no expiration claim")
	}

	switch exp := expClaim.(type) {
	case float64:
		return time.Unix(int64(exp), 0), nil
	case json.Number:
		v, _ := exp.Int64()
		return time.Unix(v, 0), nil
	default:
		return time.Time{}, errors.New("invalid expiration claim format")
	}
}
