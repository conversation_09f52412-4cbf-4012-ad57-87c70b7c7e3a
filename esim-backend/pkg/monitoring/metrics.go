package monitoring

import (
	"fmt"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

var (
	// Registry 存储所有的Prometheus指标
	Registry = prometheus.NewRegistry()

	// RequestDuration 请求持续时间直方图
	RequestDuration = promauto.With(Registry).NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "esim_http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "path", "status"},
	)

	// RequestsTotal 请求总数计数器
	RequestsTotal = promauto.With(Registry).NewCounterVec(
		prometheus.CounterOpts{
			Name: "esim_http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "path", "status"},
	)

	// ActiveRequests 当前活跃请求量
	ActiveRequests = promauto.With(Registry).NewGauge(
		prometheus.GaugeOpts{
			Name: "esim_http_active_requests",
			Help: "Number of active HTTP requests",
		},
	)

	// DatabaseQueryDuration 数据库查询持续时间直方图
	DatabaseQueryDuration = promauto.With(Registry).NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "esim_db_query_duration_seconds",
			Help:    "Database query duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"operation", "table"},
	)

	// CacheHits 缓存命中计数器
	CacheHits = promauto.With(Registry).NewCounterVec(
		prometheus.CounterOpts{
			Name: "esim_cache_hits_total",
			Help: "Total number of cache hits",
		},
		[]string{"cache_type"},
	)

	// CacheMisses 缓存未命中计数器
	CacheMisses = promauto.With(Registry).NewCounterVec(
		prometheus.CounterOpts{
			Name: "esim_cache_misses_total",
			Help: "Total number of cache misses",
		},
		[]string{"cache_type"},
	)

	// ESIMProviderRequests eSIM提供商请求计数器
	ESIMProviderRequests = promauto.With(Registry).NewCounterVec(
		prometheus.CounterOpts{
			Name: "esim_provider_requests_total",
			Help: "Total number of requests to eSIM providers",
		},
		[]string{"provider", "operation", "status"},
	)

	// ESIMProviderRequestDuration eSIM提供商请求持续时间直方图
	ESIMProviderRequestDuration = promauto.With(Registry).NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "esim_provider_request_duration_seconds",
			Help:    "Duration of requests to eSIM providers in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"provider", "operation"},
	)

	// AsyncTasksTotal 异步任务计数器
	AsyncTasksTotal = promauto.With(Registry).NewCounterVec(
		prometheus.CounterOpts{
			Name: "esim_async_tasks_total",
			Help: "Total number of async tasks",
		},
		[]string{"type", "status"},
	)

	// AsyncTaskDuration 异步任务持续时间直方图
	AsyncTaskDuration = promauto.With(Registry).NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "esim_async_task_duration_seconds",
			Help:    "Duration of async tasks in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"type"},
	)

	// BusinessMetrics 业务指标计数器
	BusinessMetrics = promauto.With(Registry).NewCounterVec(
		prometheus.CounterOpts{
			Name: "esim_business_metrics_total",
			Help: "Business metrics counter",
		},
		[]string{"metric"},
	)
)

// SetupMetricsEndpoint 设置指标端点
func SetupMetricsEndpoint(e *echo.Echo) {
	// 创建一个专门的路由组用于指标收集
	metricsGroup := e.Group("/metrics")

	// 设置对metrics端点的访问控制（可选）
	// metricsGroup.Use(middleware.BasicAuth(func(username, password string, c echo.Context) (bool, error) {
	//   return username == "metrics" && password == "your-secure-password", nil
	// }))

	// 注册Prometheus处理程序
	metricsHandler := promhttp.HandlerFor(Registry, promhttp.HandlerOpts{})
	metricsGroup.GET("", echo.WrapHandler(metricsHandler))
}

// PrometheusMiddleware Echo中间件，用于收集HTTP请求指标
func PrometheusMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		start := time.Now()

		// 增加活跃请求计数
		ActiveRequests.Inc()

		// 确保在函数退出时减少活跃请求计数
		defer ActiveRequests.Dec()

		// 调用下一个处理程序
		err := next(c)

		// 计算持续时间
		duration := time.Since(start).Seconds()

		// 获取状态码
		status := fmt.Sprintf("%d", c.Response().Status)

		// 增加请求计数器
		RequestsTotal.WithLabelValues(c.Request().Method, c.Path(), status).Inc()

		// 记录请求持续时间
		RequestDuration.WithLabelValues(c.Request().Method, c.Path(), status).Observe(duration)

		return err
	}
}

// MeasureDBQuery 测量数据库查询的持续时间
func MeasureDBQuery(operation, table string) func() {
	start := time.Now()
	return func() {
		duration := time.Since(start).Seconds()
		DatabaseQueryDuration.WithLabelValues(operation, table).Observe(duration)
	}
}

// TrackESIMProviderRequest 跟踪eSIM提供商请求
func TrackESIMProviderRequest(provider, operation string) func(status string) {
	start := time.Now()
	return func(status string) {
		duration := time.Since(start).Seconds()
		ESIMProviderRequests.WithLabelValues(provider, operation, status).Inc()
		ESIMProviderRequestDuration.WithLabelValues(provider, operation).Observe(duration)
	}
}

// TrackAsyncTask 跟踪异步任务
func TrackAsyncTask(taskType string) func(status string) {
	start := time.Now()
	return func(status string) {
		duration := time.Since(start).Seconds()
		AsyncTasksTotal.WithLabelValues(taskType, status).Inc()
		AsyncTaskDuration.WithLabelValues(taskType).Observe(duration)
	}
}

// RecordBusinessMetric 记录业务指标
func RecordBusinessMetric(metric string, value float64) {
	BusinessMetrics.WithLabelValues(metric).Add(value)
}

// HealthCheck 健康检查处理程序
func HealthCheck(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]string{
		"status":  "ok",
		"version": "1.0.0", // 可以从配置中读取
	})
}

// SetupHealthEndpoint 设置健康检查端点
func SetupHealthEndpoint(e *echo.Echo) {
	e.GET("/health", HealthCheck)
}
