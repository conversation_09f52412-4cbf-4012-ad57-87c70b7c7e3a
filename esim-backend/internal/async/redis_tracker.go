package async

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"vereal/letsesim/pkg/esim"
	"vereal/letsesim/pkg/redis"
)

// RedisTaskTrackerImpl Redis实现的任务追踪器
type RedisTaskTrackerImpl struct {
	client *redis.Client
	expiry time.Duration
}

// NewRedisTaskTrackerImpl 创建新的Redis任务追踪器
func NewRedisTaskTrackerImpl(client *redis.Client) esim.TaskTracker {
	return &RedisTaskTrackerImpl{
		client: client,
		expiry: 7 * 24 * time.Hour, // 任务状态保留7天
	}
}

// getTaskKey 获取任务的Redis键
func (t *RedisTaskTrackerImpl) getTaskKey(taskID string) string {
	return fmt.Sprintf("task:%s", taskID)
}

// RegisterTask 注册新的异步任务
func (t *RedisTaskTrackerImpl) RegisterTask(ctx context.Context, taskType string, referenceID string) (string, error) {
	taskID := generateTaskID()
	now := time.Now()

	task := &esim.TaskStatus{
		TaskID:      taskID,
		Type:        taskType,
		ReferenceID: referenceID,
		Status:      esim.TaskStatusPending,
		Metadata:    make(map[string]interface{}),
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 序列化任务状态
	taskData, err := json.Marshal(task)
	if err != nil {
		return "", fmt.Errorf("failed to serialize task: %w", err)
	}

	// 存储到Redis
	key := t.getTaskKey(taskID)
	if err := t.client.Set(ctx, key, string(taskData), t.expiry); err != nil {
		return "", fmt.Errorf("failed to store task: %w", err)
	}

	return taskID, nil
}

// UpdateTaskStatus 更新任务状态
func (t *RedisTaskTrackerImpl) UpdateTaskStatus(ctx context.Context, taskID string, status string, result interface{}) error {
	key := t.getTaskKey(taskID)

	// 检查任务是否存在
	exists, err := t.client.Exists(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to check task existence: %w", err)
	}

	if !exists {
		return esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Task with ID '%s' not found", taskID),
			nil,
			"",
		)
	}

	// 获取当前任务状态
	taskData, err := t.client.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	var task esim.TaskStatus
	if err := json.Unmarshal([]byte(taskData), &task); err != nil {
		return fmt.Errorf("failed to deserialize task: %w", err)
	}

	// 更新任务状态
	task.Status = status
	task.UpdatedAt = time.Now()

	if result != nil {
		task.Result = result
	}

	// 序列化并保存更新后的任务状态
	updatedTaskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to serialize updated task: %w", err)
	}

	if err := t.client.Set(ctx, key, string(updatedTaskData), t.expiry); err != nil {
		return fmt.Errorf("failed to store updated task: %w", err)
	}

	return nil
}

// GetTaskStatus 获取任务状态
func (t *RedisTaskTrackerImpl) GetTaskStatus(ctx context.Context, taskID string) (*esim.TaskStatus, error) {
	key := t.getTaskKey(taskID)

	// 检查任务是否存在
	exists, err := t.client.Exists(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to check task existence: %w", err)
	}

	if !exists {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Task with ID '%s' not found", taskID),
			nil,
			"",
		)
	}

	// 获取任务数据
	taskData, err := t.client.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	var task esim.TaskStatus
	if err := json.Unmarshal([]byte(taskData), &task); err != nil {
		return nil, fmt.Errorf("failed to deserialize task: %w", err)
	}

	return &task, nil
}

// PollForCompletion 轮询任务完成
func (t *RedisTaskTrackerImpl) PollForCompletion(ctx context.Context, taskID string, timeout time.Duration) (*esim.TaskStatus, error) {
	deadline := time.Now().Add(timeout)
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			task, err := t.GetTaskStatus(ctx, taskID)
			if err != nil {
				return nil, err
			}

			// 任务已完成或失败
			if task.Status == esim.TaskStatusCompleted || task.Status == esim.TaskStatusFailed {
				return task, nil
			}

			// 已超时
			if time.Now().After(deadline) {
				return nil, esim.NewESIMError(
					esim.ErrTimeout,
					fmt.Sprintf("Task '%s' polling timed out after %v", taskID, timeout),
					nil,
					"",
				)
			}
		}
	}
}

// generateTaskID 生成唯一的任务ID
func generateTaskID() string {
	now := time.Now()
	return fmt.Sprintf("task_%d%d", now.Unix(), now.Nanosecond())
}
