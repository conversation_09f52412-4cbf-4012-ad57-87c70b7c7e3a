package async

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"vereal/letsesim/pkg/esim"

	"github.com/google/uuid"
)

// InMemoryTaskTracker 基于内存的任务追踪器实现
// 注意：在生产环境中，应使用Redis或数据库来存储任务状态
type InMemoryTaskTracker struct {
	tasks map[string]*esim.TaskStatus
	mu    sync.RWMutex
}

// NewInMemoryTaskTracker 创建新的内存任务追踪器
func NewInMemoryTaskTracker() *InMemoryTaskTracker {
	return &InMemoryTaskTracker{
		tasks: make(map[string]*esim.TaskStatus),
	}
}

// RegisterTask 注册新的异步任务
func (t *InMemoryTaskTracker) RegisterTask(ctx context.Context, taskType string, referenceID string) (string, error) {
	taskID := uuid.New().String()
	now := time.Now()

	task := &esim.TaskStatus{
		TaskID:      taskID,
		Type:        taskType,
		ReferenceID: referenceID,
		Status:      esim.TaskStatusPending,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	t.mu.Lock()
	defer t.mu.Unlock()

	t.tasks[taskID] = task

	// 在实际应用中，这里应该将任务持久化到数据库或Redis

	return taskID, nil
}

// UpdateTaskStatus 更新任务状态
func (t *InMemoryTaskTracker) UpdateTaskStatus(ctx context.Context, taskID string, status string, result interface{}) error {
	t.mu.Lock()
	defer t.mu.Unlock()

	task, exists := t.tasks[taskID]
	if !exists {
		return esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Task with ID '%s' not found", taskID),
			nil,
			"",
		)
	}

	task.Status = status
	task.UpdatedAt = time.Now()

	if result != nil {
		task.Result = result
	}

	// 在实际应用中，这里应该将更新持久化到数据库或Redis

	return nil
}

// GetTaskStatus 获取任务状态
func (t *InMemoryTaskTracker) GetTaskStatus(ctx context.Context, taskID string) (*esim.TaskStatus, error) {
	t.mu.RLock()
	defer t.mu.RUnlock()

	task, exists := t.tasks[taskID]
	if !exists {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Task with ID '%s' not found", taskID),
			nil,
			"",
		)
	}

	// 返回任务状态的副本，避免外部修改
	taskCopy := *task
	return &taskCopy, nil
}

// PollForCompletion 轮询任务完成
func (t *InMemoryTaskTracker) PollForCompletion(ctx context.Context, taskID string, timeout time.Duration) (*esim.TaskStatus, error) {
	deadline := time.Now().Add(timeout)
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			task, err := t.GetTaskStatus(ctx, taskID)
			if err != nil {
				return nil, err
			}

			// 任务已完成或失败
			if task.Status == esim.TaskStatusCompleted || task.Status == esim.TaskStatusFailed {
				return task, nil
			}

			// 已超时
			if time.Now().After(deadline) {
				return nil, esim.NewESIMError(
					esim.ErrServerError,
					fmt.Sprintf("Task '%s' polling timed out after %v", taskID, timeout),
					nil,
					"",
				)
			}
		}
	}
}

// 任务类型常量
const (
	TaskTypeESIMOrder = "ESIM_ORDER"
	TaskTypeTopUp     = "TOP_UP"
)

// RedisTaskTracker Redis实现的任务追踪器
// 此为示例实现，展示如何使用Redis持久化任务状态
type RedisTaskTracker struct {
	client RedisClient
	expiry time.Duration
}

// RedisClient Redis客户端接口
type RedisClient interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Exists(ctx context.Context, key string) (bool, error)
}

// NewRedisTaskTracker 创建新的Redis任务追踪器
func NewRedisTaskTracker(client RedisClient) *RedisTaskTracker {
	return &RedisTaskTracker{
		client: client,
		expiry: 24 * time.Hour, // 任务状态保留24小时
	}
}

// RegisterTask 注册新的异步任务
func (t *RedisTaskTracker) RegisterTask(ctx context.Context, taskType string, referenceID string) (string, error) {
	taskID := uuid.New().String()
	now := time.Now()

	task := &esim.TaskStatus{
		TaskID:      taskID,
		Type:        taskType,
		ReferenceID: referenceID,
		Status:      esim.TaskStatusPending,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// 序列化任务状态
	taskData, err := json.Marshal(task)
	if err != nil {
		return "", fmt.Errorf("failed to serialize task: %w", err)
	}

	// 存储到Redis
	key := fmt.Sprintf("task:%s", taskID)
	if err := t.client.Set(ctx, key, string(taskData), t.expiry); err != nil {
		return "", fmt.Errorf("failed to store task: %w", err)
	}

	return taskID, nil
}

// UpdateTaskStatus 更新任务状态
func (t *RedisTaskTracker) UpdateTaskStatus(ctx context.Context, taskID string, status string, result interface{}) error {
	key := fmt.Sprintf("task:%s", taskID)

	// 检查任务是否存在
	exists, err := t.client.Exists(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to check task existence: %w", err)
	}

	if !exists {
		return esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Task with ID '%s' not found", taskID),
			nil,
			"",
		)
	}

	// 获取当前任务状态
	taskData, err := t.client.Get(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	var task esim.TaskStatus
	if err := json.Unmarshal([]byte(taskData), &task); err != nil {
		return fmt.Errorf("failed to deserialize task: %w", err)
	}

	// 更新任务状态
	task.Status = status
	task.UpdatedAt = time.Now()

	if result != nil {
		task.Result = result
	}

	// 序列化并保存更新后的任务状态
	updatedTaskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to serialize updated task: %w", err)
	}

	if err := t.client.Set(ctx, key, string(updatedTaskData), t.expiry); err != nil {
		return fmt.Errorf("failed to store updated task: %w", err)
	}

	return nil
}

// GetTaskStatus 获取任务状态
func (t *RedisTaskTracker) GetTaskStatus(ctx context.Context, taskID string) (*esim.TaskStatus, error) {
	key := fmt.Sprintf("task:%s", taskID)

	// 检查任务是否存在
	exists, err := t.client.Exists(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to check task existence: %w", err)
	}

	if !exists {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Task with ID '%s' not found", taskID),
			nil,
			"",
		)
	}

	// 获取任务状态
	taskData, err := t.client.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	var task esim.TaskStatus
	if err := json.Unmarshal([]byte(taskData), &task); err != nil {
		return nil, fmt.Errorf("failed to deserialize task: %w", err)
	}

	return &task, nil
}

// PollForCompletion 轮询任务完成
func (t *RedisTaskTracker) PollForCompletion(ctx context.Context, taskID string, timeout time.Duration) (*esim.TaskStatus, error) {
	deadline := time.Now().Add(timeout)
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			task, err := t.GetTaskStatus(ctx, taskID)
			if err != nil {
				return nil, err
			}

			// 任务已完成或失败
			if task.Status == esim.TaskStatusCompleted || task.Status == esim.TaskStatusFailed {
				return task, nil
			}

			// 已超时
			if time.Now().After(deadline) {
				return nil, esim.NewESIMError(
					esim.ErrServerError,
					fmt.Sprintf("Task '%s' polling timed out after %v", taskID, timeout),
					nil,
					"",
				)
			}
		}
	}
}
