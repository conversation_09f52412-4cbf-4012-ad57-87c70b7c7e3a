package async

import (
	"context"
	"fmt"
	"sync"
	"time"

	"vereal/letsesim/pkg/esim"
)

// DefaultWebhookHandler 默认的Webhook处理器实现
type DefaultWebhookHandler struct {
	handlers        map[string]map[string]eventHandler
	mu              sync.RWMutex
	providerFactory esim.ESIMProviderFactoryInterface
	configManager   esim.ProviderConfigManagerInterface
}

type eventHandler func(ctx context.Context, event *esim.WebhookEvent) error

// NewWebhookHandler 创建新的Webhook处理器
func NewWebhookHandler(providerFactory esim.ESIMProviderFactoryInterface, configManager esim.ProviderConfigManagerInterface) *DefaultWebhookHandler {
	return &DefaultWebhookHandler{
		handlers:        make(map[string]map[string]eventHandler),
		providerFactory: providerFactory,
		configManager:   configManager,
	}
}

// RegisterEventHandler 注册事件处理函数
func (h *DefaultWebhookHandler) RegisterEventHandler(eventType string, handler func(ctx context.Context, event *esim.WebhookEvent) error) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 支持多个提供商的同一事件类型
	typeParts := eventType
	providerType := "" // 默认所有提供商

	// 如果事件类型包含提供商前缀，则解析
	// 例如：esim_access:ORDER_STATUS
	if len(typeParts) > 0 {
		providerType = "global" // 默认全局处理器
	}

	if _, exists := h.handlers[providerType]; !exists {
		h.handlers[providerType] = make(map[string]eventHandler)
	}

	h.handlers[providerType][eventType] = handler
}

// HandleWebhook 处理Webhook请求
func (h *DefaultWebhookHandler) HandleWebhook(ctx context.Context, providerType string, payload []byte, headers map[string][]string) error {
	// 获取提供商实例
	provider, err := h.providerFactory.GetProvider(ctx, providerType)
	if err != nil {
		return err
	}

	// 处理Webhook事件
	event, err := provider.ProcessWebhookEvent(ctx, payload, headers)
	if err != nil {
		return err
	}

	// 设置处理时间和提供商类型
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}
	event.ProviderType = providerType

	// 查找并执行事件处理器
	return h.executeHandler(ctx, event)
}

// executeHandler 执行事件处理器
func (h *DefaultWebhookHandler) executeHandler(ctx context.Context, event *esim.WebhookEvent) error {
	h.mu.RLock()
	defer h.mu.RUnlock()

	// 首先尝试提供商特定的处理器
	if providerHandlers, exists := h.handlers[event.ProviderType]; exists {
		if handler, exists := providerHandlers[event.EventType]; exists {
			return handler(ctx, event)
		}
	}

	// 然后尝试全局处理器
	if globalHandlers, exists := h.handlers["global"]; exists {
		if handler, exists := globalHandlers[event.EventType]; exists {
			return handler(ctx, event)
		}
	}

	// 找不到处理器，记录事件但不报错
	// 在实际应用中，应该记录到日志系统
	fmt.Printf("No handler registered for event type '%s' from provider '%s'\n",
		event.EventType, event.ProviderType)

	return nil
}

// GetWebhookConfig 获取Webhook配置
func (h *DefaultWebhookHandler) GetWebhookConfig(ctx context.Context, providerType string) (*esim.WebhookConfig, error) {
	config, err := h.configManager.GetConfig(ctx, providerType)
	if err != nil {
		return nil, err
	}

	return &config.WebhookConfig, nil
}

// ESIMOrderCreatedHandler 处理eSIM订单创建完成事件
// 这是一个示例处理器，演示如何处理异步订单完成
func ESIMOrderCreatedHandler(taskTracker esim.AsyncTaskTracker) func(ctx context.Context, event *esim.WebhookEvent) error {
	return func(ctx context.Context, event *esim.WebhookEvent) error {
		// 检查事件类型
		if event.EventType != esim.EventTypeOrderStatus {
			return nil
		}

		// 从事件中提取订单号
		orderNo, ok := event.Payload["orderNo"].(string)
		if !ok || orderNo == "" {
			return fmt.Errorf("invalid order number in webhook event")
		}

		// 从事件中提取状态
		status, ok := event.Payload["orderStatus"].(string)
		if !ok {
			return fmt.Errorf("invalid order status in webhook event")
		}

		// 只处理订单资源准备就绪的事件
		if status != "GOT_RESOURCE" {
			return nil
		}

		// 查找与订单相关的任务
		// 在实际应用中，应该有一个订单到任务ID的映射
		// 这里简化处理，假设任务ID存储在某处

		// 更新任务状态为完成
		// 这里应该查询eSIM详情并设置为任务结果
		// 简化处理：
		result := map[string]interface{}{
			"orderNo": orderNo,
			"status":  "completed",
		}

		// 注意：在真实应用中，这里应该查询订单的eSIM详情
		// 然后更新任务状态为完成，并将eSIM详情设置为结果

		// 模拟找到的任务ID
		taskID := "task_" + orderNo

		// 更新任务状态
		if err := taskTracker.UpdateTaskStatus(ctx, taskID, esim.TaskStatusCompleted, result); err != nil {
			// 处理错误，但不中断处理流程
			fmt.Printf("Failed to update task status: %v\n", err)
		}

		return nil
	}
}

// InitializeDefaultHandlers 初始化默认的事件处理器
func InitializeDefaultHandlers(webhookHandler esim.WebhookHandler, taskTracker esim.AsyncTaskTracker) {
	// 注册处理eSIM订单创建完成的处理器
	webhookHandler.RegisterEventHandler(esim.EventTypeOrderStatus, ESIMOrderCreatedHandler(taskTracker))

	// 注册处理eSIM状态变更的处理器
	webhookHandler.RegisterEventHandler(esim.EventTypeESIMStatus, func(ctx context.Context, event *esim.WebhookEvent) error {
		// 这里处理eSIM状态变更事件
		// 例如：更新数据库中的eSIM状态，或触发通知
		fmt.Printf("eSIM status changed: %v\n", event.Payload)
		return nil
	})

	// 注册处理数据用量事件的处理器
	webhookHandler.RegisterEventHandler(esim.EventTypeDataUsage, func(ctx context.Context, event *esim.WebhookEvent) error {
		// 这里处理数据用量事件
		// 例如：更新数据库中的用量记录，或触发低用量提醒
		fmt.Printf("Data usage update: %v\n", event.Payload)
		return nil
	})

	// 注册处理有效期事件的处理器
	webhookHandler.RegisterEventHandler(esim.EventTypeValidityUsage, func(ctx context.Context, event *esim.WebhookEvent) error {
		// 这里处理有效期事件
		// 例如：发送即将到期提醒
		fmt.Printf("Validity period update: %v\n", event.Payload)
		return nil
	})
}
