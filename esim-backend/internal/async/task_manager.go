package async

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"vereal/letsesim/pkg/esim"
	"vereal/letsesim/pkg/monitoring"
	"vereal/letsesim/pkg/redis"

	"github.com/google/uuid"
)

// TaskManager 任务管理器，负责任务的调度、重试和监控
type TaskManager struct {
	tracker          esim.TaskTracker
	workers          int
	maxRetries       int
	retryDelays      []time.Duration
	shutdownCh       chan struct{}
	wg               sync.WaitGroup
	tasks            chan *esim.TaskStatus
	processorsByType map[string]TaskProcessor
	mu               sync.RWMutex
	client           *redis.Client
}

// TaskProcessor 任务处理器接口
type TaskProcessor interface {
	// ProcessTask 处理任务
	ProcessTask(ctx context.Context, task *esim.TaskStatus) (interface{}, error)
}

// TaskManagerOptions 任务管理器配置选项
type TaskManagerOptions struct {
	Workers     int
	MaxRetries  int
	RetryDelays []time.Duration
}

// DefaultTaskManagerOptions 默认的任务管理器配置
func DefaultTaskManagerOptions() *TaskManagerOptions {
	return &TaskManagerOptions{
		Workers:    5,
		MaxRetries: 3,
		RetryDelays: []time.Duration{
			5 * time.Second,
			30 * time.Second,
			2 * time.Minute,
		},
	}
}

// NewTaskManager 创建任务管理器
func NewTaskManager(tracker esim.TaskTracker, client *redis.Client, opts *TaskManagerOptions) *TaskManager {
	if opts == nil {
		opts = DefaultTaskManagerOptions()
	}

	return &TaskManager{
		tracker:          tracker,
		workers:          opts.Workers,
		maxRetries:       opts.MaxRetries,
		retryDelays:      opts.RetryDelays,
		shutdownCh:       make(chan struct{}),
		tasks:            make(chan *esim.TaskStatus, 100),
		processorsByType: make(map[string]TaskProcessor),
		client:           client,
	}
}

// RegisterProcessor 注册任务处理器
func (m *TaskManager) RegisterProcessor(taskType string, processor TaskProcessor) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.processorsByType[taskType] = processor
}

// QueueTask 将任务加入队列
func (m *TaskManager) QueueTask(ctx context.Context, taskType string, referenceID string, data interface{}) (string, error) {
	// 验证任务类型是否已注册处理器
	m.mu.RLock()
	_, exists := m.processorsByType[taskType]
	m.mu.RUnlock()

	if !exists {
		return "", fmt.Errorf("no processor registered for task type: %s", taskType)
	}

	// 注册任务
	taskID, err := m.tracker.RegisterTask(ctx, taskType, referenceID)
	if err != nil {
		return "", fmt.Errorf("failed to register task: %w", err)
	}

	// 将任务数据保存到Redis
	if data != nil {
		taskDataKey := fmt.Sprintf("task_data:%s", taskID)
		encodedData, err := json.Marshal(data)
		if err != nil {
			return "", fmt.Errorf("failed to marshal task data: %w", err)
		}

		if err := m.client.Set(ctx, taskDataKey, encodedData, 24*time.Hour); err != nil {
			// 如果无法保存数据，取消任务
			_ = m.tracker.UpdateTaskStatus(ctx, taskID, esim.TaskStatusFailed, nil)
			return "", fmt.Errorf("failed to store task data: %w", err)
		}
	}

	// 获取任务状态
	task, err := m.tracker.GetTaskStatus(ctx, taskID)
	if err != nil {
		return "", fmt.Errorf("failed to get task status: %w", err)
	}

	// 将任务加入队列
	select {
	case m.tasks <- task:
		// 任务已加入队列
		monitoring.RecordBusinessMetric("tasks_queued", 1)
	default:
		// 队列已满，更新任务状态为失败
		_ = m.tracker.UpdateTaskStatus(ctx, taskID, esim.TaskStatusFailed, map[string]string{
			"error": "Task queue is full",
		})
		return "", fmt.Errorf("task queue is full")
	}

	return taskID, nil
}

// Start 启动任务管理器
func (m *TaskManager) Start(ctx context.Context) {
	// 启动工作线程
	for i := 0; i < m.workers; i++ {
		m.wg.Add(1)
		go m.worker(ctx, i)
	}

	// 启动任务调度线程
	m.wg.Add(1)
	go m.scheduler(ctx)

	// 启动任务清理线程
	m.wg.Add(1)
	go m.cleaner(ctx)
}

// Shutdown 关闭任务管理器
func (m *TaskManager) Shutdown() {
	close(m.shutdownCh)
	m.wg.Wait()
}

// worker 工作线程，处理任务队列中的任务
func (m *TaskManager) worker(ctx context.Context, id int) {
	defer m.wg.Done()

	for {
		select {
		case <-m.shutdownCh:
			return
		case task := <-m.tasks:
			m.processTask(ctx, task)
		}
	}
}

// processTask 处理单个任务
func (m *TaskManager) processTask(ctx context.Context, task *esim.TaskStatus) {
	defer func() {
		if r := recover(); r != nil {
			// 处理器产生panic，更新任务状态为失败
			err := fmt.Errorf("task processor panicked: %v", r)
			_ = m.tracker.UpdateTaskStatus(ctx, task.TaskID, esim.TaskStatusFailed, map[string]string{
				"error": err.Error(),
			})
			monitoring.AsyncTasksTotal.WithLabelValues(task.Type, "panic").Inc()
		}
	}()

	// 开始处理任务，更新状态为处理中
	_ = m.tracker.UpdateTaskStatus(ctx, task.TaskID, esim.TaskStatusProcessing, nil)

	// 获取任务数据
	var taskData interface{}
	if task.ReferenceID != "" {
		taskDataKey := fmt.Sprintf("task_data:%s", task.TaskID)
		dataBytes, err := m.client.GetBytes(ctx, taskDataKey)
		if err == nil && len(dataBytes) > 0 {
			if err := json.Unmarshal(dataBytes, &taskData); err != nil {
				fmt.Printf("Failed to unmarshal task data: %v\n", err)
			}
		}
	}

	// 获取任务处理器
	m.mu.RLock()
	processor, exists := m.processorsByType[task.Type]
	m.mu.RUnlock()

	if !exists {
		// 没有找到处理器，更新任务状态为失败
		err := fmt.Errorf("no processor found for task type: %s", task.Type)
		_ = m.tracker.UpdateTaskStatus(ctx, task.TaskID, esim.TaskStatusFailed, map[string]string{
			"error": err.Error(),
		})
		monitoring.AsyncTasksTotal.WithLabelValues(task.Type, "error").Inc()
		return
	}

	// 跟踪任务执行
	trackFunc := monitoring.TrackAsyncTask(task.Type)

	// 执行任务
	result, err := processor.ProcessTask(ctx, task)

	if err != nil {
		// 处理任务失败
		retryCount, _ := task.Metadata["retry_count"].(float64)
		if int(retryCount) < m.maxRetries {
			// 设置重试信息
			if task.Metadata == nil {
				task.Metadata = make(map[string]interface{})
			}
			task.Metadata["retry_count"] = retryCount + 1
			task.Metadata["last_error"] = err.Error()
			task.Metadata["retry_after"] = time.Now().Add(m.getRetryDelay(int(retryCount))).Unix()

			// 更新任务状态为等待重试
			_ = m.tracker.UpdateTaskStatus(ctx, task.TaskID, esim.TaskStatusRetrying, task.Metadata)
			trackFunc("retrying")
		} else {
			// 已达到最大重试次数，更新任务状态为失败
			errorResult := map[string]string{
				"error": err.Error(),
			}
			_ = m.tracker.UpdateTaskStatus(ctx, task.TaskID, esim.TaskStatusFailed, errorResult)
			trackFunc("failed")
		}
		return
	}

	// 任务成功完成
	_ = m.tracker.UpdateTaskStatus(ctx, task.TaskID, esim.TaskStatusCompleted, result)
	trackFunc("completed")
}

// getRetryDelay 获取重试延迟时间
func (m *TaskManager) getRetryDelay(retryCount int) time.Duration {
	if retryCount < len(m.retryDelays) {
		return m.retryDelays[retryCount]
	}
	// 默认使用最后一个重试延迟
	return m.retryDelays[len(m.retryDelays)-1]
}

// scheduler 任务调度线程，负责从Redis队列中获取等待处理的任务和需要重试的任务
func (m *TaskManager) scheduler(ctx context.Context) {
	defer m.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.shutdownCh:
			return
		case <-ticker.C:
			m.scheduleRetryingTasks(ctx)
		}
	}
}

// scheduleRetryingTasks 调度需要重试的任务
func (m *TaskManager) scheduleRetryingTasks(ctx context.Context) {
	// 在实际实现中，这里应该从存储中查询所有状态为 TaskStatusRetrying 且 retry_after 时间已过的任务
	// 由于我们的任务跟踪器接口没有提供这种查询能力，这里只是一个示例
	// 实际生产环境应该通过数据库或Redis实现更高效的查询

	// 这里是一个简化的示例逻辑
	now := time.Now().Unix()
	taskKeys, err := m.client.Keys(ctx, "task:*")
	if err != nil {
		fmt.Printf("Failed to get task keys: %v\n", err)
		return
	}

	for _, key := range taskKeys {
		// 从key中提取taskID
		taskID := key[5:] // 去掉 "task:" 前缀

		task, err := m.tracker.GetTaskStatus(ctx, taskID)
		if err != nil {
			continue
		}

		// 如果任务状态为等待重试，且重试时间已到
		if task.Status == esim.TaskStatusRetrying {
			retryAfter, ok := task.Metadata["retry_after"].(float64)
			if ok && int64(retryAfter) <= now {
				// 将任务重新加入处理队列
				select {
				case m.tasks <- task:
					// 任务已重新加入队列
					monitoring.RecordBusinessMetric("tasks_retried", 1)
				default:
					// 队列已满，等待下次调度
				}
			}
		}
	}
}

// cleaner 清理线程，负责清理已完成和失败的过期任务
func (m *TaskManager) cleaner(ctx context.Context) {
	defer m.wg.Done()

	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-m.shutdownCh:
			return
		case <-ticker.C:
			m.cleanExpiredTasks(ctx)
		}
	}
}

// cleanExpiredTasks 清理过期任务
func (m *TaskManager) cleanExpiredTasks(ctx context.Context) {
	// 在实际实现中，应该从存储中查询所有已完成或失败的过期任务并删除
	// 由于任务跟踪器接口没有提供这种能力，这里只是一个示例
	// 实际生产环境应通过数据库或Redis实现

	// 这里是一个简化的示例逻辑
	expirationTime := time.Now().Add(-7 * 24 * time.Hour).Unix() // 7天前

	// 假设任务数据存储在Redis中
	taskKeys, err := m.client.Keys(ctx, "task:*")
	if err != nil {
		fmt.Printf("Failed to get task keys: %v\n", err)
		return
	}

	for _, key := range taskKeys {
		// 获取任务数据
		taskData, err := m.client.Get(ctx, key)
		if err != nil {
			continue
		}

		var task esim.TaskStatus
		if err := json.Unmarshal([]byte(taskData), &task); err != nil {
			continue
		}

		// 如果任务已完成或失败，且更新时间早于过期时间
		if (task.Status == esim.TaskStatusCompleted || task.Status == esim.TaskStatusFailed) && task.UpdatedAt.Unix() < expirationTime {
			// 删除任务数据
			_ = m.client.Delete(ctx, key)

			// 如果有任务相关数据，也删除
			dataKey := fmt.Sprintf("task_data:%s", task.TaskID)
			_ = m.client.Delete(ctx, dataKey)

			monitoring.RecordBusinessMetric("tasks_cleaned", 1)
		}
	}
}

// GetTaskStatus 获取任务状态
func (m *TaskManager) GetTaskStatus(ctx context.Context, taskID string) (*esim.TaskStatus, error) {
	return m.tracker.GetTaskStatus(ctx, taskID)
}

// WaitForTask 等待任务完成
func (m *TaskManager) WaitForTask(ctx context.Context, taskID string, timeout time.Duration) (*esim.TaskStatus, error) {
	return m.tracker.PollForCompletion(ctx, taskID, timeout)
}

// DistributedTaskManager 分布式任务管理器
// 在实际生产环境中，可以扩展TaskManager实现分布式任务处理
// 这里提供一个基本框架
type DistributedTaskManager struct {
	*TaskManager
	nodeID         string
	leaderElection LeaderElection
}

// LeaderElection 领导者选举接口
type LeaderElection interface {
	IsLeader() bool
	RunForLeader(ctx context.Context)
	Resign(ctx context.Context)
}

// NewDistributedTaskManager 创建分布式任务管理器
func NewDistributedTaskManager(tracker esim.TaskTracker, client *redis.Client, leaderElection LeaderElection, opts *TaskManagerOptions) *DistributedTaskManager {
	taskManager := NewTaskManager(tracker, client, opts)

	return &DistributedTaskManager{
		TaskManager:    taskManager,
		nodeID:         uuid.New().String(),
		leaderElection: leaderElection,
	}
}

// Start 启动分布式任务管理器
func (m *DistributedTaskManager) Start(ctx context.Context) {
	// 启动领导者选举
	go m.leaderElection.RunForLeader(ctx)

	// 启动任务处理器
	m.TaskManager.Start(ctx)

	// 启动分布式调度器
	m.wg.Add(1)
	go m.distributedScheduler(ctx)
}

// distributedScheduler 分布式调度器
func (m *DistributedTaskManager) distributedScheduler(ctx context.Context) {
	defer m.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-m.shutdownCh:
			// 辞去领导者角色
			m.leaderElection.Resign(ctx)
			return
		case <-ticker.C:
			// 只有领导者节点执行调度
			if m.leaderElection.IsLeader() {
				m.scheduleRetryingTasks(ctx)
				m.cleanExpiredTasks(ctx)
			}
		}
	}
}
