package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"

	"vereal/letsesim/internal/domain/credit"
	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/service"
)

// CreditHandler 积分处理器
type CreditHandler struct {
	creditService *service.CreditService
}

// NewCreditHandler 创建新的积分处理器
func NewCreditHandler(creditService *service.CreditService) *CreditHandler {
	return &CreditHandler{
		creditService: creditService,
	}
}

// GetUserCredit 获取用户积分
func (h *CreditHandler) GetUserCredit(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 获取用户积分
	userCredit, err := h.creditService.GetUserCredit(c.Request().Context(), user.ID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.<PERSON>(http.StatusOK, map[string]interface{}{
		"id":          userCredit.ID,
		"balance":     userCredit.Balance,
		"currency":    userCredit.Currency,
		"lastUpdated": userCredit.LastUpdated,
		"userId":      userCredit.UserID,
	})
}

// GetTransactionHistory 获取交易历史
func (h *CreditHandler) GetTransactionHistory(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 获取交易历史
	transactions, total, err := h.creditService.GetTransactionHistory(c.Request().Context(), user.ID, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	txList := make([]map[string]interface{}, len(transactions))
	for i, tx := range transactions {
		txList[i] = map[string]interface{}{
			"id":          tx.ID,
			"amount":      tx.Amount,
			"balance":     tx.Balance,
			"type":        tx.Type,
			"description": tx.Description,
			"orderId":     tx.OrderID,
			"status":      tx.Status,
			"createdAt":   tx.CreatedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"transactions": txList,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// AddCredit 用户充值积分
func (h *CreditHandler) AddCredit(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 解析请求
	var req struct {
		Amount      float64 `json:"amount" validate:"required,gt=0"`
		Description string  `json:"description"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 确保用户有积分账户
	userCredit, err := h.creditService.EnsureUserCredit(c.Request().Context(), user.ID, "USD")
	if err != nil {
		return err
	}

	// 充值
	transaction, err := h.creditService.AddBalance(
		c.Request().Context(),
		user.ID,
		req.Amount,
		req.Description,
		"", // 无订单ID
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"transaction": map[string]interface{}{
			"id":          transaction.ID,
			"amount":      transaction.Amount,
			"balance":     transaction.Balance,
			"type":        transaction.Type,
			"description": transaction.Description,
			"status":      transaction.Status,
			"createdAt":   transaction.CreatedAt,
		},
		"credit": map[string]interface{}{
			"id":       userCredit.ID,
			"balance":  userCredit.Balance + req.Amount,
			"currency": userCredit.Currency,
		},
	})
}

// GetTransactionDetail 获取交易详情
func (h *CreditHandler) GetTransactionDetail(c echo.Context) error {
	// 从上下文获取用户
	user := c.Get("user").(*user.User)

	// 获取交易ID
	transactionID := c.Param("id")
	if transactionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "未提供交易ID",
		})
	}

	// 获取交易详情
	transaction, err := h.creditService.GetTransaction(c.Request().Context(), transactionID)
	if err != nil {
		// 对于不存在的记录，返回404
		return echo.NewHTTPError(http.StatusNotFound, map[string]interface{}{
			"code":    404,
			"message": "交易记录不存在",
		})
	}

	// 检查是否是用户自己的交易
	if transaction.UserID != user.ID && !user.IsAdmin() {
		return echo.NewHTTPError(http.StatusForbidden, map[string]interface{}{
			"code":    403,
			"message": "无权访问此交易记录",
		})
	}

	// 获取中文交易类型描述
	typeDesc := getTransactionTypeDesc(string(transaction.Type))

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":          transaction.ID,
		"amount":      transaction.Amount,
		"balance":     transaction.Balance,
		"type":        transaction.Type,
		"typeDesc":    typeDesc,
		"description": transaction.Description,
		"orderId":     transaction.OrderID,
		"status":      transaction.Status,
		"createdAt":   transaction.CreatedAt,
		"updatedAt":   transaction.UpdatedAt,
	})
}

// getTransactionTypeDesc 获取交易类型的中文描述
func getTransactionTypeDesc(transType string) string {
	switch transType {
	case string(credit.TypeDeposit):
		return "充值"
	case string(credit.TypeWithdrawal):
		return "提现"
	case string(credit.TypeOrderPayment):
		return "订单支付"
	case string(credit.TypeRefund):
		return "退款"
	default:
		return "其他"
	}
}

// AdminAddUserCredit 管理员为用户增加积分(管理员API)
func (h *CreditHandler) AdminAddUserCredit(c echo.Context) error {
	// 获取用户ID
	userID := c.Param("id")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "未提供用户ID")
	}

	// 解析请求
	var req struct {
		Amount      float64 `json:"amount" validate:"required"`
		Description string  `json:"description"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 确保用户有积分账户
	_, err := h.creditService.EnsureUserCredit(c.Request().Context(), userID, "USD")
	if err != nil {
		return err
	}

	// 添加积分 (正数为增加，负数为扣除)
	transactionType := credit.TypeDeposit
	if req.Amount < 0 {
		transactionType = credit.TypeWithdrawal
	}

	// 添加或扣除积分
	transaction, err := h.creditService.AddBalance(
		c.Request().Context(),
		userID,
		req.Amount,
		req.Description,
		"", // 无订单ID
	)
	if err != nil {
		return err
	}

	// 获取最新的用户积分信息
	userCredit, err := h.creditService.GetUserCredit(c.Request().Context(), userID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"transaction": map[string]interface{}{
			"id":          transaction.ID,
			"amount":      transaction.Amount,
			"balance":     transaction.Balance,
			"type":        transactionType,
			"description": transaction.Description,
			"status":      transaction.Status,
			"createdAt":   transaction.CreatedAt,
		},
		"credit": map[string]interface{}{
			"id":       userCredit.ID,
			"balance":  userCredit.Balance,
			"currency": userCredit.Currency,
		},
	})
}

// AddResellerUserCredit 代理商为用户充值积分
func (h *CreditHandler) AddResellerUserCredit(c echo.Context) error {
	// 获取用户ID
	userID := c.Param("id")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "未提供用户ID")
	}

	// 解析请求
	var req struct {
		Amount      float64 `json:"amount" validate:"required,gt=0"`
		Description string  `json:"description"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 获取代理商用户
	reseller := c.Get("user").(*user.User)
	if !reseller.IsReseller() {
		return echo.NewHTTPError(http.StatusForbidden, "仅代理商可以为用户充值")
	}

	// 确保用户有积分账户
	userCredit, err := h.creditService.EnsureUserCredit(c.Request().Context(), userID, "USD")
	if err != nil {
		return err
	}

	// 如果没有提供描述，添加默认描述
	description := req.Description
	if description == "" {
		description = fmt.Sprintf("由代理商 %s 充值", reseller.Name)
	}

	// 使用AddBalance给用户充值
	transaction, err := h.creditService.AddBalance(
		c.Request().Context(),
		userID,
		req.Amount,
		description,
		"", // 无订单ID
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"transaction": map[string]interface{}{
			"id":          transaction.ID,
			"amount":      transaction.Amount,
			"balance":     transaction.Balance,
			"type":        transaction.Type,
			"description": transaction.Description,
			"status":      transaction.Status,
			"createdAt":   transaction.CreatedAt,
		},
		"credit": map[string]interface{}{
			"id":       userCredit.ID,
			"balance":  userCredit.Balance + req.Amount,
			"currency": userCredit.Currency,
		},
	})
}
