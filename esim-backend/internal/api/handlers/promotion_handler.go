package handlers

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"

	"vereal/letsesim/internal/domain/promotion"
	"vereal/letsesim/internal/service"
)

// DefaultPromotionHandler 促销处理器
type DefaultPromotionHandler struct {
	promotionService *service.PromotionService
}

// NewPromotionHandler 创建新的促销处理器
func NewPromotionHandler(promotionService *service.PromotionService) *DefaultPromotionHandler {
	return &DefaultPromotionHandler{
		promotionService: promotionService,
	}
}

// CreatePromotion 创建促销（管理员）
func (h *DefaultPromotionHandler) CreatePromotion(c echo.Context) error {
	// 解析请求
	var req struct {
		Code          string                 `json:"code" validate:"required"`
		Description   string                 `json:"description" validate:"required"`
		Type          string                 `json:"type" validate:"required,oneof=PERCENTAGE FIXED_AMOUNT FREE_ITEM"`
		Value         int64                  `json:"value" validate:"required,min=1"`
		MinOrderValue int64                  `json:"minOrderValue"`
		MaxDiscount   int64                  `json:"maxDiscount"`
		StartDate     string                 `json:"startDate"`
		EndDate       string                 `json:"endDate"`
		UsageLimit    int                    `json:"usageLimit"`
		ResellerID    string                 `json:"resellerId"`
		Metadata      map[string]interface{} `json:"metadata"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 解析时间
	startDate := time.Now()
	if req.StartDate != "" {
		var err error
		startDate, err = time.Parse("2006-01-02", req.StartDate)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid startDate format")
		}
	}

	endDate := startDate.AddDate(0, 1, 0) // 默认一个月
	if req.EndDate != "" {
		var err error
		endDate, err = time.Parse("2006-01-02", req.EndDate)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid endDate format")
		}
		// 设置为当天结束
		endDate = endDate.Add(24 * time.Hour).Add(-time.Second)
	}

	// 创建促销
	promo, err := h.promotionService.CreatePromotion(
		c.Request().Context(),
		req.Code,
		req.Description,
		promotion.Type(req.Type),
		req.Value,
		req.MinOrderValue,
		req.MaxDiscount,
		startDate,
		endDate,
		req.UsageLimit,
		req.ResellerID,
		req.Metadata,
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusCreated, map[string]interface{}{
		"id":            promo.ID,
		"code":          promo.Code,
		"description":   promo.Description,
		"type":          promo.Type,
		"value":         promo.Value,
		"minOrderValue": promo.MinOrderValue,
		"maxDiscount":   promo.MaxDiscount,
		"startDate":     promo.StartDate,
		"endDate":       promo.EndDate,
		"usageLimit":    promo.UsageLimit,
		"currentUsage":  promo.CurrentUsage,
		"status":        promo.Status,
		"resellerId":    promo.ResellerID,
		"createdAt":     promo.CreatedAt,
	})
}

// GetPromotion 获取促销详情（管理员）
func (h *DefaultPromotionHandler) GetPromotion(c echo.Context) error {
	// 获取促销ID
	promotionID := c.Param("id")
	if promotionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Promotion ID is required")
	}

	// 获取促销
	promo, err := h.promotionService.GetPromotionByID(c.Request().Context(), promotionID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":            promo.ID,
		"code":          promo.Code,
		"description":   promo.Description,
		"type":          promo.Type,
		"value":         promo.Value,
		"minOrderValue": promo.MinOrderValue,
		"maxDiscount":   promo.MaxDiscount,
		"startDate":     promo.StartDate,
		"endDate":       promo.EndDate,
		"usageLimit":    promo.UsageLimit,
		"currentUsage":  promo.CurrentUsage,
		"status":        promo.Status,
		"resellerId":    promo.ResellerID,
		"createdAt":     promo.CreatedAt,
		"updatedAt":     promo.UpdatedAt,
		"metadata":      promo.Metadata,
	})
}

// ListPromotions 获取促销列表（管理员）
func (h *DefaultPromotionHandler) ListPromotions(c echo.Context) error {
	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 构建过滤条件
	filter := make(map[string]interface{})
	if status := c.QueryParam("status"); status != "" {
		filter["status"] = status
	}

	if resellerID := c.QueryParam("resellerId"); resellerID != "" {
		filter["resellerId"] = resellerID
	}

	// 获取促销列表
	promos, total, err := h.promotionService.ListPromotions(c.Request().Context(), filter, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	promoList := make([]map[string]interface{}, len(promos))
	for i, promo := range promos {
		promoList[i] = map[string]interface{}{
			"id":            promo.ID,
			"code":          promo.Code,
			"description":   promo.Description,
			"type":          promo.Type,
			"value":         promo.Value,
			"minOrderValue": promo.MinOrderValue,
			"maxDiscount":   promo.MaxDiscount,
			"startDate":     promo.StartDate,
			"endDate":       promo.EndDate,
			"usageLimit":    promo.UsageLimit,
			"currentUsage":  promo.CurrentUsage,
			"status":        promo.Status,
			"resellerId":    promo.ResellerID,
			"createdAt":     promo.CreatedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"promotions": promoList,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// UpdatePromotion 更新促销（管理员）
func (h *DefaultPromotionHandler) UpdatePromotion(c echo.Context) error {
	// 获取促销ID
	promotionID := c.Param("id")
	if promotionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Promotion ID is required")
	}

	// 解析请求
	var req struct {
		Description   string `json:"description"`
		Value         int64  `json:"value"`
		MinOrderValue int64  `json:"minOrderValue"`
		MaxDiscount   int64  `json:"maxDiscount"`
		EndDate       string `json:"endDate"`
		UsageLimit    int    `json:"usageLimit"`
		Status        string `json:"status"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 解析时间
	var endDate time.Time
	if req.EndDate != "" {
		var err error
		endDate, err = time.Parse("2006-01-02", req.EndDate)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid endDate format")
		}
		// 设置为当天结束
		endDate = endDate.Add(24 * time.Hour).Add(-time.Second)
	}

	// 更新促销
	promo, err := h.promotionService.UpdatePromotion(
		c.Request().Context(),
		promotionID,
		req.Description,
		req.Value,
		req.MinOrderValue,
		req.MaxDiscount,
		endDate,
		req.UsageLimit,
		req.Status, // 直接传递status字符串
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":            promo.ID,
		"code":          promo.Code,
		"description":   promo.Description,
		"type":          promo.Type,
		"value":         promo.Value,
		"minOrderValue": promo.MinOrderValue,
		"maxDiscount":   promo.MaxDiscount,
		"startDate":     promo.StartDate,
		"endDate":       promo.EndDate,
		"usageLimit":    promo.UsageLimit,
		"currentUsage":  promo.CurrentUsage,
		"status":        promo.Status,
		"updatedAt":     promo.UpdatedAt,
	})
}

// DeletePromotion 删除促销（管理员）
func (h *DefaultPromotionHandler) DeletePromotion(c echo.Context) error {
	// 获取促销ID
	promotionID := c.Param("id")
	if promotionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Promotion ID is required")
	}

	// 删除促销
	err := h.promotionService.DeletePromotion(c.Request().Context(), promotionID)
	if err != nil {
		return err
	}

	// 返回成功响应
	return c.NoContent(http.StatusNoContent)
}

// ValidatePromotion 验证促销码
func (h *DefaultPromotionHandler) ValidatePromotion(c echo.Context) error {
	// 解析请求
	var req struct {
		Code         string `json:"code" validate:"required"`
		OrderValue   int64  `json:"orderValue" validate:"required,min=1"`
		ProviderType string `json:"providerType"`
		PackageId    string `json:"packageId"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "无效的请求格式",
		})
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		errMsg := err.Error()
		if strings.Contains(errMsg, "Code") {
			errMsg = "促销码是必需的"
		} else if strings.Contains(errMsg, "OrderValue") {
			errMsg = "订单金额是必需的且必须大于0"
		}

		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": errMsg,
		})
	}

	// 获取促销码
	promo, err := h.promotionService.GetPromotionByCode(c.Request().Context(), req.Code)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, map[string]interface{}{
				"code":    404,
				"message": "促销码不存在",
			})
		}
		return err
	}

	// 检查促销码是否有效
	if !promo.IsActive() {
		if promo.Status == promotion.StatusExpired {
			return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
				"code":    400,
				"message": "促销码已过期",
			})
		} else {
			return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
				"code":    400,
				"message": "促销码未激活",
			})
		}
	}

	// 检查使用次数
	if promo.UsageLimit > 0 && promo.CurrentUsage >= promo.UsageLimit {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "促销码已达到使用上限",
		})
	}

	// 检查最低订单金额
	if req.OrderValue < promo.MinOrderValue {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":          400,
			"message":       "订单金额未达到促销码最低要求",
			"minOrderValue": promo.MinOrderValue,
		})
	}

	// 计算折扣
	discountAmount := promo.CalculateDiscount(req.OrderValue)

	// 构建折扣类型描述
	discountType := ""
	discountValue := int64(0)

	switch promo.Type {
	case promotion.TypePercentage:
		discountType = "PERCENTAGE"
		discountValue = promo.Value
	case promotion.TypeFixedAmount:
		discountType = "FIXED_AMOUNT"
		discountValue = promo.Value
	case promotion.TypeFreeItem:
		discountType = "FREE_ITEM"
		discountValue = promo.Value
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"valid":          true,
		"promotionId":    promo.ID,
		"discountType":   discountType,
		"discountValue":  discountValue,
		"discountAmount": discountAmount,
		"message":        "折扣已应用",
	})
}

// ApplyPromotion 应用促销码
func (h *DefaultPromotionHandler) ApplyPromotion(c echo.Context) error {
	// 解析请求
	var req struct {
		Code    string `json:"code" validate:"required"`
		OrderID string `json:"orderId" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "无效的请求格式",
		})
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		errMsg := err.Error()
		if strings.Contains(errMsg, "Code") {
			errMsg = "促销码是必需的"
		} else if strings.Contains(errMsg, "OrderID") {
			errMsg = "订单ID是必需的"
		}

		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": errMsg,
		})
	}

	// 这里应该根据订单ID获取订单金额
	// 但由于当前的PromotionService没有这个方法，我们暂时模拟实现
	// 实际项目中需要通过订单服务获取订单金额
	orderAmount := int64(10000) // 假设订单金额为100元

	// 应用折扣
	discountAmount, _, err := h.promotionService.ApplyPromotion(
		c.Request().Context(),
		req.Code,
		orderAmount,
	)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return echo.NewHTTPError(http.StatusNotFound, map[string]interface{}{
				"code":    404,
				"message": "促销码不存在",
			})
		} else if strings.Contains(err.Error(), "not active") {
			return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
				"code":    400,
				"message": "此促销码不可用",
			})
		}
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success":        true,
		"orderId":        req.OrderID,
		"discountAmount": discountAmount,
		"message":        "促销码已成功应用",
	})
}

// ListResellerPromotions 查询代理商促销码列表
func (h *DefaultPromotionHandler) ListResellerPromotions(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 构建过滤条件
	filter := map[string]interface{}{
		"resellerId": resellerID,
	}

	if status := c.QueryParam("status"); status != "" {
		filter["status"] = status
	}

	// 获取促销列表
	promotions, total, err := h.promotionService.ListPromotions(c.Request().Context(), filter, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	promotionList := make([]map[string]interface{}, len(promotions))
	for i, promo := range promotions {
		promotionList[i] = map[string]interface{}{
			"id":            promo.ID,
			"code":          promo.Code,
			"description":   promo.Description,
			"type":          promo.Type,
			"value":         promo.Value,
			"minOrderValue": promo.MinOrderValue,
			"maxDiscount":   promo.MaxDiscount,
			"startDate":     promo.StartDate,
			"endDate":       promo.EndDate,
			"usageLimit":    promo.UsageLimit,
			"currentUsage":  promo.CurrentUsage,
			"status":        promo.Status,
			"scope":         "RESELLER",
			"createdAt":     promo.CreatedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"promotions": promotionList,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// CreateResellerPromotion 创建代理商促销码
func (h *DefaultPromotionHandler) CreateResellerPromotion(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 解析请求
	var req struct {
		Code          string `json:"code" validate:"required"`
		Description   string `json:"description" validate:"required"`
		Type          string `json:"type" validate:"required,oneof=PERCENTAGE FIXED BUNDLE"`
		Value         int    `json:"value" validate:"required,min=1"`
		MinOrderValue int    `json:"minOrderValue"`
		MaxDiscount   int    `json:"maxDiscount"`
		StartDate     string `json:"startDate" validate:"required"`
		EndDate       string `json:"endDate" validate:"required"`
		UsageLimit    int    `json:"usageLimit" validate:"min=0"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid start date format")
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid end date format")
	}

	// 设置结束日期为当天的最后一刻
	endDate = endDate.Add(24*time.Hour - time.Second)

	// 创建促销
	promotion, err := h.promotionService.CreatePromotion(
		c.Request().Context(),
		req.Code,
		req.Description,
		promotion.Type(req.Type),
		int64(req.Value),
		int64(req.MinOrderValue),
		int64(req.MaxDiscount),
		startDate,
		endDate,
		req.UsageLimit,
		resellerID,
		nil, // 暂不支持元数据
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusCreated, map[string]interface{}{
		"id":            promotion.ID,
		"code":          promotion.Code,
		"description":   promotion.Description,
		"type":          promotion.Type,
		"value":         promotion.Value,
		"minOrderValue": promotion.MinOrderValue,
		"maxDiscount":   promotion.MaxDiscount,
		"startDate":     promotion.StartDate,
		"endDate":       promotion.EndDate,
		"usageLimit":    promotion.UsageLimit,
		"currentUsage":  promotion.CurrentUsage,
		"status":        promotion.Status,
		"scope":         "RESELLER",
		"createdAt":     promotion.CreatedAt,
	})
}

// GetResellerPromotion 获取代理商促销码详情
func (h *DefaultPromotionHandler) GetResellerPromotion(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取促销ID
	promotionID := c.Param("id")
	if promotionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Promotion ID is required")
	}

	// 获取促销
	promotion, err := h.promotionService.GetPromotionByID(c.Request().Context(), promotionID)
	if err != nil {
		return err
	}

	// 验证促销是否属于该代理商
	if promotion.ResellerID != resellerID {
		return echo.NewHTTPError(http.StatusForbidden, "Promotion does not belong to this reseller")
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":            promotion.ID,
		"code":          promotion.Code,
		"description":   promotion.Description,
		"type":          promotion.Type,
		"value":         promotion.Value,
		"minOrderValue": promotion.MinOrderValue,
		"maxDiscount":   promotion.MaxDiscount,
		"startDate":     promotion.StartDate,
		"endDate":       promotion.EndDate,
		"usageLimit":    promotion.UsageLimit,
		"currentUsage":  promotion.CurrentUsage,
		"status":        promotion.Status,
		"createdAt":     promotion.CreatedAt,
		"updatedAt":     promotion.UpdatedAt,
	})
}

// UpdateResellerPromotion 更新代理商促销码
func (h *DefaultPromotionHandler) UpdateResellerPromotion(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取促销ID
	promotionID := c.Param("id")
	if promotionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Promotion ID is required")
	}

	// 获取促销
	promo, err := h.promotionService.GetPromotionByID(c.Request().Context(), promotionID)
	if err != nil {
		return err
	}

	// 验证促销是否属于该代理商
	if promo.ResellerID != resellerID {
		return echo.NewHTTPError(http.StatusForbidden, "Promotion does not belong to this reseller")
	}

	// 解析请求
	var req struct {
		Description   string `json:"description"`
		Value         int    `json:"value" validate:"min=1"`
		MinOrderValue int    `json:"minOrderValue"`
		MaxDiscount   int    `json:"maxDiscount"`
		EndDate       string `json:"endDate"`
		UsageLimit    int    `json:"usageLimit" validate:"min=0"`
		Status        string `json:"status" validate:"omitempty,oneof=ACTIVE INACTIVE"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 处理可选参数
	description := promo.Description
	if req.Description != "" {
		description = req.Description
	}

	value := promo.Value
	if req.Value > 0 {
		value = int64(req.Value)
	}

	minOrderValue := promo.MinOrderValue
	if req.MinOrderValue >= 0 {
		minOrderValue = int64(req.MinOrderValue)
	}

	maxDiscount := promo.MaxDiscount
	if req.MaxDiscount >= 0 {
		maxDiscount = int64(req.MaxDiscount)
	}

	// 解析时间
	endDate := promo.EndDate
	if req.EndDate != "" {
		parsedEndDate, err := time.Parse("2006-01-02", req.EndDate)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid end date format")
		}
		// 设置结束日期为当天的最后一秒
		endDate = parsedEndDate.Add(24*time.Hour - time.Second)
	}

	usageLimit := promo.UsageLimit
	if req.UsageLimit >= 0 {
		usageLimit = req.UsageLimit
	}

	// Handle status changes
	if req.Status != "" {
		updatedPromotion, err := h.promotionService.UpdatePromotion(
			c.Request().Context(),
			promotionID,
			description,
			value,
			minOrderValue,
			maxDiscount,
			endDate,
			usageLimit,
			req.Status, // 直接传递status字符串
		)
		if err != nil {
			return err
		}

		// 返回响应
		return c.JSON(http.StatusOK, map[string]interface{}{
			"id":            updatedPromotion.ID,
			"description":   updatedPromotion.Description,
			"value":         updatedPromotion.Value,
			"minOrderValue": updatedPromotion.MinOrderValue,
			"maxDiscount":   updatedPromotion.MaxDiscount,
			"endDate":       updatedPromotion.EndDate,
			"usageLimit":    updatedPromotion.UsageLimit,
			"status":        updatedPromotion.Status,
			"updatedAt":     updatedPromotion.UpdatedAt,
		})
	}

	// If no status change, update with existing status
	// 使用现有状态，而不是空字符串
	updatedPromotion, err := h.promotionService.UpdatePromotion(
		c.Request().Context(),
		promotionID,
		description,
		value,
		minOrderValue,
		maxDiscount,
		endDate,
		usageLimit,
		string(promo.Status), // 使用原始促销的状态并转换为字符串
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":            updatedPromotion.ID,
		"description":   updatedPromotion.Description,
		"value":         updatedPromotion.Value,
		"minOrderValue": updatedPromotion.MinOrderValue,
		"maxDiscount":   updatedPromotion.MaxDiscount,
		"endDate":       updatedPromotion.EndDate,
		"usageLimit":    updatedPromotion.UsageLimit,
		"updatedAt":     updatedPromotion.UpdatedAt,
	})
}

// DeleteResellerPromotion 删除代理商促销码
func (h *DefaultPromotionHandler) DeleteResellerPromotion(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取促销ID
	promotionID := c.Param("id")
	if promotionID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Promotion ID is required")
	}

	// 获取促销
	promotion, err := h.promotionService.GetPromotionByID(c.Request().Context(), promotionID)
	if err != nil {
		return err
	}

	// 验证促销是否属于该代理商
	if promotion.ResellerID != resellerID {
		return echo.NewHTTPError(http.StatusForbidden, "Promotion does not belong to this reseller")
	}

	// 删除促销
	err = h.promotionService.DeletePromotion(c.Request().Context(), promotionID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]string{
		"message": "Promotion deleted successfully",
	})
}
