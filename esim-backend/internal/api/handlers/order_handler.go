package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/service"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderService *service.OrderService
}

// NewOrderHandler 创建新的订单处理器
func NewOrderHandler(orderService *service.OrderService) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
	}
}

// CreateOrder 创建订单
func (h *OrderHandler) CreateOrder(c echo.Context) error {
	// 获取用户
	currentUser := c.Get("user").(*user.User)

	// 解析请求
	var req struct {
		ProviderType  string                 `json:"providerType" validate:"required"`
		PackageID     string                 `json:"packageId" validate:"required"`
		Quantity      int                    `json:"quantity" validate:"required,min=1"`
		PromotionCode string                 `json:"promotionCode"`
		Metadata      map[string]interface{} `json:"metadata"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建订单
	order, err := h.orderService.CreateESIMOrder(
		c.Request().Context(),
		currentUser.ID,
		req.ProviderType,
		req.PackageID,
		req.Quantity,
		req.PromotionCode,
		req.Metadata,
	)
	if err != nil {
		return err
	}

	// 构建订单项响应
	items := make([]map[string]interface{}, len(order.Items))
	for i, item := range order.Items {
		items[i] = map[string]interface{}{
			"packageId":     item.PackageID,
			"packageName":   item.PackageName,
			"quantity":      item.Quantity,
			"unitPrice":     item.UnitPrice,
			"currency":      item.Currency,
			"dataVolume":    item.DataVolume,
			"validityDays":  item.ValidityDays,
			"locationCodes": item.LocationCodes,
		}
	}

	// 构建eSIM响应
	esims := make([]map[string]interface{}, len(order.ESIMs))
	for i, esim := range order.ESIMs {
		esims[i] = map[string]interface{}{
			"iccid":         esim.ICCID,
			"esimTranNo":    esim.ESIMTranNo,
			"status":        esim.Status,
			"activationUrl": esim.ActivationURL,
			"qrCodeUrl":     esim.QRCodeURL,
			"dataVolume":    esim.DataVolume,
			"validityDays":  esim.ValidityDays,
			"expiryDate":    esim.ExpiryDate,
		}
	}

	// 返回响应
	return c.JSON(http.StatusCreated, map[string]interface{}{
		"id":              order.ID,
		"providerType":    order.ProviderType,
		"transactionId":   order.TransactionID,
		"providerOrderNo": order.ProviderOrderNo,
		"status":          order.Status,
		"totalAmount":     order.TotalAmount,
		"currency":        order.Currency,
		"items":           items,
		"esims":           esims,
		"createdAt":       order.CreatedAt,
		"isPending":       order.Status == "PROCESSING",
	})
}

// GetOrder 获取订单详情
func (h *OrderHandler) GetOrder(c echo.Context) error {
	// 获取订单ID
	orderID := c.Param("id")
	if orderID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Order ID is required")
	}

	// 获取订单
	order, err := h.orderService.GetOrderByID(c.Request().Context(), orderID)
	if err != nil {
		return err
	}

	// 获取用户，检查权限
	currentUser := c.Get("user").(*user.User)
	if !currentUser.IsAdmin() && order.UserID != currentUser.ID {
		return echo.ErrForbidden
	}

	// 构建订单项响应
	items := make([]map[string]interface{}, len(order.Items))
	for i, item := range order.Items {
		items[i] = map[string]interface{}{
			"packageId":     item.PackageID,
			"packageName":   item.PackageName,
			"quantity":      item.Quantity,
			"unitPrice":     item.UnitPrice,
			"currency":      item.Currency,
			"dataVolume":    item.DataVolume,
			"validityDays":  item.ValidityDays,
			"locationCodes": item.LocationCodes,
		}
	}

	// 构建eSIM响应
	esims := make([]map[string]interface{}, len(order.ESIMs))
	for i, esim := range order.ESIMs {
		esims[i] = map[string]interface{}{
			"iccid":         esim.ICCID,
			"esimTranNo":    esim.ESIMTranNo,
			"status":        esim.Status,
			"activationUrl": esim.ActivationURL,
			"qrCodeUrl":     esim.QRCodeURL,
			"dataVolume":    esim.DataVolume,
			"validityDays":  esim.ValidityDays,
			"expiryDate":    esim.ExpiryDate,
			"activatedAt":   esim.ActivatedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":              order.ID,
		"providerType":    order.ProviderType,
		"transactionId":   order.TransactionID,
		"providerOrderNo": order.ProviderOrderNo,
		"status":          order.Status,
		"totalAmount":     order.TotalAmount,
		"currency":        order.Currency,
		"items":           items,
		"esims":           esims,
		"createdAt":       order.CreatedAt,
		"updatedAt":       order.UpdatedAt,
		"completedAt":     order.CompletedAt,
		"promotionId":     order.PromotionID,
		"metadata":        order.Metadata,
	})
}

// ListOrders 获取订单列表
func (h *OrderHandler) ListOrders(c echo.Context) error {
	// 获取用户
	currentUser := c.Get("user").(*user.User)

	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 解析时间范围
	var startDate, endDate time.Time
	if startDateStr := c.QueryParam("startDate"); startDateStr != "" {
		var err error
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid startDate format")
		}
	} else {
		// 默认30天前
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr := c.QueryParam("endDate"); endDateStr != "" {
		var err error
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid endDate format")
		}
		// 设置为当天结束
		endDate = endDate.Add(24 * time.Hour).Add(-time.Second)
	} else {
		// 默认今天
		endDate = time.Now()
	}

	// 获取订单列表
	orders, total, err := h.orderService.GetUserOrders(c.Request().Context(), currentUser.ID, startDate, endDate, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	orderList := make([]map[string]interface{}, len(orders))
	for i, order := range orders {
		// 简化的eSIM信息
		esimCount := len(order.ESIMs)
		esimStatus := make(map[string]int)
		for _, esim := range order.ESIMs {
			esimStatus[esim.Status]++
		}

		orderList[i] = map[string]interface{}{
			"id":           order.ID,
			"providerType": order.ProviderType,
			"status":       order.Status,
			"totalAmount":  order.TotalAmount,
			"currency":     order.Currency,
			"createdAt":    order.CreatedAt,
			"completedAt":  order.CompletedAt,
			"esimCount":    esimCount,
			"esimStatus":   esimStatus,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"orders": orderList,
		"pagination": map[string]interface{}{
			"total":     total,
			"page":      page,
			"pageSize":  pageSize,
			"startDate": startDate.Format("2006-01-02"),
			"endDate":   endDate.Format("2006-01-02"),
		},
	})
}

// 添加getOrderStatusDesc函数用于返回订单状态的中文描述
func getOrderStatusDesc(status string) string {
	switch status {
	case "PENDING":
		return "待处理"
	case "PROCESSING":
		return "处理中"
	case "COMPLETED":
		return "已完成"
	case "CANCELLED":
		return "已取消"
	case "FAILED":
		return "失败"
	default:
		return "未知状态"
	}
}

// UpdateOrderStatus 更新订单状态（目前仅支持取消订单）
func (h *OrderHandler) UpdateOrderStatus(c echo.Context) error {
	// 获取订单ID
	orderID := c.Param("id")
	if orderID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Order ID is required")
	}

	// 解析请求体
	var req struct {
		Status string `json:"status" validate:"required,eq=CANCELLED"`
		Reason string `json:"reason"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid status value. Currently only 'CANCELLED' is supported")
	}

	// 获取订单
	order, err := h.orderService.GetOrderByID(c.Request().Context(), orderID)
	if err != nil {
		return err
	}

	// 获取用户，检查权限
	currentUser := c.Get("user").(*user.User)
	if !currentUser.IsAdmin() && order.UserID != currentUser.ID {
		return echo.NewHTTPError(http.StatusForbidden, "No permission to update this order")
	}

	// 检查订单状态是否允许取消
	if order.Status == "COMPLETED" || order.Status == "CANCELLED" {
		return echo.NewHTTPError(http.StatusConflict, map[string]interface{}{
			"code":    409,
			"message": "订单已" + getOrderStatusDesc(string(order.Status)) + "，无法取消",
			"status":  order.Status,
		})
	}

	// 取消订单
	err = h.orderService.CancelOrder(c.Request().Context(), orderID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":        orderID,
		"status":    "CANCELLED",
		"message":   "订单已成功取消",
		"updatedAt": time.Now().Format(time.RFC3339),
	})
}

// ListUserOrders 获取用户订单列表
func (h *OrderHandler) ListUserOrders(c echo.Context) error {
	// 获取用户
	currentUser := c.Get("user").(*user.User)

	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 解析时间范围
	var startDate, endDate time.Time
	if startDateStr := c.QueryParam("startDate"); startDateStr != "" {
		var err error
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid startDate format")
		}
	} else {
		// 默认为30天前
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr := c.QueryParam("endDate"); endDateStr != "" {
		var err error
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid endDate format")
		}
		// 设置为当天结束
		endDate = endDate.Add(24 * time.Hour).Add(-time.Second)
	} else {
		// 默认为今天
		endDate = time.Now()
	}

	// 调用OrderService获取用户订单
	orders, total, err := h.orderService.GetUserOrders(
		c.Request().Context(),
		currentUser.ID,
		startDate,
		endDate,
		page,
		pageSize,
	)
	if err != nil {
		return err
	}

	// 转换为API响应格式
	result := make([]map[string]interface{}, len(orders))
	for i, order := range orders {
		result[i] = map[string]interface{}{
			"id":           order.ID,
			"status":       order.Status,
			"totalAmount":  order.TotalAmount,
			"currency":     order.Currency,
			"providerType": order.ProviderType,
			"packageCount": len(order.Items),
			"esimCount":    len(order.ESIMs),
			"createdAt":    order.CreatedAt,
			"completedAt":  order.CompletedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"orders": result,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}
