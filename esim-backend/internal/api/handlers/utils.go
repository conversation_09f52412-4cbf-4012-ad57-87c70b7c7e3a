package handlers

import (
	"net/http"
	"strings"

	"vereal/letsesim/pkg/esim"

	"github.com/labstack/echo/v4"
)

// ErrorResponse 返回标准化的错误响应
func ErrorResponse(c echo.Context, statusCode int, message string) error {
	return c.JSON(statusCode, map[string]interface{}{
		"code":    statusCode,
		"message": message,
	})
}

// BadRequestError 返回400错误
func BadRequestError(c echo.Context, message string) error {
	return ErrorResponse(c, http.StatusBadRequest, message)
}

// NotFoundError 返回404错误
func NotFoundError(c echo.Context, message string) error {
	return ErrorResponse(c, http.StatusNotFound, message)
}

// ForbiddenError 返回403错误
func ForbiddenError(c echo.Context, message string) error {
	return ErrorResponse(c, http.StatusForbidden, message)
}

// ConflictError 返回409错误
func ConflictError(c echo.Context, message string) error {
	return ErrorResponse(c, http.StatusConflict, message)
}

// UnauthorizedError 返回401错误
func UnauthorizedError(c echo.Context, message string) error {
	return ErrorResponse(c, http.StatusUnauthorized, message)
}

// InternalServerError 返回500错误
func InternalServerError(c echo.Context, message string) error {
	return ErrorResponse(c, http.StatusInternalServerError, message)
}

// IsNotFoundError 检查是否为"未找到"类型的错误
func IsNotFoundError(err error) bool {
	if err == nil {
		return false
	}

	esimErr, ok := err.(*esim.ESIMError)
	if ok && esimErr.Code == esim.ErrNotFound {
		return true
	}

	return strings.Contains(strings.ToLower(err.Error()), "not found")
}

// GetValidationErrorMessage 从验证错误中提取友好的错误消息
func GetValidationErrorMessage(err error, field string) string {
	errMsg := err.Error()

	if strings.Contains(errMsg, field) {
		switch {
		case strings.Contains(errMsg, "required"):
			return field + "是必需的"
		case strings.Contains(errMsg, "min"):
			return field + "值太小"
		case strings.Contains(errMsg, "max"):
			return field + "值太大"
		case strings.Contains(errMsg, "email"):
			return "请输入有效的电子邮箱地址"
		}
	}

	return errMsg
}
