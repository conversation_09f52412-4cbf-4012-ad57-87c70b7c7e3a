package handlers

import (
	"net/http"
	"strconv"

	"vereal/letsesim/internal/domain/rate"
	"vereal/letsesim/internal/domain/reseller"
	"vereal/letsesim/internal/service"
	"vereal/letsesim/pkg/esim"

	"github.com/labstack/echo/v4"
)

// 费率请求结构
type RateRequest struct {
	PackageID string `json:"packageId" validate:"required"`
	Country   string `json:"country" validate:"required"`
	Rate      int64  `json:"rate" validate:"required,gt=0"`
	Currency  string `json:"currency" validate:"required"`
}

// 费率响应结构
type RateResponse struct {
	ID        string `json:"id"`
	PackageID string `json:"packageId"`
	Country   string `json:"country"`
	Rate      int64  `json:"rate"`
	Currency  string `json:"currency"`
	OwnerType string `json:"ownerType"`
	OwnerID   string `json:"ownerId,omitempty"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

// DefaultRateHandler 默认费率处理器实现
type DefaultRateHandler struct {
	rateService *service.RateService
}

// NewRateHandler 创建费率处理器
func NewRateHandler(rateService *service.RateService) RateHandlerInterface {
	return &DefaultRateHandler{
		rateService: rateService,
	}
}

// toResponse 将领域模型转换为API响应
func toRateResponse(r *rate.Rate) *RateResponse {
	return &RateResponse{
		ID:        r.ID,
		PackageID: r.PackageID,
		Country:   r.Country,
		Rate:      r.Rate,
		Currency:  r.Currency,
		OwnerType: r.OwnerType,
		OwnerID:   r.OwnerID,
		CreatedAt: r.CreatedAt.Format(http.TimeFormat),
		UpdatedAt: r.UpdatedAt.Format(http.TimeFormat),
	}
}

// toRateResponses 将领域模型列表转换为API响应列表
func toRateResponses(rates []*rate.Rate) []*RateResponse {
	result := make([]*RateResponse, len(rates))
	for i, r := range rates {
		result[i] = toRateResponse(r)
	}
	return result
}

// ListGlobalRates 列出全局费率
func (h *DefaultRateHandler) ListGlobalRates(c echo.Context) error {
	// 获取分页参数
	page, pageSize := getPagination(c)

	// 获取过滤参数
	filters := getFilters(c, []string{"package_id", "country"})

	// 获取费率列表
	rates, total, err := h.rateService.ListGlobalRates(c.Request().Context(), filters, page, pageSize)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// 构造响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"rates": toRateResponses(rates),
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// CreateGlobalRate 创建全局费率
func (h *DefaultRateHandler) CreateGlobalRate(c echo.Context) error {
	// 解析请求
	var req RateRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建费率
	newRate, err := h.rateService.CreateGlobalRate(c.Request().Context(), service.RateRequest{
		PackageID: req.PackageID,
		Country:   req.Country,
		Rate:      req.Rate,
		Currency:  req.Currency,
	})
	if err != nil {
		code := http.StatusInternalServerError
		if esimErr, ok := err.(*esim.ESIMError); ok {
			code = esimErr.HTTPStatus
		}
		return echo.NewHTTPError(code, err.Error())
	}

	// 返回创建的费率
	return c.JSON(http.StatusCreated, toRateResponse(newRate))
}

// UpdateGlobalRate 更新全局费率
func (h *DefaultRateHandler) UpdateGlobalRate(c echo.Context) error {
	// 获取费率ID
	id := c.Param("id")
	if id == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid rate ID")
	}

	// 解析请求
	var req RateRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 更新费率
	updatedRate, err := h.rateService.UpdateRate(c.Request().Context(), id, service.RateRequest{
		PackageID: req.PackageID,
		Country:   req.Country,
		Rate:      req.Rate,
		Currency:  req.Currency,
	})
	if err != nil {
		code := http.StatusInternalServerError
		if esimErr, ok := err.(*esim.ESIMError); ok {
			code = esimErr.HTTPStatus
		}
		return echo.NewHTTPError(code, err.Error())
	}

	// 返回更新后的费率
	return c.JSON(http.StatusOK, toRateResponse(updatedRate))
}

// DeleteGlobalRate 删除全局费率
func (h *DefaultRateHandler) DeleteGlobalRate(c echo.Context) error {
	// 获取费率ID
	id := c.Param("id")
	if id == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid rate ID")
	}

	// 删除费率
	err := h.rateService.DeleteRate(c.Request().Context(), id)
	if err != nil {
		code := http.StatusInternalServerError
		if esimErr, ok := err.(*esim.ESIMError); ok {
			code = esimErr.HTTPStatus
		}
		return echo.NewHTTPError(code, err.Error())
	}

	// 返回成功消息
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Rate deleted successfully",
	})
}

// ListResellerRates 列出代理商费率
func (h *DefaultRateHandler) ListResellerRates(c echo.Context) error {
	// 获取代理商信息
	resellerObj := c.Get("reseller")
	if resellerObj == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Reseller not found in context")
	}
	reseller, ok := resellerObj.(*reseller.Reseller)
	if !ok {
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid reseller object in context")
	}

	// 获取分页参数
	page, pageSize := getPagination(c)

	// 获取过滤参数
	filters := getFilters(c, []string{"package_id", "country"})

	// 获取费率列表
	rates, total, err := h.rateService.ListResellerRates(c.Request().Context(), reseller.ID, filters, page, pageSize)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// 构造响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"rates": toRateResponses(rates),
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// CreateResellerRate 创建代理商费率
func (h *DefaultRateHandler) CreateResellerRate(c echo.Context) error {
	// 获取代理商信息
	resellerObj := c.Get("reseller")
	if resellerObj == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Reseller not found in context")
	}
	reseller, ok := resellerObj.(*reseller.Reseller)
	if !ok {
		return echo.NewHTTPError(http.StatusInternalServerError, "Invalid reseller object in context")
	}

	// 解析请求
	var req RateRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建费率
	newRate, err := h.rateService.CreateResellerRate(c.Request().Context(), reseller.ID, service.RateRequest{
		PackageID: req.PackageID,
		Country:   req.Country,
		Rate:      req.Rate,
		Currency:  req.Currency,
	})
	if err != nil {
		code := http.StatusInternalServerError
		if esimErr, ok := err.(*esim.ESIMError); ok {
			code = esimErr.HTTPStatus
		}
		return echo.NewHTTPError(code, err.Error())
	}

	// 返回创建的费率
	return c.JSON(http.StatusCreated, toRateResponse(newRate))
}

// ListEnterpriseRates 列出企业代理商费率
func (h *DefaultRateHandler) ListEnterpriseRates(c echo.Context) error {
	// 获取企业代理商信息
	resellerObj := c.Get("reseller")
	if resellerObj == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Enterprise not found in context")
	}
	enterprise, ok := resellerObj.(*reseller.Reseller)
	if !ok || !enterprise.IsEnterprise() {
		return echo.NewHTTPError(http.StatusForbidden, "Only enterprise resellers can access this resource")
	}

	// 获取分页参数
	page, pageSize := getPagination(c)

	// 获取过滤参数
	filters := getFilters(c, []string{"package_id", "country"})

	// 获取费率列表
	rates, total, err := h.rateService.ListEnterpriseRates(c.Request().Context(), enterprise.ID, filters, page, pageSize)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}

	// 构造响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"rates": toRateResponses(rates),
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// CreateEnterpriseRate 创建企业代理商费率
func (h *DefaultRateHandler) CreateEnterpriseRate(c echo.Context) error {
	// 获取企业代理商信息
	resellerObj := c.Get("reseller")
	if resellerObj == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Enterprise not found in context")
	}
	enterprise, ok := resellerObj.(*reseller.Reseller)
	if !ok || !enterprise.IsEnterprise() {
		return echo.NewHTTPError(http.StatusForbidden, "Only enterprise resellers can access this resource")
	}

	// 解析请求
	var req RateRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建费率
	newRate, err := h.rateService.CreateEnterpriseRate(c.Request().Context(), enterprise.ID, service.RateRequest{
		PackageID: req.PackageID,
		Country:   req.Country,
		Rate:      req.Rate,
		Currency:  req.Currency,
	})
	if err != nil {
		code := http.StatusInternalServerError
		if esimErr, ok := err.(*esim.ESIMError); ok {
			code = esimErr.HTTPStatus
		}
		return echo.NewHTTPError(code, err.Error())
	}

	// 返回创建的费率
	return c.JSON(http.StatusCreated, toRateResponse(newRate))
}

// 辅助函数：获取分页参数
func getPagination(c echo.Context) (int, int) {
	pageStr := c.QueryParam("page")
	pageSizeStr := c.QueryParam("pageSize")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	return page, pageSize
}

// 辅助函数：获取过滤参数
func getFilters(c echo.Context, allowedFilters []string) map[string]interface{} {
	filters := make(map[string]interface{})

	for _, filter := range allowedFilters {
		value := c.QueryParam(filter)
		if value != "" {
			// 将API参数名转换为数据库字段名
			dbField := filter
			filters[dbField] = value
		}
	}

	return filters
}
