package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"vereal/letsesim/internal/service"
)

// DateTimeFormat 日期时间格式
const DateTimeFormat = "2006-01-02T15:04:05Z07:00"

// EnterpriseHandlerImpl 企业代理商处理器实现
type EnterpriseHandlerImpl struct {
	enterpriseService *service.EnterpriseService
	logger            *zap.Logger
}

// NewEnterpriseHandler 创建新的企业代理商处理器
func NewEnterpriseHandler(enterpriseService *service.EnterpriseService, logger *zap.Logger) EnterpriseHandlerInterface {
	return &EnterpriseHandlerImpl{
		enterpriseService: enterpriseService,
		logger:            logger,
	}
}

// CreateEmployeeRequest 创建员工请求
type CreateEmployeeRequest struct {
	Email        string `json:"email" validate:"required,email"`
	Name         string `json:"name" validate:"required"`
	Department   string `json:"department" validate:"required"`
	Mobile       string `json:"mobile" validate:"omitempty"`
	Password     string `json:"password" validate:"required,min=8"`
	Position     string `json:"position" validate:"omitempty"`
	EmployeeCode string `json:"employeeCode" validate:"omitempty"`
}

// EmployeeResponse 员工响应
type EmployeeResponse struct {
	ID         string `json:"id"`
	Email      string `json:"email"`
	Name       string `json:"name"`
	Department string `json:"department"`
	Position   string `json:"position,omitempty"`
	Mobile     string `json:"mobile,omitempty"`
	Status     string `json:"status"`
	CreatedAt  string `json:"createdAt"`
}

// DepartmentRequest 部门请求
type DepartmentRequest struct {
	Name        string `json:"name" validate:"required"`
	Description string `json:"description" validate:"omitempty"`
}

// DepartmentResponse 部门响应
type DepartmentResponse struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description,omitempty"`
	CreatedAt   string `json:"createdAt"`
}

// ESIMAssignRequest eSIM分配请求
type ESIMAssignRequest struct {
	EmployeeIDs []string `json:"employeeIds" validate:"required"`
	ESIMIDs     []string `json:"esimIds" validate:"required"`
}

// ESIMReclaimRequest eSIM回收请求
type ESIMReclaimRequest struct {
	ESIMIDs []string `json:"esimIds" validate:"required"`
}

// ListEmployees 列出员工
func (h *EnterpriseHandlerImpl) ListEmployees(c echo.Context) error {
	// 从请求中获取企业ID
	enterpriseID := c.Get("enterpriseId").(string)

	// 获取分页参数
	page, pageSize := getPaginationParams(c)

	// 构建过滤条件
	filter := map[string]interface{}{
		"enterprise_id": enterpriseID,
	}

	// 如果提供了部门过滤，添加到条件中
	department := c.QueryParam("department")
	if department != "" {
		filter["department_id"] = department
	}

	// 获取员工列表
	employees, total, err := h.enterpriseService.ListEmployees(c.Request().Context(), filter, page, pageSize)
	if err != nil {
		h.logger.Error("获取员工列表失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取员工列表失败: "+err.Error())
	}

	// 将员工转换为响应格式
	response := struct {
		Employees  []EmployeeResponse `json:"employees"`
		Pagination struct {
			Total    int64 `json:"total"`
			Page     int   `json:"page"`
			PageSize int   `json:"pageSize"`
		} `json:"pagination"`
	}{
		Pagination: struct {
			Total    int64 `json:"total"`
			Page     int   `json:"page"`
			PageSize int   `json:"pageSize"`
		}{
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		},
		Employees: make([]EmployeeResponse, 0, len(employees)),
	}

	// 将员工数据转换为响应格式
	for _, emp := range employees {
		response.Employees = append(response.Employees, EmployeeResponse{
			ID: emp.ID,
			// 其他字段需要从用户信息获取，先留空
			Department: emp.DepartmentID,
			Position:   emp.Position,
			CreatedAt:  emp.CreatedAt.Format(DateTimeFormat),
		})
	}

	// 返回响应
	return c.JSON(http.StatusOK, response)
}

// CreateEmployee 创建员工
func (h *EnterpriseHandlerImpl) CreateEmployee(c echo.Context) error {
	// 从请求中获取企业ID
	enterpriseID := c.Get("enterpriseId").(string)

	// 解析请求体
	req := new(CreateEmployeeRequest)
	if err := c.Bind(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式: "+err.Error())
	}

	// 验证请求
	if err := c.Validate(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "请求验证失败: "+err.Error())
	}

	// 创建员工
	employee, user, err := h.enterpriseService.CreateEmployeeWithUser(
		c.Request().Context(),
		enterpriseID,
		req.Email,
		req.Name,
		req.Department,
		req.Position,
		req.Mobile,
		req.Password,
	)
	if err != nil {
		h.logger.Error("创建员工失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "创建员工失败: "+err.Error())
	}

	// 创建响应
	response := EmployeeResponse{
		ID:         employee.ID,
		Email:      user.Email,
		Name:       user.Name,
		Department: req.Department,
		Position:   employee.Position,
		Mobile:     user.Mobile,
		Status:     string(user.Status),
		CreatedAt:  employee.CreatedAt.Format(DateTimeFormat),
	}

	return c.JSON(http.StatusCreated, response)
}

// GetEmployee 获取员工详情
func (h *EnterpriseHandlerImpl) GetEmployee(c echo.Context) error {
	// 获取员工ID
	employeeID := c.Param("id")
	if employeeID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "员工ID不能为空")
	}

	// 获取员工
	employee, err := h.enterpriseService.GetEmployeeByID(c.Request().Context(), employeeID)
	if err != nil {
		h.logger.Error("获取员工失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取员工失败: "+err.Error())
	}

	// 创建响应
	// TODO: 获取用户信息以丰富员工响应
	response := struct {
		ID           string `json:"id"`
		UserID       string `json:"userId"`
		EnterpriseID string `json:"enterpriseId"`
		DepartmentID string `json:"departmentId,omitempty"`
		Position     string `json:"position,omitempty"`
		EmployeeCode string `json:"employeeCode,omitempty"`
		CreatedAt    string `json:"createdAt"`
		UpdatedAt    string `json:"updatedAt"`
	}{
		ID:           employee.ID,
		UserID:       employee.UserID,
		EnterpriseID: employee.EnterpriseID,
		DepartmentID: employee.DepartmentID,
		Position:     employee.Position,
		EmployeeCode: employee.EmployeeCode,
		CreatedAt:    employee.CreatedAt.Format(DateTimeFormat),
		UpdatedAt:    employee.UpdatedAt.Format(DateTimeFormat),
	}

	return c.JSON(http.StatusOK, response)
}

// UpdateEmployee 更新员工信息
func (h *EnterpriseHandlerImpl) UpdateEmployee(c echo.Context) error {
	// 获取员工ID
	employeeID := c.Param("id")
	if employeeID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "员工ID不能为空")
	}

	// 解析请求体
	req := struct {
		DepartmentID string `json:"departmentId" validate:"omitempty"`
		Position     string `json:"position" validate:"omitempty"`
		EmployeeCode string `json:"employeeCode" validate:"omitempty"`
	}{}
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式: "+err.Error())
	}

	// 更新员工
	employee, err := h.enterpriseService.UpdateEmployee(
		c.Request().Context(),
		employeeID,
		req.DepartmentID,
		req.Position,
		req.EmployeeCode,
	)
	if err != nil {
		h.logger.Error("更新员工失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新员工失败: "+err.Error())
	}

	// 创建响应
	response := struct {
		ID           string `json:"id"`
		DepartmentID string `json:"departmentId,omitempty"`
		Position     string `json:"position,omitempty"`
		EmployeeCode string `json:"employeeCode,omitempty"`
		UpdatedAt    string `json:"updatedAt"`
	}{
		ID:           employee.ID,
		DepartmentID: employee.DepartmentID,
		Position:     employee.Position,
		EmployeeCode: employee.EmployeeCode,
		UpdatedAt:    employee.UpdatedAt.Format(DateTimeFormat),
	}

	return c.JSON(http.StatusOK, response)
}

// DeleteEmployee 删除员工
func (h *EnterpriseHandlerImpl) DeleteEmployee(c echo.Context) error {
	// 获取员工ID
	employeeID := c.Param("id")
	if employeeID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "员工ID不能为空")
	}

	// 删除员工
	err := h.enterpriseService.DeleteEmployee(c.Request().Context(), employeeID)
	if err != nil {
		h.logger.Error("删除员工失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "删除员工失败: "+err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "员工删除成功"})
}

// ListDepartments 列出部门
func (h *EnterpriseHandlerImpl) ListDepartments(c echo.Context) error {
	// 从请求中获取企业ID
	enterpriseID := c.Get("enterpriseId").(string)

	// 获取分页参数
	page, pageSize := getPaginationParams(c)

	// 获取部门列表
	departments, total, err := h.enterpriseService.ListDepartments(
		c.Request().Context(),
		enterpriseID,
		page,
		pageSize,
	)
	if err != nil {
		h.logger.Error("获取部门列表失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取部门列表失败: "+err.Error())
	}

	// 创建响应
	response := struct {
		Departments []struct {
			ID          string `json:"id"`
			Name        string `json:"name"`
			Description string `json:"description,omitempty"`
			CreatedAt   string `json:"createdAt"`
		} `json:"departments"`
		Pagination struct {
			Total    int64 `json:"total"`
			Page     int   `json:"page"`
			PageSize int   `json:"pageSize"`
		} `json:"pagination"`
	}{
		Pagination: struct {
			Total    int64 `json:"total"`
			Page     int   `json:"page"`
			PageSize int   `json:"pageSize"`
		}{
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		},
	}

	// 填充部门数据
	for _, dept := range departments {
		response.Departments = append(response.Departments, struct {
			ID          string `json:"id"`
			Name        string `json:"name"`
			Description string `json:"description,omitempty"`
			CreatedAt   string `json:"createdAt"`
		}{
			ID:          dept.ID,
			Name:        dept.Name,
			Description: dept.Description,
			CreatedAt:   dept.CreatedAt.Format(DateTimeFormat),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// CreateDepartment 创建部门
func (h *EnterpriseHandlerImpl) CreateDepartment(c echo.Context) error {
	// 从请求中获取企业ID
	enterpriseID := c.Get("enterpriseId").(string)

	// 解析请求体
	req := new(DepartmentRequest)
	if err := c.Bind(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式: "+err.Error())
	}

	// 验证请求
	if err := c.Validate(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "请求验证失败: "+err.Error())
	}

	// 创建部门
	department, err := h.enterpriseService.CreateDepartment(
		c.Request().Context(),
		enterpriseID,
		req.Name,
		req.Description,
	)
	if err != nil {
		h.logger.Error("创建部门失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "创建部门失败: "+err.Error())
	}

	// 创建响应
	response := DepartmentResponse{
		ID:          department.ID,
		Name:        department.Name,
		Description: department.Description,
		CreatedAt:   department.CreatedAt.Format(DateTimeFormat),
	}

	return c.JSON(http.StatusCreated, response)
}

// GetDepartment 获取部门详情
func (h *EnterpriseHandlerImpl) GetDepartment(c echo.Context) error {
	// 获取部门ID
	departmentID := c.Param("id")
	if departmentID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "部门ID不能为空")
	}

	// 获取部门
	department, err := h.enterpriseService.GetDepartmentByID(c.Request().Context(), departmentID)
	if err != nil {
		h.logger.Error("获取部门失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取部门失败: "+err.Error())
	}

	// 创建响应
	response := struct {
		ID           string `json:"id"`
		EnterpriseID string `json:"enterpriseId"`
		Name         string `json:"name"`
		Description  string `json:"description,omitempty"`
		CreatedAt    string `json:"createdAt"`
		UpdatedAt    string `json:"updatedAt"`
	}{
		ID:           department.ID,
		EnterpriseID: department.EnterpriseID,
		Name:         department.Name,
		Description:  department.Description,
		CreatedAt:    department.CreatedAt.Format(DateTimeFormat),
		UpdatedAt:    department.UpdatedAt.Format(DateTimeFormat),
	}

	return c.JSON(http.StatusOK, response)
}

// UpdateDepartment 更新部门信息
func (h *EnterpriseHandlerImpl) UpdateDepartment(c echo.Context) error {
	// 获取部门ID
	departmentID := c.Param("id")
	if departmentID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "部门ID不能为空")
	}

	// 解析请求体
	req := new(DepartmentRequest)
	if err := c.Bind(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式: "+err.Error())
	}

	// 更新部门
	department, err := h.enterpriseService.UpdateDepartment(
		c.Request().Context(),
		departmentID,
		req.Name,
		req.Description,
	)
	if err != nil {
		h.logger.Error("更新部门失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "更新部门失败: "+err.Error())
	}

	// 创建响应
	response := struct {
		ID          string `json:"id"`
		Name        string `json:"name"`
		Description string `json:"description,omitempty"`
		UpdatedAt   string `json:"updatedAt"`
	}{
		ID:          department.ID,
		Name:        department.Name,
		Description: department.Description,
		UpdatedAt:   department.UpdatedAt.Format(DateTimeFormat),
	}

	return c.JSON(http.StatusOK, response)
}

// DeleteDepartment 删除部门
func (h *EnterpriseHandlerImpl) DeleteDepartment(c echo.Context) error {
	// 获取部门ID
	departmentID := c.Param("id")
	if departmentID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "部门ID不能为空")
	}

	// 删除部门
	err := h.enterpriseService.DeleteDepartment(c.Request().Context(), departmentID)
	if err != nil {
		h.logger.Error("删除部门失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "删除部门失败: "+err.Error())
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "部门删除成功"})
}

// AssignESIMs 分配eSIM
func (h *EnterpriseHandlerImpl) AssignESIMs(c echo.Context) error {
	// 解析请求体
	req := new(ESIMAssignRequest)
	if err := c.Bind(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式: "+err.Error())
	}

	// 验证请求
	if err := c.Validate(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "请求验证失败: "+err.Error())
	}

	// 分配eSIM
	count, err := h.enterpriseService.AssignESIMs(
		c.Request().Context(),
		req.EmployeeIDs,
		req.ESIMIDs,
	)
	if err != nil {
		h.logger.Error("分配eSIM失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "分配eSIM失败: "+err.Error())
	}

	// 创建响应
	response := struct {
		Success  bool   `json:"success"`
		Assigned int    `json:"assigned"`
		Message  string `json:"message"`
	}{
		Success:  true,
		Assigned: count,
		Message:  "eSIM分配成功",
	}

	return c.JSON(http.StatusOK, response)
}

// ReclaimESIMs 回收eSIM
func (h *EnterpriseHandlerImpl) ReclaimESIMs(c echo.Context) error {
	// 解析请求体
	req := new(ESIMReclaimRequest)
	if err := c.Bind(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "无效的请求格式: "+err.Error())
	}

	// 验证请求
	if err := c.Validate(req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "请求验证失败: "+err.Error())
	}

	// 回收eSIM
	count, err := h.enterpriseService.ReclaimESIMs(
		c.Request().Context(),
		req.ESIMIDs,
	)
	if err != nil {
		h.logger.Error("回收eSIM失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "回收eSIM失败: "+err.Error())
	}

	// 创建响应
	response := struct {
		Success   bool   `json:"success"`
		Reclaimed int    `json:"reclaimed"`
		Message   string `json:"message"`
	}{
		Success:   true,
		Reclaimed: count,
		Message:   "eSIM回收成功",
	}

	return c.JSON(http.StatusOK, response)
}

// GetEmployeeESIMs 获取员工的eSIM列表
func (h *EnterpriseHandlerImpl) GetEmployeeESIMs(c echo.Context) error {
	// 获取员工ID
	employeeID := c.Param("id")
	if employeeID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "员工ID不能为空")
	}

	// 获取eSIM列表
	esims, err := h.enterpriseService.GetEmployeeESIMs(c.Request().Context(), employeeID)
	if err != nil {
		h.logger.Error("获取员工eSIM列表失败", zap.Error(err))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取员工eSIM列表失败: "+err.Error())
	}

	// 创建响应
	response := struct {
		ESIMs []struct {
			ICCID      string `json:"iccid"`
			ESIMTranNo string `json:"esimTranNo"`
			Status     string `json:"status"`
			QRCodeURL  string `json:"qrCodeURL,omitempty"`
			DataVolume int64  `json:"dataVolume"`
			UsedData   int64  `json:"usedData"`
			ExpiryTime string `json:"expiryTime"`
		} `json:"esims"`
	}{}

	// 填充eSIM数据
	for _, e := range esims {
		response.ESIMs = append(response.ESIMs, struct {
			ICCID      string `json:"iccid"`
			ESIMTranNo string `json:"esimTranNo"`
			Status     string `json:"status"`
			QRCodeURL  string `json:"qrCodeURL,omitempty"`
			DataVolume int64  `json:"dataVolume"`
			UsedData   int64  `json:"usedData"`
			ExpiryTime string `json:"expiryTime"`
		}{
			ICCID:      e.Identifier.ICCID,
			ESIMTranNo: e.Identifier.ESIMTranNo,
			Status:     e.Status.Code,
			QRCodeURL:  e.QRCodeURL,
			DataVolume: e.DataVolume,
			UsedData:   e.UsedData,
			ExpiryTime: e.ExpiryTime.Format(DateTimeFormat),
		})
	}

	return c.JSON(http.StatusOK, response)
}

// ListEnterprises 列出企业代理商
func (h *EnterpriseHandlerImpl) ListEnterprises(c echo.Context) error {
	return echo.NewHTTPError(http.StatusNotImplemented, "功能尚未实现")
}

// GetEnterprise 获取企业代理商详情
func (h *EnterpriseHandlerImpl) GetEnterprise(c echo.Context) error {
	return echo.NewHTTPError(http.StatusNotImplemented, "功能尚未实现")
}

// UpdateEnterprise 更新企业代理商信息
func (h *EnterpriseHandlerImpl) UpdateEnterprise(c echo.Context) error {
	return echo.NewHTTPError(http.StatusNotImplemented, "功能尚未实现")
}

// 辅助函数

// getPaginationParams 获取分页参数
func getPaginationParams(c echo.Context) (int, int) {
	page := 1
	pageSize := 10

	pageStr := c.QueryParam("page")
	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSizeStr := c.QueryParam("pageSize")
	if pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	return page, pageSize
}
