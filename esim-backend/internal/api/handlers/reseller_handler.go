package handlers

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"

	"vereal/letsesim/internal/domain/order"
	"vereal/letsesim/internal/domain/reseller"
	"vereal/letsesim/internal/service"
)

// ResellerHandler 代理商处理器
type ResellerHandler struct {
	resellerService *service.ResellerService
}

// NewResellerHandler 创建新的代理商处理器
func NewResellerHandler(resellerService *service.ResellerService) *ResellerHandler {
	return &ResellerHandler{
		resellerService: resellerService,
	}
}

// CreateReseller 创建代理商（管理员）
func (h *ResellerHandler) CreateReseller(c echo.Context) error {
	// 解析请求
	var req struct {
		Email          string                 `json:"email" validate:"required,email"`
		Password       string                 `json:"password" validate:"required,min=8"`
		CompanyName    string                 `json:"companyName" validate:"required"`
		ContactPerson  string                 `json:"contactPerson" validate:"required"`
		Phone          string                 `json:"phone" validate:"required"`
		Country        string                 `json:"country" validate:"required"`
		Address        string                 `json:"address"`
		CommissionRate int                    `json:"commissionRate" validate:"min=0,max=10000"`
		Metadata       map[string]interface{} `json:"metadata"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建代理商
	resellerObj, err := h.resellerService.CreateReseller(
		c.Request().Context(),
		req.Email,
		req.Password,
		req.CompanyName,
		req.ContactPerson,
		req.Phone,
		req.Country,
		req.Address,
		req.CommissionRate,
		req.Metadata,
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusCreated, map[string]interface{}{
		"id":             resellerObj.ID,
		"companyName":    resellerObj.CompanyName,
		"contactPerson":  resellerObj.ContactPerson,
		"email":          resellerObj.Email,
		"phone":          resellerObj.Phone,
		"country":        resellerObj.Country,
		"address":        resellerObj.Address,
		"commissionRate": resellerObj.CommissionRate,
		"status":         resellerObj.Status,
		"createdAt":      resellerObj.CreatedAt,
	})
}

// GetReseller 获取代理商详情（管理员）
func (h *ResellerHandler) GetReseller(c echo.Context) error {
	// 获取代理商ID
	resellerID := c.Param("id")
	if resellerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Reseller ID is required")
	}

	// 获取代理商
	resellerObj, err := h.resellerService.GetResellerByID(c.Request().Context(), resellerID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":             resellerObj.ID,
		"userId":         resellerObj.UserID,
		"companyName":    resellerObj.CompanyName,
		"contactPerson":  resellerObj.ContactPerson,
		"email":          resellerObj.Email,
		"phone":          resellerObj.Phone,
		"country":        resellerObj.Country,
		"address":        resellerObj.Address,
		"commissionRate": resellerObj.CommissionRate,
		"balance":        resellerObj.Balance,
		"currency":       resellerObj.Currency,
		"status":         resellerObj.Status,
		"callbackUrl":    resellerObj.CallbackURL,
		"createdAt":      resellerObj.CreatedAt,
		"updatedAt":      resellerObj.UpdatedAt,
	})
}

// UpdateReseller 更新代理商信息（管理员）
func (h *ResellerHandler) UpdateReseller(c echo.Context) error {
	// 获取代理商ID
	resellerID := c.Param("id")
	if resellerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Reseller ID is required")
	}

	// 解析请求
	var req struct {
		CompanyName    string `json:"companyName"`
		ContactPerson  string `json:"contactPerson"`
		Phone          string `json:"phone"`
		Address        string `json:"address"`
		CommissionRate int    `json:"commissionRate" validate:"min=0,max=10000"`
		CallbackURL    string `json:"callbackUrl"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 更新代理商
	resellerObj, err := h.resellerService.UpdateReseller(
		c.Request().Context(),
		resellerID,
		req.CompanyName,
		req.ContactPerson,
		req.Phone,
		req.Address,
		req.CommissionRate,
		req.CallbackURL,
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":             resellerObj.ID,
		"companyName":    resellerObj.CompanyName,
		"contactPerson":  resellerObj.ContactPerson,
		"phone":          resellerObj.Phone,
		"address":        resellerObj.Address,
		"commissionRate": resellerObj.CommissionRate,
		"callbackUrl":    resellerObj.CallbackURL,
		"updatedAt":      resellerObj.UpdatedAt,
	})
}

// UpdateResellerStatus 更新代理商状态（管理员）
func (h *ResellerHandler) UpdateResellerStatus(c echo.Context) error {
	// 获取代理商ID
	resellerID := c.Param("id")
	if resellerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Reseller ID is required")
	}

	// 解析请求
	var req struct {
		Status string `json:"status" validate:"required,oneof=ACTIVE INACTIVE PENDING"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 更新状态
	err := h.resellerService.UpdateResellerStatus(c.Request().Context(), resellerID, reseller.Status(req.Status))
	if err != nil {
		return err
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]string{
		"message": "Reseller status updated successfully",
	})
}

// RegenerateAPIKey 重新生成API密钥（管理员）
func (h *ResellerHandler) RegenerateAPIKey(c echo.Context) error {
	// 获取代理商ID
	resellerID := c.Param("id")
	if resellerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Reseller ID is required")
	}

	// 重新生成API密钥
	apiKey, apiSecret, err := h.resellerService.RegenerateAPIKey(c.Request().Context(), resellerID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"apiKey":    apiKey,
		"apiSecret": apiSecret,
		"message":   "API key regenerated successfully",
	})
}

// AddResellerBalance 添加代理商余额（管理员）
func (h *ResellerHandler) AddResellerBalance(c echo.Context) error {
	// 获取代理商ID
	resellerID := c.Param("id")
	if resellerID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Reseller ID is required")
	}

	// 解析请求
	var req struct {
		Amount int64 `json:"amount" validate:"required,min=1"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 添加余额
	err := h.resellerService.AddResellerBalance(c.Request().Context(), resellerID, req.Amount)
	if err != nil {
		return err
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]string{
		"message": "Balance added successfully",
	})
}

// ListResellers 获取代理商列表（管理员）
func (h *ResellerHandler) ListResellers(c echo.Context) error {
	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 构建过滤条件
	filter := make(map[string]interface{})
	if status := c.QueryParam("status"); status != "" {
		filter["status"] = status
	}

	if country := c.QueryParam("country"); country != "" {
		filter["country"] = country
	}

	// 获取代理商列表
	resellers, total, err := h.resellerService.ListResellers(c.Request().Context(), filter, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	resellerList := make([]map[string]interface{}, len(resellers))
	for i, resellerObj := range resellers {
		resellerList[i] = map[string]interface{}{
			"id":             resellerObj.ID,
			"companyName":    resellerObj.CompanyName,
			"contactPerson":  resellerObj.ContactPerson,
			"email":          resellerObj.Email,
			"phone":          resellerObj.Phone,
			"country":        resellerObj.Country,
			"status":         resellerObj.Status,
			"commissionRate": resellerObj.CommissionRate,
			"balance":        resellerObj.Balance,
			"currency":       resellerObj.Currency,
			"createdAt":      resellerObj.CreatedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"resellers": resellerList,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// GetResellerProfile 获取代理商自身资料
func (h *ResellerHandler) GetResellerProfile(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取代理商
	resellerObj, err := h.resellerService.GetResellerByID(c.Request().Context(), resellerID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":             resellerObj.ID,
		"companyName":    resellerObj.CompanyName,
		"contactPerson":  resellerObj.ContactPerson,
		"email":          resellerObj.Email,
		"phone":          resellerObj.Phone,
		"country":        resellerObj.Country,
		"address":        resellerObj.Address,
		"commissionRate": resellerObj.CommissionRate,
		"status":         resellerObj.Status,
		"type":           resellerObj.Type,
		"createdAt":      resellerObj.CreatedAt,
		"updatedAt":      resellerObj.UpdatedAt,
	})
}

// UpdateResellerProfile 更新代理商自身资料
func (h *ResellerHandler) UpdateResellerProfile(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 解析请求
	var req struct {
		CompanyName   string `json:"companyName"`
		ContactPerson string `json:"contactPerson"`
		Phone         string `json:"phone"`
		Address       string `json:"address"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 更新代理商
	resellerObj, err := h.resellerService.UpdateReseller(
		c.Request().Context(),
		resellerID,
		req.CompanyName,
		req.ContactPerson,
		req.Phone,
		req.Address,
		0,  // 不允许代理商修改自己的佣金率
		"", // 不允许代理商修改自己的回调URL
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":            resellerObj.ID,
		"companyName":   resellerObj.CompanyName,
		"contactPerson": resellerObj.ContactPerson,
		"phone":         resellerObj.Phone,
		"address":       resellerObj.Address,
		"updatedAt":     resellerObj.UpdatedAt,
	})
}

// GetResellerBalance 查询代理商余额
func (h *ResellerHandler) GetResellerBalance(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取代理商
	resellerObj, err := h.resellerService.GetResellerByID(c.Request().Context(), resellerID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"balance":     resellerObj.Balance,
		"currency":    resellerObj.Currency,
		"creditLimit": 0, // 假设目前没有信用额度功能
		"lastUpdated": resellerObj.UpdatedAt,
	})
}

// ListResellerUsers 查询下属用户列表
func (h *ResellerHandler) ListResellerUsers(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 构建过滤条件
	filter := map[string]interface{}{
		"resellerId": resellerID,
	}

	if status := c.QueryParam("status"); status != "" {
		filter["status"] = status
	}

	// 获取用户列表
	users, total, err := h.resellerService.ListUsers(c.Request().Context(), filter, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	userList := make([]map[string]interface{}, len(users))
	for i, user := range users {
		userList[i] = map[string]interface{}{
			"id":        user.ID,
			"email":     user.Email,
			"name":      user.Name,
			"role":      user.Role,
			"status":    user.Status,
			"createdAt": user.CreatedAt,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"users": userList,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// CreateResellerUser 创建下属用户
func (h *ResellerHandler) CreateResellerUser(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 解析请求
	var req struct {
		Email    string `json:"email" validate:"required,email"`
		Password string `json:"password" validate:"required,min=8"`
		Name     string `json:"name" validate:"required"`
		Mobile   string `json:"mobile"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建用户
	user, err := h.resellerService.CreateUser(
		c.Request().Context(),
		resellerID,
		req.Email,
		req.Password,
		req.Name,
		req.Mobile,
	)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusCreated, map[string]interface{}{
		"id":        user.ID,
		"email":     user.Email,
		"name":      user.Name,
		"mobile":    user.Mobile,
		"role":      user.Role,
		"status":    user.Status,
		"createdAt": user.CreatedAt,
	})
}

// GetResellerUser 查询下属用户详情
func (h *ResellerHandler) GetResellerUser(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取用户ID
	userID := c.Param("id")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "User ID is required")
	}

	// 获取用户
	user, err := h.resellerService.GetUserByID(c.Request().Context(), resellerID, userID)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":          user.ID,
		"email":       user.Email,
		"name":        user.Name,
		"mobile":      user.Mobile,
		"role":        user.Role,
		"status":      user.Status,
		"createdAt":   user.CreatedAt,
		"lastLoginAt": user.LastLoginAt,
	})
}

// GetResellerUserESIMs 查询下属用户eSIM列表
func (h *ResellerHandler) GetResellerUserESIMs(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取用户ID
	userID := c.Param("id")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "User ID is required")
	}

	// 验证用户归属于该代理商
	if err := h.resellerService.VerifyUserBelongsToReseller(c.Request().Context(), resellerID, userID); err != nil {
		return err
	}

	// 获取用户的eSIM列表
	esims, err := h.resellerService.GetUserESIMs(c.Request().Context(), userID)
	if err != nil {
		return err
	}

	// 转换为响应格式
	esimList := make([]map[string]interface{}, len(esims))
	for i, esim := range esims {
		esimList[i] = map[string]interface{}{
			"iccid":          esim.Identifier.ICCID,
			"esimTranNo":     esim.Identifier.ESIMTranNo,
			"status":         esim.Status.Code,
			"activationCode": esim.ActivationCode,
			"qrCodeURL":      esim.QRCodeURL,
			"dataVolume":     esim.DataVolume,
			"usedData":       esim.UsedData,
			"expiryTime":     esim.ExpiryTime,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"esims": esimList,
	})
}

// GetResellerUserOrders 查询下属用户订单列表
func (h *ResellerHandler) GetResellerUserOrders(c echo.Context) error {
	// 从上下文获取代理商ID
	resellerID := c.Get("reseller_id").(string)

	// 获取用户ID
	userID := c.Param("id")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "User ID is required")
	}

	// 验证用户归属于该代理商
	if err := h.resellerService.VerifyUserBelongsToReseller(c.Request().Context(), resellerID, userID); err != nil {
		return err
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 获取用户的订单列表
	orders, total, err := h.resellerService.GetUserOrders(c.Request().Context(), userID, page, pageSize)
	if err != nil {
		return err
	}

	// 转换为响应格式
	orderList := make([]map[string]interface{}, len(orders))
	for i, order := range orders {
		orderList[i] = map[string]interface{}{
			"id":           order.ID,
			"orderNumber":  order.TransactionID,
			"amount":       order.TotalAmount,
			"status":       order.Status,
			"productType":  order.ProviderType,
			"productInfo":  extractProductInfo(order.Items),
			"createdAt":    order.CreatedAt,
			"completedAt":  order.CompletedAt,
			"providerType": order.ProviderType,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"orders": orderList,
		"pagination": map[string]interface{}{
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		},
	})
}

// 提取产品信息
func extractProductInfo(items []order.Item) string {
	if len(items) == 0 {
		return ""
	}
	return items[0].PackageName
}
