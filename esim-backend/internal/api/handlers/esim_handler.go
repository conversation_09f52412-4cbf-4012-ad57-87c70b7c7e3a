package handlers

import (
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/service"
	pkgesim "vereal/letsesim/pkg/esim"
)

// ESIMHandler eSIM处理器
type ESIMHandler struct {
	esimService  *service.ESIMService
	orderService *service.OrderService
}

// NewESIMHandler 创建新的eSIM处理器
func NewESIMHandler(esimService *service.ESIMService, orderService *service.OrderService) *ESIMHandler {
	return &ESIMHandler{
		esimService:  esimService,
		orderService: orderService,
	}
}

// ListProviders 获取提供商列表
func (h *ESIMHandler) ListProviders(c echo.Context) error {
	providers := h.esimService.ListProviders(c.Request().Context())

	// 转换为响应格式
	result := make([]map[string]interface{}, len(providers))
	for i, provider := range providers {
		result[i] = map[string]interface{}{
			"type":         provider.Type,
			"name":         provider.Name,
			"capabilities": provider.Capabilities,
		}
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"providers": result,
	})
}

// ListPackages 获取套餐列表
func (h *ESIMHandler) ListPackages(c echo.Context) error {
	// 获取提供商类型
	provider := c.Param("provider")
	if provider == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Provider is required")
	}

	// 解析查询参数
	params := pkgesim.PackageQueryParams{
		LocationCode: c.QueryParam("locationCode"),
		Type:         c.QueryParam("type"),
		PackageCode:  c.QueryParam("packageCode"),
		Slug:         c.QueryParam("slug"),
		ICCID:        c.QueryParam("iccid"),
		Region:       c.QueryParam("region"),
		Country:      c.QueryParam("country"),
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	params.PageNum = page
	params.PageSize = pageSize

	// 获取套餐列表
	packages, pagination, err := h.esimService.ListPackages(c.Request().Context(), provider, params)
	if err != nil {
		return err
	}

	// 转换为响应格式
	packageList := make([]map[string]interface{}, len(packages))
	for i, pkg := range packages {
		packageList[i] = map[string]interface{}{
			"id":            pkg.ID,
			"name":          pkg.Name,
			"description":   pkg.Description,
			"price":         pkg.Price,
			"currency":      pkg.Currency,
			"dataVolume":    pkg.DataVolume,
			"validityDays":  pkg.ValidityDays,
			"locationCodes": pkg.LocationCodes,
			"supportsSMS":   pkg.SupportsSMS,
			"dataType":      pkg.DataType,
			"networkTypes":  pkg.NetworkTypes,
			"supportTopUp":  pkg.SupportTopUp,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"packages": packageList,
		"pagination": map[string]interface{}{
			"total":    pagination.Total,
			"page":     pagination.PageNum,
			"pageSize": pagination.PageSize,
		},
	})
}

// GetPackageDetails 获取套餐详情
func (h *ESIMHandler) GetPackageDetails(c echo.Context) error {
	// 获取提供商类型和套餐ID
	provider := c.Param("provider")
	packageID := c.Param("id")

	if provider == "" || packageID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Provider and package ID are required")
	}

	// 获取套餐详情
	pkg, err := h.esimService.GetPackageDetails(c.Request().Context(), provider, packageID)
	if err != nil {
		return err
	}

	// 转换为响应格式
	result := map[string]interface{}{
		"id":            pkg.ID,
		"name":          pkg.Name,
		"description":   pkg.Description,
		"price":         pkg.Price,
		"currency":      pkg.Currency,
		"dataVolume":    pkg.DataVolume,
		"validityDays":  pkg.ValidityDays,
		"locationCodes": pkg.LocationCodes,
		"supportsSMS":   pkg.SupportsSMS,
		"dataType":      pkg.DataType,
		"networkTypes":  pkg.NetworkTypes,
		"supportTopUp":  pkg.SupportTopUp,
	}

	return c.JSON(http.StatusOK, result)
}

// ListSupportedRegions 获取支持的区域列表
func (h *ESIMHandler) ListSupportedRegions(c echo.Context) error {
	// 获取提供商类型
	provider := c.Param("provider")
	if provider == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Provider is required")
	}

	// 获取区域列表
	regions, err := h.esimService.ListSupportedRegions(c.Request().Context(), provider)
	if err != nil {
		return err
	}

	// 转换为响应格式
	regionList := make([]map[string]interface{}, len(regions))
	for i, region := range regions {
		subLocations := make([]map[string]interface{}, len(region.SubLocations))
		for j, subLocation := range region.SubLocations {
			subLocations[j] = map[string]interface{}{
				"code": subLocation.Code,
				"name": subLocation.Name,
			}
		}

		regionList[i] = map[string]interface{}{
			"code":         region.Code,
			"name":         region.Name,
			"type":         region.Type,
			"subLocations": subLocations,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"regions": regionList,
	})
}

// GetESIMDetails 获取eSIM详情
func (h *ESIMHandler) GetESIMDetails(c echo.Context) error {
	// 获取提供商类型和标识
	providerType := c.Param("providerType")

	if providerType == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Provider type is required")
	}

	// 构建eSIM标识
	esimID := pkgesim.ESIMIdentifier{
		ICCID:      c.QueryParam("iccid"),
		ESIMTranNo: c.QueryParam("esimTranNo"),
		OrderNo:    c.QueryParam("orderNo"),
	}

	if esimID.ICCID == "" && esimID.ESIMTranNo == "" && esimID.OrderNo == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "ICCID, esimTranNo or orderNo is required")
	}

	// 获取eSIM详情
	esimInfo, err := h.esimService.GetESIMDetails(c.Request().Context(), providerType, esimID)
	if err != nil {
		return err
	}

	// 转换为响应格式
	packages := make([]map[string]interface{}, len(esimInfo.Packages))
	for i, pkg := range esimInfo.Packages {
		packages[i] = map[string]interface{}{
			"id":           pkg.ID,
			"name":         pkg.Name,
			"dataVolume":   pkg.DataVolume,
			"validityDays": pkg.ValidityDays,
		}
	}

	result := map[string]interface{}{
		"iccid":          esimInfo.Identifier.ICCID,
		"esimTranNo":     esimInfo.Identifier.ESIMTranNo,
		"orderNo":        esimInfo.Identifier.OrderNo,
		"status":         esimInfo.Status.Code,
		"smdpStatus":     esimInfo.Status.SMDPStatus,
		"statusDesc":     esimInfo.Status.Description,
		"activationCode": esimInfo.ActivationCode,
		"qrCodeURL":      esimInfo.QRCodeURL,
		"dataVolume":     esimInfo.DataVolume,
		"usedData":       esimInfo.UsedData,
		"validityDays":   esimInfo.ValidityDays,
		"expiryTime":     esimInfo.ExpiryTime,
		"activeType":     esimInfo.ActiveType,
		"eid":            esimInfo.EID,
		"packages":       packages,
		"sms": map[string]interface{}{
			"supported": esimInfo.SMS.Supported,
			"msisdn":    esimInfo.SMS.MSISDN,
			"apiOnly":   esimInfo.SMS.APIOnly,
		},
	}

	return c.JSON(http.StatusOK, result)
}

// ListESIMs 获取eSIM列表
func (h *ESIMHandler) ListESIMs(c echo.Context) error {
	// 获取提供商类型
	providerType := c.Param("providerType")
	if providerType == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Provider type is required")
	}

	// 解析查询参数
	params := pkgesim.ESIMQueryParams{
		OrderNo:    c.QueryParam("orderNo"),
		ICCID:      c.QueryParam("iccid"),
		ESIMTranNo: c.QueryParam("esimTranNo"),
		CustomerID: c.QueryParam("customerId"),
	}

	// 解析时间范围
	if startTimeStr := c.QueryParam("startTime"); startTimeStr != "" {
		startTime, err := time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid startTime format")
		}
		params.StartTime = startTime
	}

	if endTimeStr := c.QueryParam("endTime"); endTimeStr != "" {
		endTime, err := time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid endTime format")
		}
		params.EndTime = endTime
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	params.PageNum = page
	params.PageSize = pageSize

	// 获取eSIM列表
	result, err := h.esimService.ListESIMs(c.Request().Context(), providerType, params)
	if err != nil {
		return err
	}

	// 转换为响应格式
	esimList := make([]map[string]interface{}, len(result.ESIMs))
	for i, esimInfo := range result.ESIMs {
		esimList[i] = map[string]interface{}{
			"iccid":          esimInfo.Identifier.ICCID,
			"esimTranNo":     esimInfo.Identifier.ESIMTranNo,
			"orderNo":        esimInfo.Identifier.OrderNo,
			"status":         esimInfo.Status.Code,
			"activationCode": esimInfo.ActivationCode,
			"qrCodeURL":      esimInfo.QRCodeURL,
			"dataVolume":     esimInfo.DataVolume,
			"usedData":       esimInfo.UsedData,
			"validityDays":   esimInfo.ValidityDays,
			"expiryTime":     esimInfo.ExpiryTime,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"esims": esimList,
		"pagination": map[string]interface{}{
			"total":    result.Pagination.Total,
			"page":     result.Pagination.PageNum,
			"pageSize": result.Pagination.PageSize,
		},
	})
}

// UpdateESIMStatus 更新eSIM状态 (原ManageESIM)
func (h *ESIMHandler) UpdateESIMStatus(c echo.Context) error {
	// 获取eSIM ID
	esimID := c.Param("id")
	if esimID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "eSIM ID is required")
	}

	// 解析请求
	var req struct {
		Status string `json:"status" validate:"required,oneof=SUSPENDED ACTIVE CANCELLED"`
		Reason string `json:"reason"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid status value. Supported values: SUSPENDED, ACTIVE, CANCELLED")
	}

	// 从状态值映射到操作
	var action string
	switch req.Status {
	case "SUSPENDED":
		action = "suspend"
	case "ACTIVE":
		action = "resume"
	case "CANCELLED":
		action = "cancel"
	default:
		return echo.NewHTTPError(http.StatusBadRequest, "Unsupported status value")
	}

	// 从数据库获取eSIM详情
	esimDetails, err := h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
		ESIMTranNo: esimID, // 使用ID作为交易号查询
	})
	if err != nil {
		// 尝试使用ICCID查询
		esimDetails, err = h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
			ICCID: esimID, // 使用ID作为ICCID查询
		})
		if err != nil {
			return echo.NewHTTPError(http.StatusNotFound, "eSIM not found")
		}
	}

	// 检查当前状态是否允许进行状态转换
	if esimDetails.Status.Code == "EXPIRED" && req.Status != "ACTIVE" {
		return echo.NewHTTPError(http.StatusConflict, map[string]interface{}{
			"code":          409,
			"message":       "无法" + getActionName(action) + "已过期的eSIM",
			"currentStatus": esimDetails.Status.Code,
		})
	}

	// 构建eSIM标识
	identifier := pkgesim.ESIMIdentifier{
		ICCID:      esimDetails.Identifier.ICCID,
		ESIMTranNo: esimDetails.Identifier.ESIMTranNo,
		OrderNo:    esimDetails.Identifier.OrderNo,
	}

	// 根据ICCID或ESIMTranNo从esimRepo确定提供商类型
	// 这里我们直接使用第一次查询时使用的提供商类型
	providerType := ""
	if len(esimDetails.Packages) > 0 {
		// 从套餐推断提供商类型
		for _, pkg := range esimDetails.Packages {
			if prov, ok := pkg.ProviderSpecific["provider"]; ok {
				if provStr, ok := prov.(string); ok {
					providerType = provStr
					break
				}
			}
		}
	}

	// 如果无法确定提供商类型，尝试从ProviderSpecific字段获取
	if providerType == "" && esimDetails.ProviderSpecific != nil {
		if prov, ok := esimDetails.ProviderSpecific["provider"]; ok {
			if provStr, ok := prov.(string); ok {
				providerType = provStr
			}
		}
	}

	// 如果仍然无法确定，使用默认提供商
	if providerType == "" {
		providerType = "esim_access" // 使用默认提供商
	}

	// 执行操作
	err = h.esimService.ManageESIM(c.Request().Context(), providerType, identifier, action)
	if err != nil {
		return err
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":        esimID,
		"status":    req.Status,
		"message":   "eSIM已成功" + getActionName(action),
		"updatedAt": time.Now().Format(time.RFC3339),
	})
}

// getActionName 获取操作的中文名称
func getActionName(action string) string {
	switch action {
	case "suspend":
		return "暂停"
	case "resume":
		return "激活"
	case "cancel":
		return "取消"
	default:
		return "更新"
	}
}

// AddESIMData 为eSIM添加流量 (原TopUpESIM)
func (h *ESIMHandler) AddESIMData(c echo.Context) error {
	// 获取eSIM ID
	esimID := c.Param("id")
	if esimID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "eSIM ID is required")
	}

	// 解析请求
	var req struct {
		PackageCode   string `json:"packageCode" validate:"required"`
		PaymentMethod string `json:"paymentMethod"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 从数据库获取eSIM详情
	esimDetails, err := h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
		ESIMTranNo: esimID, // 使用ID作为交易号查询
	})
	if err != nil {
		// 尝试使用ICCID查询
		esimDetails, err = h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
			ICCID: esimID, // 使用ID作为ICCID查询
		})
		if err != nil {
			return echo.NewHTTPError(http.StatusNotFound, "eSIM not found")
		}
	}

	// 确定提供商类型
	providerType := ""
	if len(esimDetails.Packages) > 0 {
		// 从套餐推断提供商类型
		for _, pkg := range esimDetails.Packages {
			if prov, ok := pkg.ProviderSpecific["provider"]; ok {
				if provStr, ok := prov.(string); ok {
					providerType = provStr
					break
				}
			}
		}
	}

	// 如果无法确定提供商类型，尝试从ProviderSpecific字段获取
	if providerType == "" && esimDetails.ProviderSpecific != nil {
		if prov, ok := esimDetails.ProviderSpecific["provider"]; ok {
			if provStr, ok := prov.(string); ok {
				providerType = provStr
			}
		}
	}

	// 如果仍然无法确定，使用默认提供商
	if providerType == "" {
		providerType = "esim_access" // 使用默认提供商
	}

	// 生成交易ID
	transactionID := "txn_" + strconv.FormatInt(time.Now().Unix(), 10)

	// 构建充值参数
	params := pkgesim.TopUpParams{
		ESIMID: pkgesim.ESIMIdentifier{
			ICCID:      esimDetails.Identifier.ICCID,
			ESIMTranNo: esimDetails.Identifier.ESIMTranNo,
		},
		PackageID:     req.PackageCode,
		TransactionID: transactionID,
	}

	// 执行充值
	result, err := h.esimService.TopUpESIM(c.Request().Context(), providerType, params)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":            esimID,
		"transactionId": result.TransactionID,
		"expiredTime":   result.ExpiryTime,
		"totalVolume":   result.TotalVolume,
		"totalDuration": calculateDuration(esimDetails.ExpiryTime, result.ExpiryTime),
		"message":       "eSIM充值成功",
		"updatedAt":     time.Now().Format(time.RFC3339),
	})
}

// calculateDuration 计算两个时间之间的天数
func calculateDuration(startTime, endTime time.Time) int {
	duration := endTime.Sub(startTime)
	return int(duration.Hours() / 24)
}

// SendESIMMessage 发送短信 (原SendSMS)
func (h *ESIMHandler) SendESIMMessage(c echo.Context) error {
	// 获取eSIM ID
	esimID := c.Param("id")
	if esimID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "eSIM ID is required")
	}

	// 解析请求
	var req struct {
		PhoneNumber string `json:"phoneNumber" validate:"required,e164"`
		Message     string `json:"message" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid phone number format. Must be in E.164 format (e.g., +12345678901)")
	}

	// 从数据库获取eSIM详情
	esimDetails, err := h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
		ESIMTranNo: esimID, // 使用ID作为交易号查询
	})
	if err != nil {
		// 尝试使用ICCID查询
		esimDetails, err = h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
			ICCID: esimID, // 使用ID作为ICCID查询
		})
		if err != nil {
			return echo.NewHTTPError(http.StatusNotFound, "eSIM not found")
		}
	}

	// 检查eSIM是否支持短信
	if !esimDetails.SMS.Supported {
		return echo.NewHTTPError(http.StatusBadRequest, "eSIM does not support SMS")
	}

	// 确定提供商类型
	providerType := ""
	if len(esimDetails.Packages) > 0 {
		// 从套餐推断提供商类型
		for _, pkg := range esimDetails.Packages {
			if prov, ok := pkg.ProviderSpecific["provider"]; ok {
				if provStr, ok := prov.(string); ok {
					providerType = provStr
					break
				}
			}
		}
	}

	// 如果无法确定提供商类型，尝试从ProviderSpecific字段获取
	if providerType == "" && esimDetails.ProviderSpecific != nil {
		if prov, ok := esimDetails.ProviderSpecific["provider"]; ok {
			if provStr, ok := prov.(string); ok {
				providerType = provStr
			}
		}
	}

	// 如果仍然无法确定，使用默认提供商
	if providerType == "" {
		providerType = "esim_access" // 使用默认提供商
	}

	// 构建SMS参数
	params := pkgesim.SMSParams{
		ESIMID: pkgesim.ESIMIdentifier{
			ICCID:      esimDetails.Identifier.ICCID,
			ESIMTranNo: esimDetails.Identifier.ESIMTranNo,
		},
		Message: req.Message,
		// 注意：修改SMSParams结构需要对接服务层和提供商层
		// 目前我们在此处使用现有字段，应该在服务层中处理电话号码
	}

	// 发送短信
	err = h.esimService.SendSMS(c.Request().Context(), providerType, params)
	if err != nil {
		return err
	}

	// 生成短信ID
	smsID := "sms_" + strconv.FormatInt(time.Now().Unix(), 10)

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":         esimID,
		"smsId":      smsID,
		"statusCode": "200",
		"status":     "SENT",
		"message":    "短信发送成功",
		"sentAt":     time.Now().Format(time.RFC3339),
	})
}

// GetESIMUsage 获取eSIM使用情况
func (h *ESIMHandler) GetESIMUsage(c echo.Context) error {
	// 获取eSIM ID
	esimID := c.Param("id")
	if esimID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "eSIM ID is required")
	}

	// 获取使用情况周期
	period := c.QueryParam("period")
	if period == "" {
		period = "daily" // 默认为每日数据
	}
	if period != "daily" && period != "weekly" && period != "monthly" {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid period value. Supported values: daily, weekly, monthly")
	}

	// 从数据库获取eSIM详情
	esimDetails, err := h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
		ESIMTranNo: esimID, // 使用ID作为交易号查询
	})
	if err != nil {
		// 尝试使用ICCID查询
		esimDetails, err = h.esimService.GetESIMDetails(c.Request().Context(), "", pkgesim.ESIMIdentifier{
			ICCID: esimID, // 使用ID作为ICCID查询
		})
		if err != nil {
			return echo.NewHTTPError(http.StatusNotFound, "eSIM not found")
		}
	}

	// 确定提供商类型
	providerType := ""
	if len(esimDetails.Packages) > 0 {
		// 从套餐推断提供商类型
		for _, pkg := range esimDetails.Packages {
			if prov, ok := pkg.ProviderSpecific["provider"]; ok {
				if provStr, ok := prov.(string); ok {
					providerType = provStr
					break
				}
			}
		}
	}

	// 如果无法确定提供商类型，尝试从ProviderSpecific字段获取
	if providerType == "" && esimDetails.ProviderSpecific != nil {
		if prov, ok := esimDetails.ProviderSpecific["provider"]; ok {
			if provStr, ok := prov.(string); ok {
				providerType = provStr
			}
		}
	}

	// 如果仍然无法确定，使用默认提供商
	if providerType == "" {
		providerType = "esim_access" // 使用默认提供商
	}

	// 获取使用情况
	usage, err := h.esimService.GetESIMUsage(c.Request().Context(), providerType, pkgesim.ESIMIdentifier{
		ICCID:      esimDetails.Identifier.ICCID,
		ESIMTranNo: esimDetails.Identifier.ESIMTranNo,
	})
	if err != nil {
		return err
	}

	// 计算剩余数据和使用百分比
	remainingData := esimDetails.DataVolume - usage.DataUsage
	if remainingData < 0 {
		remainingData = 0
	}

	usagePercentage := float64(0)
	if esimDetails.DataVolume > 0 {
		usagePercentage = float64(usage.DataUsage) / float64(esimDetails.DataVolume) * 100
	}

	// 计算剩余天数
	remainingDays := 0
	now := time.Now()
	if esimDetails.ExpiryTime.After(now) {
		remainingDays = int(esimDetails.ExpiryTime.Sub(now).Hours() / 24)
	}

	// 构造使用详情列表（这里需要根据period创建模拟数据，实际项目中应从数据库或提供商API获取）
	details := []map[string]interface{}{}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"id":              esimID,
		"dataVolume":      esimDetails.DataVolume,
		"usedData":        usage.DataUsage,
		"remainingData":   remainingData,
		"usagePercentage": usagePercentage,
		"validityDays":    esimDetails.ValidityDays,
		"startDate":       esimDetails.ExpiryTime.AddDate(0, 0, -esimDetails.ValidityDays),
		"expiryDate":      esimDetails.ExpiryTime,
		"remainingDays":   remainingDays,
		"details":         details,
		"updatedAt":       usage.LastUpdateTime,
	})
}

// GetAccountBalance 获取账户余额
func (h *ESIMHandler) GetAccountBalance(c echo.Context) error {
	// 获取提供商类型
	providerType := c.Param("providerType")
	if providerType == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Provider type is required")
	}

	// 获取账户余额
	balance, err := h.esimService.GetAccountBalance(c.Request().Context(), providerType)
	if err != nil {
		return err
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"balance":        balance.Balance,
		"currency":       balance.Currency,
		"hasOverdraft":   balance.HasOverdraft,
		"overdraftLimit": balance.OverdraftLimit,
	})
}

// HandleWebhook 处理Webhook
func (h *ESIMHandler) HandleWebhook(c echo.Context) error {
	// 获取提供商类型
	provider := c.Param("provider")
	if provider == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "需要提供商标识符",
		})
	}

	// 读取请求体
	body, err := io.ReadAll(c.Request().Body)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]interface{}{
			"code":    400,
			"message": "无法读取请求内容",
		})
	}

	// 获取请求头
	headers := make(map[string][]string)
	for key, values := range c.Request().Header {
		headers[key] = values
	}

	// 处理Webhook
	err = h.esimService.HandleWebhook(c.Request().Context(), provider, body, headers)
	if err != nil {
		return err
	}

	// 返回成功响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
	})
}

// ListUserESIMs 获取用户的eSIM列表
func (h *ESIMHandler) ListUserESIMs(c echo.Context) error {
	// 从上下文获取用户
	currentUser := c.Get("user").(*user.User)
	userId := currentUser.ID

	// 解析分页参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("pageSize"))
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询参数
	params := pkgesim.ESIMQueryParams{
		CustomerID: userId,
		PageNum:    page,
		PageSize:   pageSize,
	}

	// 调用服务获取用户eSIM列表
	// 注意：这里我们使用默认提供商，实际情况可能需要支持多提供商查询
	result, err := h.esimService.ListESIMs(c.Request().Context(), "", params)
	if err != nil {
		return err
	}

	// 转换为响应格式
	esimList := make([]map[string]interface{}, len(result.ESIMs))
	for i, e := range result.ESIMs {
		esimList[i] = map[string]interface{}{
			"iccid":          e.Identifier.ICCID,
			"tranNo":         e.Identifier.ESIMTranNo,
			"status":         e.Status.Code,
			"activationCode": e.ActivationCode,
			"qrCodeURL":      e.QRCodeURL,
			"dataVolume":     e.DataVolume,
			"usedData":       e.UsedData,
			"validityDays":   e.ValidityDays,
			"expiryTime":     e.ExpiryTime,
		}
	}

	// 返回响应
	return c.JSON(http.StatusOK, map[string]interface{}{
		"esims": esimList,
		"pagination": map[string]interface{}{
			"total":    result.Pagination.Total,
			"page":     result.Pagination.PageNum,
			"pageSize": result.Pagination.PageSize,
		},
	})
}
