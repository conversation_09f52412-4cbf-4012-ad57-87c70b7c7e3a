package middleware

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"

	"vereal/letsesim/pkg/esim"
)

// ErrorMiddleware 错误处理中间件
type ErrorMiddleware struct {
	logger *zap.Logger
}

// NewErrorMiddleware 创建新的错误处理中间件
func NewErrorMiddleware(logger *zap.Logger) *ErrorMiddleware {
	return &ErrorMiddleware{
		logger: logger,
	}
}

// ErrorHandler 全局错误处理器
func (m *ErrorMiddleware) ErrorHandler() echo.HTTPErrorHandler {
	return func(err error, c echo.Context) {
		// 已经发送了响应
		if c.Response().Committed {
			return
		}

		// 请求上下文
		req := c.Request()

		// 处理不同类型的错误
		var (
			code    = http.StatusInternalServerError
			message = "Internal Server Error"
		)

		// 处理Echo框架错误
		if echoErr, ok := err.(*echo.HTTPError); ok {
			code = echoErr.Code
			message = echoErr.Message.(string)
		} else if esimErr, ok := err.(*esim.ESIMError); ok {
			// 处理eSIM自定义错误
			code = esimErr.HTTPStatus
			message = esimErr.Message
		}

		// 记录错误
		m.logger.Error("Request error",
			zap.String("method", req.Method),
			zap.String("path", req.URL.Path),
			zap.Int("status", code),
			zap.String("error", message),
			zap.Error(err),
		)

		// 返回错误响应
		if !c.Response().Committed {
			if c.Request().Method == http.MethodHead {
				c.NoContent(code)
			} else {
				c.JSON(code, map[string]interface{}{
					"code":    code,
					"message": message,
				})
			}
		}
	}
}
