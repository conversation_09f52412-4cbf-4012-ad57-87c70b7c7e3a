package middleware

import (
	"time"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// LoggerMiddleware 日志中间件
type LoggerMiddleware struct {
	logger *zap.Logger
}

// NewLoggerMiddleware 创建新的日志中间件
func NewLoggerMiddleware(logger *zap.Logger) *LoggerMiddleware {
	return &LoggerMiddleware{
		logger: logger,
	}
}

// Logger 日志中间件
func (m *LoggerMiddleware) Logger() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()

			// 处理请求
			err := next(c)

			// 记录请求信息
			req := c.Request()
			res := c.Response()

			fields := []zap.Field{
				zap.String("method", req.Method),
				zap.String("path", req.URL.Path),
				zap.Int("status", res.Status),
				zap.Int64("latency_ms", time.Since(start).Milliseconds()),
				zap.String("ip", c.RealIP()),
				zap.String("user_agent", req.UserAgent()),
			}

			// 记录错误
			if err != nil {
				fields = append(fields, zap.Error(err))
				m.logger.Error("Request error", fields...)
			} else {
				m.logger.Info("Request handled", fields...)
			}

			return err
		}
	}
}
