package middleware

import (
	"time"

	"github.com/labstack/echo/v4"

	"vereal/letsesim/pkg/monitoring"
)

// Monitoring 监控中间件
func Monitoring() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 添加请求拦截和指标收集
			return monitoring.PrometheusMiddleware(next)(c)
		}
	}
}

// SetupMonitoring 设置监控系统
func SetupMonitoring(e *echo.Echo) {
	// 设置指标端点
	monitoring.SetupMetricsEndpoint(e)

	// 设置健康检查端点
	monitoring.SetupHealthEndpoint(e)

	// 全局添加监控中间件
	e.Use(Monitoring())
}

// TrackOperation 跟踪操作执行时间并记录到监控系统
func TrackOperation(name string) func() {
	start := time.Now()
	return func() {
		duration := time.Since(start).Seconds()
		// 记录操作持续时间
		monitoring.RequestDuration.WithLabelValues("operation", name, "200").Observe(duration)
	}
}

// TrackDatabaseQuery 跟踪数据库查询并记录到监控系统
func TrackDatabaseQuery(operation, table string) func() {
	return monitoring.MeasureDBQuery(operation, table)
}

// RecordMetric 记录业务指标
func RecordMetric(metric string, value float64) {
	monitoring.RecordBusinessMetric(metric, value)
}
