package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
	goredis "github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/pkg/redis"
)

// ErrKeyNotFound 定义Redis键不存在错误
var ErrKeyNotFound = goredis.Nil

// RateLimiterConfig 速率限制配置
type RateLimiterConfig struct {
	// 默认限制（每分钟请求数）
	DefaultLimit int
	// 每个角色的限制（每分钟请求数）
	RoleLimits map[user.Role]int
	// 每个路径的限制（每分钟请求数）
	PathLimits map[string]int
	// 缓存过期时间
	Expiry time.Duration
}

// DefaultRateLimiterConfig 默认速率限制配置
func DefaultRateLimiterConfig() *RateLimiterConfig {
	return &RateLimiterConfig{
		DefaultLimit: 60, // 默认每分钟60个请求
		RoleLimits: map[user.Role]int{
			user.RoleAdmin:      300, // 管理员每分钟300个请求
			user.RoleReseller:   200, // 代理商每分钟200个请求
			user.RoleEnterprise: 200, // 企业代理商每分钟200个请求
			user.RoleUser:       100, // 普通用户每分钟100个请求
		},
		PathLimits: map[string]int{
			"/api/v1/register":  10,  // 注册接口每分钟10个请求
			"/api/v1/login":     20,  // 登录接口每分钟20个请求
			"/api/v1/webhook/*": 500, // Webhook接口每分钟500个请求
		},
		Expiry: time.Minute, // 限制窗口为1分钟
	}
}

// RateLimiterMiddleware 速率限制中间件
type RateLimiterMiddleware struct {
	redisClient *redis.Client
	config      *RateLimiterConfig
	logger      *zap.Logger
}

// NewRateLimiterMiddleware 创建新的速率限制中间件
func NewRateLimiterMiddleware(
	redisClient *redis.Client,
	config *RateLimiterConfig,
	logger *zap.Logger,
) *RateLimiterMiddleware {
	if config == nil {
		config = DefaultRateLimiterConfig()
	}

	return &RateLimiterMiddleware{
		redisClient: redisClient,
		config:      config,
		logger:      logger,
	}
}

// RateLimit 速率限制中间件
func (m *RateLimiterMiddleware) RateLimit() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 获取客户端IP和请求路径
			clientIP := c.RealIP()
			path := c.Request().URL.Path

			// 获取用户角色（如果已认证）
			var role user.Role
			userObj, ok := c.Get("user").(*user.User)
			if ok && userObj != nil {
				role = userObj.Role
			}

			// 确定限制值
			limit := m.determineLimit(path, role)

			// 生成Redis键
			var key string
			if userObj != nil {
				// 已认证用户，按用户ID限制
				key = fmt.Sprintf("rate_limit:user:%s:%s", userObj.ID, path)
			} else {
				// 未认证用户，按IP限制
				key = fmt.Sprintf("rate_limit:ip:%s:%s", clientIP, path)
			}

			// 检查是否超过限制
			exceeded, count, err := m.checkRateLimit(c.Request().Context(), key, limit)
			if err != nil {
				m.logger.Error("Rate limit check failed",
					zap.Error(err),
					zap.String("ip", clientIP),
					zap.String("path", path))
				// 发生错误时继续处理请求，不阻止用户
				return next(c)
			}

			// 设置速率限制响应头
			c.Response().Header().Set("X-RateLimit-Limit", strconv.Itoa(limit))
			c.Response().Header().Set("X-RateLimit-Remaining", strconv.Itoa(limit-count))
			c.Response().Header().Set("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(m.config.Expiry).Unix(), 10))

			// 如果超过限制，返回错误
			if exceeded {
				m.logger.Warn("Rate limit exceeded",
					zap.String("ip", clientIP),
					zap.String("path", path),
					zap.String("role", string(role)),
					zap.Int("limit", limit),
					zap.Int("count", count))

				return echo.NewHTTPError(http.StatusTooManyRequests, "API rate limit exceeded")
			}

			return next(c)
		}
	}
}

// 路径匹配函数，简化版
func pathMatch(pattern, path string) bool {
	// 如果模式以 * 结尾，则检查前缀
	if strings.HasSuffix(pattern, "*") {
		return strings.HasPrefix(path, strings.TrimSuffix(pattern, "*"))
	}
	// 否则精确匹配
	return pattern == path
}

// determineLimit 确定请求的速率限制
func (m *RateLimiterMiddleware) determineLimit(path string, role user.Role) int {
	// 首先检查路径特定限制
	for pattern, limit := range m.config.PathLimits {
		if pathMatch(pattern, path) {
			return limit
		}
	}

	// 然后检查用户角色限制
	if role != "" {
		if limit, ok := m.config.RoleLimits[role]; ok {
			return limit
		}
	}

	// 默认限制
	return m.config.DefaultLimit
}

// checkRateLimit 检查是否超过速率限制
func (m *RateLimiterMiddleware) checkRateLimit(ctx context.Context, key string, limit int) (bool, int, error) {
	// 获取当前计数
	countStr, err := m.redisClient.Get(ctx, key)
	if err != nil {
		// 检查是否是键不存在错误
		if err == goredis.Nil {
			// 键不存在，这是首次访问
			countStr = "0"
		} else {
			// 其他错误
			return false, 0, fmt.Errorf("failed to get rate limit count: %w", err)
		}
	}

	count := 0
	if countStr != "" {
		count, err = strconv.Atoi(countStr)
		if err != nil {
			return false, 0, fmt.Errorf("invalid rate limit count: %w", err)
		}
	}

	// 增加计数
	count++

	// 保存计数，如果是新键则设置过期时间
	err = m.redisClient.Set(ctx, key, strconv.Itoa(count), m.config.Expiry)
	if err != nil {
		return false, count, fmt.Errorf("failed to update rate limit count: %w", err)
	}

	// 判断是否超过限制
	return count > limit, count, nil
}

// GlobalRateLimit 全局速率限制中间件
func (m *RateLimiterMiddleware) GlobalRateLimit(limit int) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 获取客户端IP
			clientIP := c.RealIP()

			// 生成Redis键
			key := fmt.Sprintf("rate_limit:global:%s", clientIP)

			// 检查是否超过限制
			exceeded, count, err := m.checkRateLimit(c.Request().Context(), key, limit)
			if err != nil {
				m.logger.Error("Global rate limit check failed", zap.Error(err), zap.String("ip", clientIP))
				// 发生错误时继续处理请求，不阻止用户
				return next(c)
			}

			// 设置速率限制响应头
			c.Response().Header().Set("X-RateLimit-Limit", strconv.Itoa(limit))
			c.Response().Header().Set("X-RateLimit-Remaining", strconv.Itoa(limit-count))
			c.Response().Header().Set("X-RateLimit-Reset", strconv.FormatInt(time.Now().Add(m.config.Expiry).Unix(), 10))

			// 如果超过限制，返回错误
			if exceeded {
				m.logger.Warn("Global rate limit exceeded", zap.String("ip", clientIP), zap.Int("limit", limit))
				return echo.NewHTTPError(http.StatusTooManyRequests, "Global API rate limit exceeded")
			}

			return next(c)
		}
	}
}

// CircuitBreaker 熔断器中间件
func (m *RateLimiterMiddleware) CircuitBreaker(threshold int, timeout time.Duration) echo.MiddlewareFunc {
	// 每个路径的错误计数
	errorCounts := make(map[string]int)
	// 每个路径的熔断状态
	circuitOpen := make(map[string]time.Time)
	// 锁，用于并发保护
	mutex := &sync.RWMutex{}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			path := c.Request().URL.Path

			// 检查熔断状态
			mutex.RLock()
			openTime, isOpen := circuitOpen[path]
			mutex.RUnlock()

			// 如果熔断器打开，检查是否已超过超时时间
			if isOpen {
				if time.Now().Before(openTime.Add(timeout)) {
					// 熔断器仍然打开，返回错误
					return echo.NewHTTPError(http.StatusServiceUnavailable, "Service temporarily unavailable")
				}

				// 超过超时时间，重置熔断器
				mutex.Lock()
				delete(circuitOpen, path)
				errorCounts[path] = 0
				mutex.Unlock()
			}

			// 执行请求
			err := next(c)

			// 如果发生错误，增加错误计数
			if err != nil {
				statusCode := http.StatusInternalServerError
				if httpErr, ok := err.(*echo.HTTPError); ok {
					statusCode = httpErr.Code
				}

				// 只有服务器错误才计入熔断
				if statusCode >= 500 {
					mutex.Lock()
					errorCounts[path]++
					count := errorCounts[path]

					// 如果错误数超过阈值，打开熔断器
					if count >= threshold {
						circuitOpen[path] = time.Now()
						errorCounts[path] = 0
						m.logger.Warn("Circuit breaker opened",
							zap.String("path", path),
							zap.Int("threshold", threshold),
							zap.Duration("timeout", timeout))
					}
					mutex.Unlock()
				}
			} else {
				// 成功请求，减少错误计数
				mutex.Lock()
				if errorCounts[path] > 0 {
					errorCounts[path]--
				}
				mutex.Unlock()
			}

			return err
		}
	}
}
