package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"vereal/letsesim/internal/domain/reseller"
	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/service"
	jwtpkg "vereal/letsesim/pkg/jwt"
	"vereal/letsesim/pkg/redis"

	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	userService     *service.UserService
	resellerService *service.ResellerService
	tokenService    *jwtpkg.TokenService
	redisClient     *redis.Client
	logger          *zap.Logger
}

// NewAuthMiddleware 创建新的认证中间件
func NewAuthMiddleware(
	userService *service.UserService,
	resellerService *service.ResellerService,
	tokenService *jwtpkg.TokenService,
	redisClient *redis.Client,
	logger *zap.Logger,
) *AuthMiddleware {
	return &AuthMiddleware{
		userService:     userService,
		resellerService: resellerService,
		tokenService:    tokenService,
		redisClient:     redisClient,
		logger:          logger,
	}
}

// 从请求中获取令牌
func extractToken(c echo.Context) string {
	// 从Authorization头获取
	authHeader := c.Request().Header.Get("Authorization")
	if authHeader != "" {
		// Bearer token 格式解析
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			return parts[1]
		}
	}

	// 从查询参数获取
	token := c.QueryParam("token")
	if token != "" {
		return token
	}

	// 从Cookie获取
	cookie, err := c.Cookie("token")
	if err == nil {
		return cookie.Value
	}

	return ""
}

// isTokenBlacklisted 检查令牌是否在黑名单中
func (m *AuthMiddleware) isTokenBlacklisted(ctx context.Context, tokenID string) bool {
	exists, err := m.redisClient.Exists(ctx, "token_blacklist:"+tokenID)
	if err != nil {
		m.logger.Error("Failed to check token blacklist", zap.Error(err))
		return false
	}
	return exists
}

// blacklistToken 将令牌添加到黑名单
func (m *AuthMiddleware) blacklistToken(ctx context.Context, tokenID string, expiration time.Time) error {
	// 计算令牌剩余有效期
	duration := time.Until(expiration)
	if duration <= 0 {
		// 令牌已过期，无需添加到黑名单
		return nil
	}

	// 将令牌添加到黑名单，有效期设为剩余时间
	return m.redisClient.Set(ctx, "token_blacklist:"+tokenID, "1", duration)
}

// UserAuth 用户认证中间件
func (m *AuthMiddleware) UserAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 获取令牌
			tokenString := extractToken(c)
			if tokenString == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, "未提供认证令牌")
			}

			// 验证JWT令牌并获取用户信息
			claims, err := m.tokenService.ValidateToken(tokenString)
			if err != nil {
				// 如果是令牌过期错误，尝试从刷新令牌更新
				if err == jwtpkg.ErrTokenExpired {
					// 获取刷新令牌
					refreshToken := c.Request().Header.Get("X-Refresh-Token")
					if refreshToken != "" {
						// 尝试刷新令牌
						tokenPair, err := m.tokenService.RefreshAccessToken(refreshToken)
						if err == nil {
							// 设置新令牌到响应头
							c.Response().Header().Set("X-New-Access-Token", tokenPair.AccessToken)
							c.Response().Header().Set("X-New-Refresh-Token", tokenPair.RefreshToken)
							c.Response().Header().Set("X-Token-Expires-In", fmt.Sprintf("%d", tokenPair.ExpiresIn))

							// 使用新令牌中的信息
							newClaims, err := m.tokenService.ValidateToken(tokenPair.AccessToken)
							if err == nil {
								claims = newClaims
							} else {
								return echo.NewHTTPError(http.StatusUnauthorized, "认证令牌无效")
							}
						} else {
							return echo.NewHTTPError(http.StatusUnauthorized, "认证令牌已过期")
						}
					} else {
						return echo.NewHTTPError(http.StatusUnauthorized, "认证令牌已过期")
					}
				} else {
					return echo.NewHTTPError(http.StatusUnauthorized, "认证令牌无效")
				}
			}

			// 检查令牌是否在黑名单中
			if m.isTokenBlacklisted(c.Request().Context(), claims.ID) {
				return echo.NewHTTPError(http.StatusUnauthorized, "认证令牌已被吊销")
			}

			// 获取用户
			userID := claims.UserID
			user, err := m.userService.GetUserByID(c.Request().Context(), userID)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "用户不存在")
			}

			// 检查用户状态
			if !user.IsActive() {
				return echo.NewHTTPError(http.StatusForbidden, "用户账号已被禁用")
			}

			// 将用户信息存储到上下文
			c.Set("user", user)
			c.Set("token", tokenString)
			c.Set("claims", claims)

			return next(c)
		}
	}
}

// AdminAuth 管理员认证中间件
func (m *AuthMiddleware) AdminAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 先执行用户认证
			err := m.UserAuth()(func(c echo.Context) error {
				// 这里不会执行，仅用于获取用户信息
				return nil
			})(c)

			if err != nil {
				return err
			}

			// 获取用户
			user := c.Get("user").(*user.User)

			// 检查是否为管理员
			if !user.IsAdmin() {
				return echo.NewHTTPError(http.StatusForbidden, "需要管理员权限")
			}

			return next(c)
		}
	}
}

// ResellerAuth 代理商认证中间件
func (m *AuthMiddleware) ResellerAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 先执行用户认证
			err := m.UserAuth()(func(c echo.Context) error {
				// 这里不会执行，仅用于获取用户信息
				return nil
			})(c)

			if err != nil {
				return err
			}

			// 获取用户
			user := c.Get("user").(*user.User)

			// 检查是否为代理商
			if !user.IsReseller() && !user.IsEnterprise() {
				return echo.NewHTTPError(http.StatusForbidden, "需要代理商权限")
			}

			// 获取代理商信息
			reseller, err := m.resellerService.GetResellerByUserID(c.Request().Context(), user.ID)
			if err != nil {
				return echo.NewHTTPError(http.StatusForbidden, "代理商信息不存在")
			}

			// 检查代理商状态
			if !reseller.IsActive() {
				return echo.NewHTTPError(http.StatusForbidden, "代理商账号已被禁用")
			}

			// 将代理商信息存储到上下文
			c.Set("reseller", reseller)

			return next(c)
		}
	}
}

// EnterpriseAuth 企业代理商认证中间件
func (m *AuthMiddleware) EnterpriseAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 先执行代理商认证
			err := m.ResellerAuth()(func(c echo.Context) error {
				// 这里不会执行，仅用于获取代理商信息
				return nil
			})(c)

			if err != nil {
				return err
			}

			// 获取代理商
			reseller := c.Get("reseller").(*reseller.Reseller)

			// 检查是否为企业代理商
			if !reseller.IsEnterprise() {
				return echo.NewHTTPError(http.StatusForbidden, "需要企业代理商权限")
			}

			return next(c)
		}
	}
}

// LogoutHandler 退出登录处理
func (m *AuthMiddleware) LogoutHandler() echo.HandlerFunc {
	return func(c echo.Context) error {
		// 获取当前令牌
		tokenString, ok := c.Get("token").(string)
		if !ok || tokenString == "" {
			return c.NoContent(http.StatusOK)
		}

		// 解析令牌获取过期时间（不验证签名）
		token, err := m.tokenService.ParseTokenWithoutValidation(tokenString)
		if err != nil {
			m.logger.Warn("Failed to parse token during logout", zap.Error(err))
			return c.NoContent(http.StatusOK)
		}

		// 获取令牌ID和过期时间
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			m.logger.Warn("Invalid token claims during logout")
			return c.NoContent(http.StatusOK)
		}

		// 获取过期时间
		expTime, err := m.tokenService.GetExpirationTime(tokenString)
		if err != nil {
			m.logger.Warn("Failed to get token expiration time", zap.Error(err))
			return c.NoContent(http.StatusOK)
		}

		// 获取令牌ID
		jti, ok := claims["jti"].(string)
		if !ok || jti == "" {
			// 如果令牌没有ID，使用整个令牌作为ID
			jti = tokenString
		}

		// 将令牌添加到黑名单
		err = m.blacklistToken(c.Request().Context(), jti, expTime)
		if err != nil {
			m.logger.Error("Failed to blacklist token", zap.Error(err))
		}

		// 获取刷新令牌
		refreshToken := c.Request().Header.Get("X-Refresh-Token")
		if refreshToken != "" {
			// 解析刷新令牌
			refreshClaims, err := m.tokenService.ParseTokenWithoutValidation(refreshToken)
			if err == nil {
				refreshClaimsMap, ok := refreshClaims.Claims.(jwt.MapClaims)
				if ok {
					// 获取刷新令牌过期时间
					refreshExpTime, err := m.tokenService.GetExpirationTime(refreshToken)
					if err == nil {
						// 获取刷新令牌ID
						refreshJti, ok := refreshClaimsMap["jti"].(string)
						if !ok || refreshJti == "" {
							refreshJti = refreshToken
						}
						// 将刷新令牌添加到黑名单
						err = m.blacklistToken(c.Request().Context(), refreshJti, refreshExpTime)
						if err != nil {
							m.logger.Error("Failed to blacklist refresh token", zap.Error(err))
						}
					}
				}
			}
		}

		return c.NoContent(http.StatusOK)
	}
}

// ResellerAPIAuth 代理商API认证中间件（使用API Key + 可选JWT）
func (m *AuthMiddleware) ResellerAPIAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 获取API密钥
			apiKey := c.Request().Header.Get("X-API-Key")
			apiSecret := c.Request().Header.Get("X-API-Secret")

			if apiKey == "" || apiSecret == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, "未提供API密钥")
			}

			// 验证代理商
			reseller, err := m.resellerService.AuthenticateReseller(c.Request().Context(), apiKey, apiSecret)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "API密钥无效")
			}

			// 将代理商信息存储到上下文
			c.Set("reseller", reseller)

			// 尝试获取JWT（如果有）
			tokenString := extractToken(c)
			if tokenString != "" {
				// 验证JWT令牌并获取用户信息
				claims, err := m.tokenService.ValidateToken(tokenString)
				if err == nil && !m.isTokenBlacklisted(c.Request().Context(), claims.ID) {
					// 获取用户
					userID := claims.UserID
					user, err := m.userService.GetUserByID(c.Request().Context(), userID)
					if err == nil && user.IsActive() {
						// 检查用户是否与代理商关联
						linkedReseller, err := m.resellerService.GetResellerByUserID(c.Request().Context(), user.ID)
						if err == nil && linkedReseller.ID == reseller.ID {
							// 将用户信息存储到上下文
							c.Set("user", user)
							c.Set("token", tokenString)
							c.Set("claims", claims)
						}
					}
				}
			}

			return next(c)
		}
	}
}
