package api

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"vereal/letsesim/internal/api/handlers"
	apimiddleware "vereal/letsesim/internal/api/middleware"
)

// SetupRouter sets up the API routes
func SetupRouter(
	e *echo.Echo,
	authMiddleware *apimiddleware.AuthMiddleware,
	loggerMiddleware *apimiddleware.LoggerMiddleware,
	errorMiddleware *apimiddleware.ErrorMiddleware,
	userHandler handlers.UserHandlerInterface,
	esimHandler handlers.ESIMHandlerInterface,
	orderHandler handlers.OrderHandlerInterface,
	promotionHandler handlers.PromotionHandlerInterface,
	resellerHandler handlers.ResellerHandlerInterface,
	enterpriseHandler handlers.EnterpriseHandlerInterface,
	creditHandler handlers.CreditHandlerInterface,
	rateHandler handlers.RateHandlerInterface,
	rateLimiterMiddleware *apimiddleware.RateLimiterMiddleware,
) {
	// Set up middleware
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())
	e.Use(loggerMiddleware.Logger())
	e.HTTPErrorHandler = errorMiddleware.ErrorHandler()

	// API version prefix
	api := e.Group("/api/v1")

	// Public routes
	public := api.Group("")

	// Add rate limiting for specific routes
	authRoutes := public.Group("")
	authRoutes.Use(rateLimiterMiddleware.RateLimit()) // Use default configuration, limit based on user role and path

	// User authentication routes
	authRoutes.POST("/auth/register", userHandler.Register)
	authRoutes.POST("/auth/login", userHandler.Login)
	authRoutes.POST("/auth/password/reset", userHandler.ResetPassword)
	authRoutes.GET("/auth/password/reset/verify", userHandler.VerifyResetToken)
	authRoutes.POST("/auth/password/reset/complete", userHandler.CompletePasswordReset)
	authRoutes.POST("/auth/logout", authMiddleware.LogoutHandler())

	// User routes (requires authentication)
	users := api.Group("/users")
	users.Use(authMiddleware.UserAuth())
	users.GET("/me", userHandler.GetProfile)
	users.PUT("/me", userHandler.UpdateProfile)
	users.PUT("/me/password", userHandler.ChangePassword)
	users.GET("/me/esims", esimHandler.ListUserESIMs)
	users.GET("/me/orders", orderHandler.ListUserOrders)
	users.DELETE("/me", userHandler.DisableAccount)

	// eSIM provider routes (requires authentication)
	providers := api.Group("/providers")
	providers.Use(authMiddleware.UserAuth())
	providers.GET("", esimHandler.ListProviders)
	providers.GET("/:provider/packages", esimHandler.ListPackages)
	providers.GET("/:provider/packages/:id", esimHandler.GetPackageDetails)
	providers.GET("/:provider/regions", esimHandler.ListSupportedRegions)

	// User eSIM management routes
	esims := api.Group("/esims")
	esims.Use(authMiddleware.UserAuth())
	esims.GET("", esimHandler.ListESIMs)
	esims.GET("/:id", esimHandler.GetESIMDetails)
	esims.PUT("/:id/status", esimHandler.UpdateESIMStatus)
	esims.POST("/:id/data", esimHandler.AddESIMData)
	esims.POST("/:id/messages", esimHandler.SendESIMMessage)
	esims.GET("/:id/usage", esimHandler.GetESIMUsage)

	// Order routes (requires authentication)
	orders := api.Group("/orders")
	orders.Use(authMiddleware.UserAuth())
	orders.POST("", orderHandler.CreateOrder)
	orders.GET("/:id", orderHandler.GetOrder)
	orders.GET("", orderHandler.ListOrders)
	orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)

	// Credit routes (requires authentication)
	credits := api.Group("/credits")
	credits.Use(authMiddleware.UserAuth())
	credits.GET("", creditHandler.GetUserCredit)
	credits.GET("/transactions", creditHandler.GetTransactionHistory)
	credits.GET("/transactions/:id", creditHandler.GetTransactionDetail)
	credits.POST("", creditHandler.AddCredit)

	// Promotion routes (user can access)
	promotions := api.Group("/promotions")
	promotions.Use(authMiddleware.UserAuth())
	promotions.POST("/validate", promotionHandler.ValidatePromotion)
	promotions.POST("", promotionHandler.ApplyPromotion)

	// Reseller routes (requires reseller authentication)
	resellers := api.Group("/resellers")
	resellers.Use(authMiddleware.ResellerAuth())

	resellers.GET("/me", resellerHandler.GetResellerProfile)
	resellers.PUT("/me", resellerHandler.UpdateResellerProfile)
	resellers.GET("/me/balance", resellerHandler.GetResellerBalance)

	resellers.GET("/users", resellerHandler.ListResellerUsers)
	resellers.POST("/users", resellerHandler.CreateResellerUser)
	resellers.GET("/users/:id", resellerHandler.GetResellerUser)
	resellers.GET("/users/:id/esims", resellerHandler.GetResellerUserESIMs)
	resellers.GET("/users/:id/orders", resellerHandler.GetResellerUserOrders)
	resellers.POST("/users/:id/credits", creditHandler.AddResellerUserCredit)

	resellers.GET("/promotions", promotionHandler.ListResellerPromotions)
	resellers.POST("/promotions", promotionHandler.CreateResellerPromotion)
	resellers.GET("/promotions/:id", promotionHandler.GetResellerPromotion)
	resellers.PUT("/promotions/:id", promotionHandler.UpdateResellerPromotion)
	resellers.DELETE("/promotions/:id", promotionHandler.DeleteResellerPromotion)

	resellers.GET("/rates", rateHandler.ListResellerRates)
	resellers.POST("/rates", rateHandler.CreateResellerRate)

	// Enterprise routes (requires enterprise authentication)
	enterprises := api.Group("/enterprises")
	enterprises.Use(authMiddleware.EnterpriseAuth())

	enterprises.GET("/employees", enterpriseHandler.ListEmployees)
	enterprises.POST("/employees", enterpriseHandler.CreateEmployee)
	enterprises.GET("/employees/:id", enterpriseHandler.GetEmployee)
	enterprises.PUT("/employees/:id", enterpriseHandler.UpdateEmployee)
	enterprises.DELETE("/employees/:id", enterpriseHandler.DeleteEmployee)

	enterprises.GET("/departments", enterpriseHandler.ListDepartments)
	enterprises.POST("/departments", enterpriseHandler.CreateDepartment)
	enterprises.GET("/departments/:id", enterpriseHandler.GetDepartment)
	enterprises.PUT("/departments/:id", enterpriseHandler.UpdateDepartment)
	enterprises.DELETE("/departments/:id", enterpriseHandler.DeleteDepartment)

	enterprises.POST("/esims/assignments", enterpriseHandler.AssignESIMs)
	enterprises.DELETE("/esims/assignments", enterpriseHandler.ReclaimESIMs)
	enterprises.GET("/employees/:id/esims", enterpriseHandler.GetEmployeeESIMs)

	enterprises.GET("/rates", rateHandler.ListEnterpriseRates)
	enterprises.POST("/rates", rateHandler.CreateEnterpriseRate)

	// Admin routes
	admin := api.Group("/admin")
	admin.Use(authMiddleware.AdminAuth())
	admin.GET("/users", userHandler.ListUsers)
	admin.POST("/users/:id/credits", creditHandler.AdminAddUserCredit)
	admin.POST("/promotions", promotionHandler.CreatePromotion)
	admin.GET("/promotions/:id", promotionHandler.GetPromotion)
	admin.GET("/promotions", promotionHandler.ListPromotions)
	admin.PUT("/promotions/:id", promotionHandler.UpdatePromotion)
	admin.DELETE("/promotions/:id", promotionHandler.DeletePromotion)
	admin.POST("/resellers", resellerHandler.CreateReseller)
	admin.GET("/resellers/:id", resellerHandler.GetReseller)
	admin.PUT("/resellers/:id", resellerHandler.UpdateReseller)
	admin.PUT("/resellers/:id/status", resellerHandler.UpdateResellerStatus)
	admin.POST("/resellers/:id/api-keys", resellerHandler.RegenerateAPIKey)
	admin.POST("/resellers/:id/balance", resellerHandler.AddResellerBalance)
	admin.GET("/resellers", resellerHandler.ListResellers)
	admin.GET("/enterprises", enterpriseHandler.ListEnterprises)
	admin.GET("/enterprises/:id", enterpriseHandler.GetEnterprise)
	admin.PUT("/enterprises/:id", enterpriseHandler.UpdateEnterprise)
	admin.GET("/rates", rateHandler.ListGlobalRates)
	admin.POST("/rates", rateHandler.CreateGlobalRate)
	admin.PUT("/rates/:id", rateHandler.UpdateGlobalRate)
	admin.DELETE("/rates/:id", rateHandler.DeleteGlobalRate)

	// Webhook routes
	webhooks := api.Group("/webhooks")
	webhooks.POST("/:provider", esimHandler.HandleWebhook)
}
