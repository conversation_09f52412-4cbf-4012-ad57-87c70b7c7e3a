package reseller

import (
	"time"
)

// Status 代理商状态
type Status string

const (
	StatusActive   Status = "ACTIVE"
	StatusInactive Status = "INACTIVE"
	StatusPending  Status = "PENDING"
)

// Type 代理商类型
type Type string

const (
	TypeRegular    Type = "RESELLER"
	TypeEnterprise Type = "ENTERPRISE"
)

// Reseller 代理商模型
type Reseller struct {
	ID             string                 `json:"id"`
	UserID         string                 `json:"userId"`
	CompanyName    string                 `json:"companyName"`
	ContactPerson  string                 `json:"contactPerson"`
	Email          string                 `json:"email"`
	Phone          string                 `json:"phone"`
	Address        string                 `json:"address,omitempty"`
	Country        string                 `json:"country"`
	Status         Status                 `json:"status"`
	Type           Type                   `json:"type"`           // 新增，代理商类型
	CommissionRate int                    `json:"commissionRate"` // 佣金率，百分比乘以100
	Balance        int64                  `json:"balance"`
	Currency       string                 `json:"currency"`
	APIKey         string                 `json:"-"` // 不输出到JSON
	APISecret      string                 `json:"-"` // 不输出到JSON
	CallbackURL    string                 `json:"callbackUrl,omitempty"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// NewReseller 创建新代理商
func NewReseller(userID, companyName, contactPerson, email, phone, country string, commissionRate int, resellerType Type) *Reseller {
	now := time.Now()
	return &Reseller{
		UserID:         userID,
		CompanyName:    companyName,
		ContactPerson:  contactPerson,
		Email:          email,
		Phone:          phone,
		Country:        country,
		Status:         StatusPending,
		Type:           resellerType,
		CommissionRate: commissionRate,
		Balance:        0,
		Currency:       "USD", // 默认币种
		CreatedAt:      now,
		UpdatedAt:      now,
		Metadata:       map[string]interface{}{},
	}
}

// IsActive 检查代理商是否活动
func (r *Reseller) IsActive() bool {
	return r.Status == StatusActive
}

// IsEnterprise 检查是否是企业代理商
func (r *Reseller) IsEnterprise() bool {
	return r.Type == TypeEnterprise
}

// HasSufficientBalance 检查代理商是否有足够余额
func (r *Reseller) HasSufficientBalance(amount int64) bool {
	return r.Balance >= amount
}

// AddBalance 添加余额
func (r *Reseller) AddBalance(amount int64) {
	r.Balance += amount
	r.UpdatedAt = time.Now()
}

// DeductBalance 扣除余额
func (r *Reseller) DeductBalance(amount int64) bool {
	if r.Balance < amount {
		return false
	}

	r.Balance -= amount
	r.UpdatedAt = time.Now()
	return true
}

// CalculateCommission 计算佣金
func (r *Reseller) CalculateCommission(orderAmount int64) int64 {
	return orderAmount * int64(r.CommissionRate) / 10000 // 佣金率百分比乘以100
}
