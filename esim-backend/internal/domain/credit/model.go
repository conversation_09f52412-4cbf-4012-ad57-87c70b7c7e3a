package credit

import (
	"time"
)

// Status 交易状态
type Status string

const (
	StatusPending   Status = "PENDING"
	StatusCompleted Status = "COMPLETED"
	StatusFailed    Status = "FAILED"
)

// TransactionType 交易类型
type TransactionType string

const (
	TypeDeposit      TransactionType = "DEPOSIT"       // 充值
	TypeWithdrawal   TransactionType = "WITHDRAWAL"    // 提现
	TypeOrderPayment TransactionType = "ORDER_PAYMENT" // 订单支付
	TypeRefund       TransactionType = "REFUND"        // 退款
)

// Credit 用户积分模型
type Credit struct {
	ID          string    `json:"id"`
	UserID      string    `json:"userId"`
	Balance     float64   `json:"balance"`
	Currency    string    `json:"currency"`
	LastUpdated time.Time `json:"lastUpdated"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// Transaction 积分交易记录模型
type Transaction struct {
	ID          string          `json:"id"`
	UserID      string          `json:"userId"`
	Amount      float64         `json:"amount"`
	Balance     float64         `json:"balance"`
	Type        TransactionType `json:"type"`
	Description string          `json:"description,omitempty"`
	OrderID     string          `json:"orderId,omitempty"`
	Status      Status          `json:"status"`
	CreatedAt   time.Time       `json:"createdAt"`
	UpdatedAt   time.Time       `json:"updatedAt"`
}

// NewCredit 创建新的用户积分
func NewCredit(userID string, currency string) *Credit {
	now := time.Now()
	return &Credit{
		UserID:      userID,
		Balance:     0,
		Currency:    currency,
		LastUpdated: now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// NewTransaction 创建新的交易记录
func NewTransaction(userID string, amount float64, balance float64, transType TransactionType, description string, orderID string) *Transaction {
	now := time.Now()
	return &Transaction{
		UserID:      userID,
		Amount:      amount,
		Balance:     balance,
		Type:        transType,
		Description: description,
		OrderID:     orderID,
		Status:      StatusCompleted,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// HasSufficientBalance 检查是否有足够余额
func (c *Credit) HasSufficientBalance(amount float64) bool {
	return c.Balance >= amount
}

// AddBalance 增加余额
func (c *Credit) AddBalance(amount float64) {
	c.Balance += amount
	c.LastUpdated = time.Now()
	c.UpdatedAt = time.Now()
}

// DeductBalance 扣除余额
func (c *Credit) DeductBalance(amount float64) bool {
	if !c.HasSufficientBalance(amount) {
		return false
	}

	c.Balance -= amount
	c.LastUpdated = time.Now()
	c.UpdatedAt = time.Now()
	return true
}
