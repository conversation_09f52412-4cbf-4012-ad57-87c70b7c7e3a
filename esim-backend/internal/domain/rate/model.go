package rate

import (
	"time"
)

// Rate 费率模型
type Rate struct {
	ID        string    `json:"id"`
	PackageID string    `json:"packageId"`
	Country   string    `json:"country,omitempty"`
	Rate      int64     `json:"rate"` // 单位：分
	Currency  string    `json:"currency"`
	OwnerType string    `json:"ownerType"` // GLOBAL, RESELLER, ENTERPRISE
	OwnerID   string    `json:"ownerId,omitempty"`
	IsActive  bool      `json:"isActive"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

// 费率所有者类型
const (
	OwnerTypeGlobal     = "GLOBAL"
	OwnerTypeReseller   = "RESELLER"
	OwnerTypeEnterprise = "ENTERPRISE"
)

// NewRate 创建新费率
func NewRate(packageID, country string, rateValue int64, currency, ownerType, ownerID string) *Rate {
	now := time.Now()
	return &Rate{
		PackageID: packageID,
		Country:   country,
		Rate:      rateValue,
		Currency:  currency,
		OwnerType: ownerType,
		OwnerID:   ownerID,
		IsActive:  true,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// NewGlobalRate 创建全局费率
func NewGlobalRate(packageID, country string, rateValue int64, currency string) *Rate {
	return NewRate(packageID, country, rateValue, currency, OwnerTypeGlobal, "")
}

// NewResellerRate 创建代理商费率
func NewResellerRate(packageID, country string, rateValue int64, currency, resellerID string) *Rate {
	return NewRate(packageID, country, rateValue, currency, OwnerTypeReseller, resellerID)
}

// NewEnterpriseRate 创建企业代理商费率
func NewEnterpriseRate(packageID, country string, rateValue int64, currency, enterpriseID string) *Rate {
	return NewRate(packageID, country, rateValue, currency, OwnerTypeEnterprise, enterpriseID)
}
