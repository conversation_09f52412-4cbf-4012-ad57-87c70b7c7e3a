package order

import (
	"time"

	"vereal/letsesim/pkg/esim"
)

// Status 订单状态
type Status string

const (
	StatusPending    Status = "PENDING"
	StatusProcessing Status = "PROCESSING"
	StatusCompleted  Status = "COMPLETED"
	StatusFailed     Status = "FAILED"
	StatusCancelled  Status = "CANCELLED"
	StatusRefunded   Status = "REFUNDED"
)

// Order 订单模型
type Order struct {
	ID              string                 `json:"id"`
	UserID          string                 `json:"userId"`
	ResellerID      string                 `json:"resellerId,omitempty"`
	ProviderType    string                 `json:"providerType"`
	TransactionID   string                 `json:"transactionId"`
	ProviderOrderNo string                 `json:"providerOrderNo,omitempty"`
	Status          Status                 `json:"status"`
	TotalAmount     int64                  `json:"totalAmount"`
	Currency        string                 `json:"currency"`
	Items           []Item                 `json:"items"`
	ESIMs           []ESIM                 `json:"esims,omitempty"`
	CreatedAt       time.Time              `json:"createdAt"`
	UpdatedAt       time.Time              `json:"updatedAt"`
	CompletedAt     time.Time              `json:"completedAt,omitempty"`
	PromotionID     string                 `json:"promotionId,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// Item 订单项
type Item struct {
	PackageID     string   `json:"packageId"`
	PackageName   string   `json:"packageName"`
	Quantity      int      `json:"quantity"`
	UnitPrice     int64    `json:"unitPrice"`
	Currency      string   `json:"currency"`
	DataVolume    int64    `json:"dataVolume"`
	ValidityDays  int      `json:"validityDays"`
	LocationCodes []string `json:"locationCodes"`
}

// ESIM 订单中的eSIM
type ESIM struct {
	ICCID         string    `json:"iccid"`
	ESIMTranNo    string    `json:"esimTranNo,omitempty"`
	Status        string    `json:"status"`
	ActivationURL string    `json:"activationUrl,omitempty"`
	QRCodeURL     string    `json:"qrCodeUrl,omitempty"`
	DataVolume    int64     `json:"dataVolume"`
	ValidityDays  int       `json:"validityDays"`
	ExpiryDate    time.Time `json:"expiryDate,omitempty"`
	ActivatedAt   time.Time `json:"activatedAt,omitempty"`
}

// NewOrder 创建新订单
func NewOrder(userID, providerType, transactionID string, totalAmount int64, currency string) *Order {
	now := time.Now()
	return &Order{
		UserID:        userID,
		ProviderType:  providerType,
		TransactionID: transactionID,
		Status:        StatusPending,
		TotalAmount:   totalAmount,
		Currency:      currency,
		Items:         []Item{},
		ESIMs:         []ESIM{},
		CreatedAt:     now,
		UpdatedAt:     now,
		Metadata:      map[string]interface{}{},
	}
}

// AddItem 添加订单项
func (o *Order) AddItem(packageID, packageName string, quantity int, unitPrice int64, currency string, dataVolume int64, validityDays int, locationCodes []string) {
	o.Items = append(o.Items, Item{
		PackageID:     packageID,
		PackageName:   packageName,
		Quantity:      quantity,
		UnitPrice:     unitPrice,
		Currency:      currency,
		DataVolume:    dataVolume,
		ValidityDays:  validityDays,
		LocationCodes: locationCodes,
	})
}

// AddESIM 添加eSIM
func (o *Order) AddESIM(esim esim.ESIM) {
	o.ESIMs = append(o.ESIMs, ESIM{
		ICCID:         esim.Identifier.ICCID,
		ESIMTranNo:    esim.Identifier.ESIMTranNo,
		Status:        esim.Status.Code,
		ActivationURL: esim.ActivationCode,
		QRCodeURL:     esim.QRCodeURL,
		DataVolume:    esim.DataVolume,
		ValidityDays:  esim.ValidityDays,
		ExpiryDate:    esim.ExpiryTime,
	})
}

// UpdateStatus 更新订单状态
func (o *Order) UpdateStatus(status Status) {
	o.Status = status
	o.UpdatedAt = time.Now()

	if status == StatusCompleted {
		o.CompletedAt = time.Now()
	}
}

// IsComplete 检查订单是否完成
func (o *Order) IsComplete() bool {
	return o.Status == StatusCompleted
}

// IsCancellable 检查订单是否可取消
func (o *Order) IsCancellable() bool {
	return o.Status == StatusPending || o.Status == StatusProcessing
}
