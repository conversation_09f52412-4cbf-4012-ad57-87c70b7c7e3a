package esim

import (
	"time"
)

// Status eSIM状态
type Status string

const (
	StatusPending  Status = "PENDING"  // 待激活
	StatusActive   Status = "ACTIVE"   // 已激活
	StatusInactive Status = "INACTIVE" // 已停用
	StatusExpired  Status = "EXPIRED"  // 已过期
)

// PackageStatus 套餐状态
type PackageStatus string

const (
	PackageStatusActive       PackageStatus = "ACTIVE"       // 活跃状态
	PackageStatusInactive     PackageStatus = "INACTIVE"     // 不活跃
	PackageStatusDiscontinued PackageStatus = "DISCONTINUED" // 已下架但保留引用
)

// RegionType 区域类型
type RegionType string

const (
	RegionTypeContinent RegionType = "CONTINENT" // 大洲
	RegionTypeCountry   RegionType = "COUNTRY"   // 国家
	RegionTypeRegion    RegionType = "REGION"    // 地区
)

// ESIM eSIM模型
type ESIM struct {
	ID              string    `json:"id"`
	UserID          string    `json:"userId"`
	OrderID         string    `json:"orderId"`
	ProviderType    string    `json:"providerType"`
	ICCID           string    `json:"iccid,omitempty"`
	ESIMTranNo      string    `json:"esimTranNo,omitempty"`
	ExternalOrderNo string    `json:"externalOrderNo,omitempty"`
	ActivationCode  string    `json:"activationCode,omitempty"`
	QRCodeURL       string    `json:"qrCodeUrl,omitempty"`
	Status          Status    `json:"status"`
	ValidityDays    int       `json:"validityDays"`
	ExpiryTime      time.Time `json:"expiryTime,omitempty"`
	SupportsSMS     bool      `json:"supportsSMS"`
	MSISDN          string    `json:"msisdn,omitempty"` // 短信相关手机号
	SMSAPIOnly      bool      `json:"smsApiOnly"`
	DataPackageInfo string    `json:"dataPackageInfo,omitempty"` // JSON格式存储的套餐摘要信息
	DataVolume      int64     `json:"dataVolume"`                // 套餐总流量（MB）
	DataUsed        int64     `json:"dataUsed"`                  // 已使用流量（MB）
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
	LastSyncAt      time.Time `json:"lastSyncAt,omitempty"`
	ActivatedAt     time.Time `json:"activatedAt,omitempty"`   // 激活时间
	DeactivatedAt   time.Time `json:"deactivatedAt,omitempty"` // 停用时间
	ResellerID      string    `json:"resellerId,omitempty"`    // 代理商ID，如果通过代理商分配
}

// Package 套餐模型
type Package struct {
	ID            string        `json:"id"`
	ProviderType  string        `json:"providerType"`
	ExternalID    string        `json:"externalId"`
	Name          string        `json:"name"`
	Description   string        `json:"description,omitempty"`
	Price         float64       `json:"price"`
	Currency      string        `json:"currency"`
	DataVolume    int64         `json:"dataVolume"` // 单位：MB
	ValidityDays  int           `json:"validityDays"`
	LocationCodes []string      `json:"locationCodes,omitempty"`
	SupportsSMS   bool          `json:"supportsSMS"`
	DataType      string        `json:"dataType,omitempty"`
	NetworkTypes  []string      `json:"networkTypes,omitempty"`
	SupportTopUp  bool          `json:"supportTopUp"`
	Status        PackageStatus `json:"status"`
	CreatedAt     time.Time     `json:"createdAt"`
	UpdatedAt     time.Time     `json:"updatedAt"`
	LastSyncAt    time.Time     `json:"lastSyncAt,omitempty"`
}

// Region 区域模型
type Region struct {
	ID           string     `json:"id"`
	ProviderType string     `json:"providerType"`
	Code         string     `json:"code"`
	Name         string     `json:"name"`
	Type         RegionType `json:"type"`
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
	LastSyncAt   time.Time  `json:"lastSyncAt,omitempty"`
}

// SubLocation 子区域模型
type SubLocation struct {
	ID           string    `json:"id"`
	Code         string    `json:"code"`
	Name         string    `json:"name"`
	ProviderType string    `json:"providerType"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// ESIMUsage eSIM使用情况
type ESIMUsage struct {
	ID               string    `json:"id"`
	ESIMID           string    `json:"esimId"`
	DataUsed         int64     `json:"dataUsed"`         // 已使用流量（MB）
	DataTotal        int64     `json:"dataTotal"`        // 总流量（MB）
	DataRemaining    int64     `json:"dataRemaining"`    // 剩余流量（MB）
	ValidDays        int       `json:"validDays"`        // 有效天数
	DaysUsed         int       `json:"daysUsed"`         // 已使用天数
	DaysRemaining    int       `json:"daysRemaining"`    // 剩余天数
	LastUpdateTime   time.Time `json:"lastUpdateTime"`   // 上次更新时间
	NextUpdateTime   time.Time `json:"nextUpdateTime"`   // 下次更新时间
	CurrentProvider  string    `json:"currentProvider"`  // 当前连接的网络提供商
	CurrentCountry   string    `json:"currentCountry"`   // 当前连接的国家
	ConnectionStatus string    `json:"connectionStatus"` // 连接状态
	CreatedAt        time.Time `json:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt"`
}

// NewESIM 创建新的eSIM
func NewESIM(userID, orderID, providerType string) *ESIM {
	now := time.Now()
	return &ESIM{
		UserID:       userID,
		OrderID:      orderID,
		ProviderType: providerType,
		Status:       StatusPending,
		SupportsSMS:  false,
		SMSAPIOnly:   true,
		DataVolume:   0,
		DataUsed:     0,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// NewPackage 创建新的套餐
func NewPackage(providerType, externalID, name string, price float64, currency string, dataVolume int64, validityDays int) *Package {
	now := time.Now()
	return &Package{
		ProviderType:  providerType,
		ExternalID:    externalID,
		Name:          name,
		Price:         price,
		Currency:      currency,
		DataVolume:    dataVolume,
		ValidityDays:  validityDays,
		LocationCodes: []string{},
		NetworkTypes:  []string{},
		Status:        PackageStatusActive,
		CreatedAt:     now,
		UpdatedAt:     now,
		LastSyncAt:    now,
	}
}

// NewESIMUsage 创建新的eSIM使用情况
func NewESIMUsage(esimID string, dataTotal int64, validDays int) *ESIMUsage {
	now := time.Now()
	return &ESIMUsage{
		ESIMID:           esimID,
		DataUsed:         0,
		DataTotal:        dataTotal,
		DataRemaining:    dataTotal,
		ValidDays:        validDays,
		DaysUsed:         0,
		DaysRemaining:    validDays,
		LastUpdateTime:   now,
		NextUpdateTime:   now.Add(time.Hour * 6), // 默认6小时更新一次
		ConnectionStatus: "DISCONNECTED",
		CreatedAt:        now,
		UpdatedAt:        now,
	}
}

// IsExpired 检查eSIM是否已过期
func (e *ESIM) IsExpired() bool {
	return e.Status == StatusExpired || time.Now().After(e.ExpiryTime)
}

// IsActive 检查eSIM是否活跃
func (e *ESIM) IsActive() bool {
	return e.Status == StatusActive && !e.IsExpired()
}

// IsAvailable 检查套餐是否可用
func (p *Package) IsAvailable() bool {
	return p.Status == PackageStatusActive
}

// Activate 激活eSIM
func (e *ESIM) Activate() {
	if e.Status == StatusPending {
		now := time.Now()
		e.Status = StatusActive
		e.ActivatedAt = now
		e.UpdatedAt = now
		// 设置过期时间
		if e.ValidityDays > 0 && e.ExpiryTime.IsZero() {
			e.ExpiryTime = now.AddDate(0, 0, e.ValidityDays)
		}
	}
}

// Deactivate 停用eSIM
func (e *ESIM) Deactivate() {
	if e.Status == StatusActive {
		now := time.Now()
		e.Status = StatusInactive
		e.DeactivatedAt = now
		e.UpdatedAt = now
	}
}

// Reactivate 重新激活eSIM
func (e *ESIM) Reactivate() {
	if e.Status == StatusInactive && !e.IsExpired() {
		now := time.Now()
		e.Status = StatusActive
		e.DeactivatedAt = time.Time{} // 清空停用时间
		e.UpdatedAt = now
	}
}

// SetExpired 设置eSIM为过期状态
func (e *ESIM) SetExpired() {
	if e.Status != StatusExpired {
		now := time.Now()
		e.Status = StatusExpired
		e.ExpiryTime = now
		e.UpdatedAt = now
	}
}

// UpdateUsage 更新eSIM使用情况
func (e *ESIM) UpdateUsage(dataUsed int64) {
	e.DataUsed = dataUsed
	e.UpdatedAt = time.Now()
}

// UpdateRemainingData 更新eSIM剩余流量
func (u *ESIMUsage) UpdateRemainingData(dataUsed int64) {
	u.DataUsed = dataUsed
	u.DataRemaining = u.DataTotal - dataUsed
	if u.DataRemaining < 0 {
		u.DataRemaining = 0
	}

	now := time.Now()
	u.LastUpdateTime = now
	u.NextUpdateTime = now.Add(time.Hour * 6) // 默认6小时更新一次
	u.UpdatedAt = now
}

// UpdateRemainingDays 更新eSIM剩余天数
func (u *ESIMUsage) UpdateRemainingDays() {
	now := time.Now()

	// 计算已使用天数
	startDate := u.CreatedAt
	daysUsed := int(now.Sub(startDate).Hours() / 24)

	u.DaysUsed = daysUsed
	u.DaysRemaining = u.ValidDays - daysUsed
	if u.DaysRemaining < 0 {
		u.DaysRemaining = 0
	}

	u.LastUpdateTime = now
	u.UpdatedAt = now
}

// UpdateConnectionInfo 更新连接信息
func (u *ESIMUsage) UpdateConnectionInfo(provider, country, status string) {
	u.CurrentProvider = provider
	u.CurrentCountry = country
	u.ConnectionStatus = status

	now := time.Now()
	u.LastUpdateTime = now
	u.UpdatedAt = now
}
