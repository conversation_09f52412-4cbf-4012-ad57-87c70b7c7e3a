package promotion

import (
	"time"
)

// Type 促销类型
type Type string

const (
	TypePercentage  Type = "PERCENTAGE"
	TypeFixedAmount Type = "FIXED_AMOUNT"
	TypeFreeItem    Type = "FREE_ITEM"
)

// Status 促销状态
type Status string

const (
	StatusActive   Status = "ACTIVE"
	StatusInactive Status = "INACTIVE"
	StatusExpired  Status = "EXPIRED"
)

// Promotion 促销模型
type Promotion struct {
	ID            string                 `json:"id"`
	Code          string                 `json:"code"`
	Description   string                 `json:"description"`
	Type          Type                   `json:"type"`
	Value         int64                  `json:"value"` // 百分比乘以100或固定金额
	MinOrderValue int64                  `json:"minOrderValue,omitempty"`
	MaxDiscount   int64                  `json:"maxDiscount,omitempty"`
	StartDate     time.Time              `json:"startDate"`
	EndDate       time.Time              `json:"endDate"`
	UsageLimit    int                    `json:"usageLimit,omitempty"`
	CurrentUsage  int                    `json:"currentUsage"`
	Status        Status                 `json:"status"`
	ResellerID    string                 `json:"resellerId,omitempty"` // 如果是代理商特定促销
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt     time.Time              `json:"createdAt"`
	UpdatedAt     time.Time              `json:"updatedAt"`
}

// NewPromotion 创建新促销
func NewPromotion(code, description string, promotionType Type, value int64) *Promotion {
	now := time.Now()
	return &Promotion{
		Code:        code,
		Description: description,
		Type:        promotionType,
		Value:       value,
		Status:      StatusActive,
		StartDate:   now,
		EndDate:     now.AddDate(0, 1, 0), // 默认1个月
		CreatedAt:   now,
		UpdatedAt:   now,
		Metadata:    map[string]interface{}{},
	}
}

// IsActive 检查促销是否活动
func (p *Promotion) IsActive() bool {
	now := time.Now()
	return p.Status == StatusActive &&
		now.After(p.StartDate) &&
		now.Before(p.EndDate) &&
		(p.UsageLimit == 0 || p.CurrentUsage < p.UsageLimit)
}

// CalculateDiscount 计算折扣金额
func (p *Promotion) CalculateDiscount(orderAmount int64) int64 {
	if !p.IsActive() || orderAmount < p.MinOrderValue {
		return 0
	}

	var discount int64
	switch p.Type {
	case TypePercentage:
		discount = orderAmount * p.Value / 10000 // 百分比乘以100
	case TypeFixedAmount:
		discount = p.Value
	default:
		return 0
	}

	// 应用最大折扣限制
	if p.MaxDiscount > 0 && discount > p.MaxDiscount {
		discount = p.MaxDiscount
	}

	// 确保折扣不超过订单金额
	if discount > orderAmount {
		discount = orderAmount
	}

	return discount
}

// Use 使用促销
func (p *Promotion) Use() bool {
	if !p.IsActive() {
		return false
	}

	p.CurrentUsage++
	p.UpdatedAt = time.Now()

	// 如果达到使用限制，将状态设置为过期
	if p.UsageLimit > 0 && p.CurrentUsage >= p.UsageLimit {
		p.Status = StatusExpired
	}

	return true
}
