package enterprise

import (
	"time"
)

// Department 企业部门模型
type Department struct {
	ID           string    `json:"id"`
	EnterpriseID string    `json:"enterpriseId"`
	Name         string    `json:"name"`
	Description  string    `json:"description,omitempty"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// NewDepartment 创建新部门
func NewDepartment(enterpriseID, name, description string) *Department {
	now := time.Now()
	return &Department{
		EnterpriseID: enterpriseID,
		Name:         name,
		Description:  description,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// Employee 企业员工模型
type Employee struct {
	ID           string    `json:"id"`
	UserID       string    `json:"userId"`
	EnterpriseID string    `json:"enterpriseId"`
	DepartmentID string    `json:"departmentId,omitempty"`
	Position     string    `json:"position,omitempty"`
	EmployeeCode string    `json:"employeeCode,omitempty"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// NewEmployee 创建新员工
func NewEmployee(userID, enterpriseID, departmentID, position string) *Employee {
	now := time.Now()
	return &Employee{
		UserID:       userID,
		EnterpriseID: enterpriseID,
		DepartmentID: departmentID,
		Position:     position,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// QuotaType 配额类型
type QuotaType string

const (
	QuotaTypeData  QuotaType = "DATA"  // 流量配额
	QuotaTypeValue QuotaType = "VALUE" // 金额配额
)

// EmployeeQuota 员工eSIM配额模型
type EmployeeQuota struct {
	ID           string    `json:"id"`
	EmployeeID   string    `json:"employeeId"`
	QuotaType    QuotaType `json:"quotaType"`
	QuotaValue   int64     `json:"quotaValue"`             // 根据QuotaType，可能是流量大小(MB)或金额(分)
	UsedValue    int64     `json:"usedValue"`              // 已使用的配额
	RefreshCycle string    `json:"refreshCycle,omitempty"` // MONTHLY, QUARTERLY, ANNUALLY
	StartDate    time.Time `json:"startDate"`
	EndDate      time.Time `json:"endDate,omitempty"`
	IsActive     bool      `json:"isActive"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// NewEmployeeQuota 创建新的员工eSIM配额
func NewEmployeeQuota(employeeID string, quotaType QuotaType, quotaValue int64, refreshCycle string) *EmployeeQuota {
	now := time.Now()

	// 设置结束日期，基于刷新周期
	var endDate time.Time
	switch refreshCycle {
	case "MONTHLY":
		endDate = now.AddDate(0, 1, 0)
	case "QUARTERLY":
		endDate = now.AddDate(0, 3, 0)
	case "ANNUALLY":
		endDate = now.AddDate(1, 0, 0)
	default:
		// 默认为年度
		endDate = now.AddDate(1, 0, 0)
	}

	return &EmployeeQuota{
		EmployeeID:   employeeID,
		QuotaType:    quotaType,
		QuotaValue:   quotaValue,
		UsedValue:    0,
		RefreshCycle: refreshCycle,
		StartDate:    now,
		EndDate:      endDate,
		IsActive:     true,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// HasSufficientQuota 检查是否有足够的配额
func (q *EmployeeQuota) HasSufficientQuota(requiredValue int64) bool {
	return q.IsActive && time.Now().Before(q.EndDate) && (q.QuotaValue-q.UsedValue) >= requiredValue
}

// UseQuota 使用配额
func (q *EmployeeQuota) UseQuota(value int64) bool {
	if !q.HasSufficientQuota(value) {
		return false
	}

	q.UsedValue += value
	q.UpdatedAt = time.Now()
	return true
}

// RefreshQuota 刷新配额
func (q *EmployeeQuota) RefreshQuota() {
	now := time.Now()
	q.UsedValue = 0
	q.StartDate = now

	// 更新结束日期
	switch q.RefreshCycle {
	case "MONTHLY":
		q.EndDate = now.AddDate(0, 1, 0)
	case "QUARTERLY":
		q.EndDate = now.AddDate(0, 3, 0)
	case "ANNUALLY":
		q.EndDate = now.AddDate(1, 0, 0)
	default:
		// 默认为年度
		q.EndDate = now.AddDate(1, 0, 0)
	}

	q.UpdatedAt = now
}
