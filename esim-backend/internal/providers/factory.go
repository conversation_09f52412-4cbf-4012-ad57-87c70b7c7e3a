package providers

import (
	"context"
	"fmt"
	"sync"

	"vereal/letsesim/pkg/esim"
)

// DefaultProviderFactory 默认的Provider工厂实现
type DefaultProviderFactory struct {
	providers map[string]esim.ESIMProviderInterface
	mu        sync.RWMutex
	configMgr esim.ProviderConfigManagerInterface
}

// NewProviderFactory 创建新的Provider工厂
func NewProviderFactory(configMgr esim.ProviderConfigManagerInterface) *DefaultProviderFactory {
	return &DefaultProviderFactory{
		providers: make(map[string]esim.ESIMProviderInterface),
		configMgr: configMgr,
	}
}

// GetProviders 获取所有已注册的Provider信息
func (f *DefaultProviderFactory) GetProviders(ctx context.Context) []esim.ProviderInfo {
	f.mu.RLock()
	defer f.mu.RUnlock()

	providers := make([]esim.ProviderInfo, 0, len(f.providers))
	for _, provider := range f.providers {
		providers = append(providers, provider.GetProviderInfo(ctx))
	}
	return providers
}

// GetProvider 根据类型获取特定的Provider实例
func (f *DefaultProviderFactory) GetProvider(ctx context.Context, providerType string) (esim.ESIMProviderInterface, error) {
	f.mu.RLock()
	defer f.mu.RUnlock()

	provider, exists := f.providers[providerType]
	if !exists {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Provider type '%s' not found", providerType),
			nil,
			"",
		)
	}

	// 检查Provider是否已启用
	config, err := f.configMgr.GetConfig(ctx, providerType)
	if err != nil {
		return nil, err
	}

	if !config.Enabled {
		return nil, esim.NewESIMError(
			esim.ErrOperationNotAllowed,
			fmt.Sprintf("Provider '%s' is disabled", providerType),
			nil,
			"",
		)
	}

	return provider, nil
}

// RegisterProvider 注册新的Provider实例
func (f *DefaultProviderFactory) RegisterProvider(ctx context.Context, provider esim.ESIMProviderInterface) error {
	info := provider.GetProviderInfo(ctx)
	providerType := info.Type

	f.mu.Lock()
	defer f.mu.Unlock()

	if _, exists := f.providers[providerType]; exists {
		return esim.NewESIMError(
			esim.ErrInvalidParams,
			fmt.Sprintf("Provider type '%s' already registered", providerType),
			nil,
			"",
		)
	}

	f.providers[providerType] = provider
	return nil
}

// ProviderType常量
const (
	ProviderTypeESIMAccess = "esim_access"
	ProviderTypeMayaMobile = "maya_mobile"
)
