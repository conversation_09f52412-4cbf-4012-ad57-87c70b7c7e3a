package esimaccess

import (
	"encoding/json"
	"time"
)

// APIResponse represents the standard response format from the ESIMAccess API
type APIResponse struct {
	Success bool            `json:"success"`
	Code    string          `json:"code"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// ErrorResponse represents an error response from the API
type ErrorResponse struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Message string `json:"message"`
}

// RegionsResponse represents the response for supported regions
type RegionsResponse struct {
	Regions []Region `json:"regions"`
}

// Region represents a supported region for eSIMs
type Region struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Flag        string `json:"flag,omitempty"`
}

// PackagesResponse represents the response for available packages
type PackagesResponse struct {
	Packages []Package `json:"packages"`
}

// Package represents an eSIM data package
type Package struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Type        string  `json:"type"`        // "data", "voice", etc.
	DataAmount  int64   `json:"data_amount"` // in bytes
	Duration    int     `json:"duration"`    // in days
	Price       float64 `json:"price"`
	Currency    string  `json:"currency"`
	RegionCode  string  `json:"region_code"`
}

// PackageDetails extends Package with additional details
type PackageDetails struct {
	Package
	Coverage     []string    `json:"coverage"` // List of country codes
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
	ValidFrom    *time.Time  `json:"valid_from,omitempty"`
	ValidUntil   *time.Time  `json:"valid_until,omitempty"`
	Requirements []string    `json:"requirements,omitempty"`
	Features     []string    `json:"features,omitempty"`
	Options      interface{} `json:"options,omitempty"`
}

// CreateESIMRequest represents the request to create a new eSIM
type CreateESIMRequest struct {
	PackageID   string `json:"package_id"`
	UserID      string `json:"user_id,omitempty"`
	PhoneNumber string `json:"phone_number,omitempty"`
	Email       string `json:"email,omitempty"`
	ICCID       string `json:"iccid,omitempty"`
	Metadata    string `json:"metadata,omitempty"`
}

// TopUpRequest represents the request to add a package to an existing eSIM
type TopUpRequest struct {
	PackageID string `json:"package_id"`
}

// ESIMResponse represents the response for a single eSIM
type ESIMResponse struct {
	ESIM ESIM `json:"esim"`
}

// ESIMListResponse represents the response for a list of eSIMs
type ESIMListResponse struct {
	ESIMs []ESIM `json:"esims"`
}

// ESIM represents an eSIM
type ESIM struct {
	ID             string    `json:"id"`
	ICCID          string    `json:"iccid"`
	EID            string    `json:"eid,omitempty"`
	Status         string    `json:"status"`
	ActivationURL  string    `json:"activation_url,omitempty"`
	QRCode         string    `json:"qr_code,omitempty"`
	ActivationQR   string    `json:"activation_qr,omitempty"`
	ActivationSMS  string    `json:"activation_sms,omitempty"`
	ActivationCode string    `json:"activation_code,omitempty"`
	MSISDN         string    `json:"msisdn,omitempty"` // Phone number
	IMSI           string    `json:"imsi,omitempty"`
	UserID         string    `json:"user_id,omitempty"`
	PackageID      string    `json:"package_id"`
	PackageName    string    `json:"package_name"`
	RegionCode     string    `json:"region_code"`
	RegionName     string    `json:"region_name"`
	DataTotal      int64     `json:"data_total"`     // in bytes
	DataRemaining  int64     `json:"data_remaining"` // in bytes
	DataUsed       int64     `json:"data_used"`      // in bytes
	ValidFrom      time.Time `json:"valid_from"`
	ValidUntil     time.Time `json:"valid_until"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	Metadata       string    `json:"metadata,omitempty"`
}

// UsageResponse represents the response for eSIM usage
type UsageResponse struct {
	Usage Usage `json:"usage"`
}

// Usage represents the usage statistics of an eSIM
type Usage struct {
	ESIMID        string        `json:"esim_id"`
	DataTotal     int64         `json:"data_total"`     // in bytes
	DataRemaining int64         `json:"data_remaining"` // in bytes
	DataUsed      int64         `json:"data_used"`      // in bytes
	ValidFrom     time.Time     `json:"valid_from"`
	ValidUntil    time.Time     `json:"valid_until"`
	LastUpdated   time.Time     `json:"last_updated"`
	UsageHistory  []UsageRecord `json:"usage_history,omitempty"`
}

// UsageRecord represents a single usage record
type UsageRecord struct {
	Timestamp time.Time `json:"timestamp"`
	DataUsed  int64     `json:"data_used"` // in bytes
	Location  string    `json:"location,omitempty"`
	NetworkID string    `json:"network_id,omitempty"`
}

// BalanceResponse represents the response for account balance
type BalanceResponse struct {
	Balance Balance `json:"balance"`
}

// Balance represents the account balance
type Balance struct {
	Amount      float64   `json:"amount"`
	Currency    string    `json:"currency"`
	LastUpdated time.Time `json:"last_updated"`
}

// ESIMActivatedEvent represents an eSIM activation event
type ESIMActivatedEvent struct {
	ESIMID    string    `json:"esim_id"`
	ICCID     string    `json:"iccid"`
	UserID    string    `json:"user_id,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// ESIMDataUsageEvent represents an eSIM data usage update event
type ESIMDataUsageEvent struct {
	ESIMID        string    `json:"esim_id"`
	ICCID         string    `json:"iccid"`
	DataUsed      int64     `json:"data_used"`      // in bytes
	DataRemaining int64     `json:"data_remaining"` // in bytes
	Timestamp     time.Time `json:"timestamp"`
}

// ESIMExpiryEvent represents an eSIM expiry event
type ESIMExpiryEvent struct {
	ESIMID    string    `json:"esim_id"`
	ICCID     string    `json:"iccid"`
	UserID    string    `json:"user_id,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// ESIMStatusChangeEvent represents an eSIM status change event
type ESIMStatusChangeEvent struct {
	ESIMID      string    `json:"esim_id"`
	ICCID       string    `json:"iccid"`
	UserID      string    `json:"user_id,omitempty"`
	OldStatus   string    `json:"old_status"`
	NewStatus   string    `json:"new_status"`
	Timestamp   time.Time `json:"timestamp"`
	Description string    `json:"description,omitempty"`
}

// 请求结构

// PackageQueryRequest 套餐查询请求
type PackageQueryRequest struct {
	LocationCode string `json:"locationCode,omitempty"`
	Type         string `json:"type,omitempty"`
	PackageCode  string `json:"packageCode,omitempty"`
	Slug         string `json:"slug,omitempty"`
	ICCID        string `json:"iccid,omitempty"`
}

// OrderRequest 订购请求
type OrderRequest struct {
	TransactionId   string             `json:"transactionId"`
	Amount          int64              `json:"amount,omitempty"`
	PackageInfoList []PackageOrderInfo `json:"packageInfoList"`
}

// PackageOrderInfo 套餐订单信息
type PackageOrderInfo struct {
	PackageCode string `json:"packageCode,omitempty"`
	Count       int    `json:"count"`
	Price       int64  `json:"price,omitempty"`
	PeriodNum   int    `json:"periodNum,omitempty"`
}

// ESIMQueryRequest eSIM查询请求
type ESIMQueryRequest struct {
	OrderNo   string     `json:"orderNo,omitempty"`
	ICCID     string     `json:"iccid,omitempty"`
	StartTime string     `json:"startTime,omitempty"`
	EndTime   string     `json:"endTime,omitempty"`
	Pager     PagerParam `json:"pager"`
}

// PagerParam 分页参数
type PagerParam struct {
	PageSize int `json:"pageSize"`
	PageNum  int `json:"pageNum"`
}

// ESIMOpRequest eSIM操作请求(取消/暂停/恢复/撤销)
type ESIMOpRequest struct {
	ICCID      string `json:"iccid,omitempty"`
	ESIMTranNo string `json:"esimTranNo,omitempty"`
}

// SMSRequest 发送短信请求
type SMSRequest struct {
	ICCID      string `json:"iccid,omitempty"`
	ESIMTranNo string `json:"esimTranNo,omitempty"`
	Message    string `json:"message"`
}

// UsageQueryRequest 用量查询请求
type UsageQueryRequest struct {
	ESIMTranNoList []string `json:"esimTranNoList"`
}

// WebhookRequest webhook设置请求
type WebhookRequest struct {
	Webhook string `json:"webhook"`
}

// 响应结构

// PackageQueryResponse 套餐查询响应
type PackageQueryResponse struct {
	PackageList []PackageInfo `json:"packageList"`
}

// PackageInfo 套餐信息
type PackageInfo struct {
	PackageCode         string            `json:"packageCode"`
	Slug                string            `json:"slug"`
	Name                string            `json:"name"`
	Price               int64             `json:"price"`
	CurrencyCode        string            `json:"currencyCode"`
	Volume              int64             `json:"volume"`
	SMSStatus           int               `json:"smsStatus"`
	DataType            int               `json:"dataType"`
	UnusedValidTime     int               `json:"unusedValidTime"`
	Duration            int               `json:"duration"`
	DurationUnit        string            `json:"durationUnit"`
	Location            string            `json:"location"`
	Description         string            `json:"description"`
	ActiveType          int               `json:"activeType"`
	Favorite            bool              `json:"favorite"`
	RetailPrice         int64             `json:"retailPrice"`
	Speed               string            `json:"speed"`
	LocationNetworkList []LocationNetwork `json:"locationNetworkList"`
	SupportTopUpType    int               `json:"supportTopUpType"`
}

// LocationNetwork 位置网络信息
type LocationNetwork struct {
	LocationName string         `json:"locationName"`
	LocationLogo string         `json:"locationLogo"`
	OperatorList []OperatorInfo `json:"operatorList"`
}

// OperatorInfo 运营商信息
type OperatorInfo struct {
	OperatorName string `json:"operatorName"`
	NetworkType  string `json:"networkType"`
}

// OrderResponse 订单响应
type OrderResponse struct {
	OrderNo       string `json:"orderNo"`
	TransactionId string `json:"transactionId"`
}

// ESIMInfo eSIM信息
type ESIMInfo struct {
	ICCID         string        `json:"iccid"`
	ESIMTranNo    string        `json:"esimTranNo"`
	OrderNo       string        `json:"orderNo"`
	ESIMStatus    string        `json:"esimStatus"`
	SMDPStatus    string        `json:"smdpStatus"`
	AC            string        `json:"ac"` // ActivationCode
	QRCodeURL     string        `json:"qrCodeUrl"`
	TotalVolume   int64         `json:"totalVolume"`
	OrderUsage    int64         `json:"orderUsage"`
	TotalDuration int           `json:"totalDuration"`
	ExpiredTime   string        `json:"expiredTime"`
	ActiveType    int           `json:"activeType"`
	EID           string        `json:"eid"`
	PackageList   []PackageInfo `json:"packageList"`
	PIN           string        `json:"pin"`
	PUK           string        `json:"puk"`
	APN           string        `json:"apn"`
	SMSStatus     int           `json:"smsStatus"`
	MSISDN        string        `json:"msisdn"`
	IMSI          string        `json:"imsi"`
	ShortURL      string        `json:"shortUrl"`
	DurationUnit  string        `json:"durationUnit"`
	ActivateTime  string        `json:"activateTime"`
	TransactionId string        `json:"transactionId"`
}

// ESIMQueryResponse eSIM查询响应
type ESIMQueryResponse struct {
	ESIMList []ESIMInfo `json:"esimList"`
	Pager    PagerInfo  `json:"pager"`
}

// PagerInfo 分页信息
type PagerInfo struct {
	Total    int64 `json:"total"`
	PageSize int   `json:"pageSize"`
	PageNum  int   `json:"pageNum"`
}

// TopUpResponse 充值响应
type TopUpResponse struct {
	TransactionId string `json:"transactionId"`
	ExpiredTime   string `json:"expiredTime"`
	TotalVolume   int64  `json:"totalVolume"`
	OrderUsage    int64  `json:"orderUsage"`
}

// ESIMUsageInfo eSIM用量信息
type ESIMUsageInfo struct {
	DataUsage      int64  `json:"dataUsage"`
	TotalData      int64  `json:"totalData"`
	LastUpdateTime string `json:"lastUpdateTime"`
}

// RegionResponse 区域响应
type RegionResponse struct {
	LocationList []LocationInfo `json:"locationList"`
}

// LocationInfo 位置信息
type LocationInfo struct {
	Code            string            `json:"code"`
	Name            string            `json:"name"`
	Type            int               `json:"type"`
	SubLocationList []SubLocationInfo `json:"subLocationList,omitempty"`
}

// SubLocationInfo 子位置信息
type SubLocationInfo struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// WebhookEvent Webhook事件
type WebhookEvent struct {
	NotifyType string                 `json:"notifyType"`
	Content    map[string]interface{} `json:"content"`
}
