package esimaccess

import (
	"strings"
	"time"

	"vereal/letsesim/pkg/esim"
)

// ProviderTypeESIMAccess is the provider type for ESIMAccess
const ProviderTypeESIMAccess = "esim_access"

// ConvertToPackage 将eSIMAccess包信息转换为统一模型
func ConvertToPackage(info PackageInfo) esim.Package {
	locations := []string{}
	if info.Location != "" {
		locations = strings.Split(info.Location, ",")
	}

	networkTypes := []string{}
	if info.Speed != "" {
		networkTypes = strings.Split(info.Speed, "/")
	}

	return esim.Package{
		ID:            info.PackageCode,
		Name:          info.Name,
		Description:   info.Description,
		Price:         info.Price,
		Currency:      info.CurrencyCode,
		DataVolume:    info.Volume,
		ValidityDays:  info.Duration,
		LocationCodes: locations,
		SupportsSMS:   info.SMSStatus > 0,
		DataType:      getDataTypeString(info.DataType),
		NetworkTypes:  networkTypes,
		SupportTopUp:  info.SupportTopUpType == 2,
		ProviderSpecific: map[string]interface{}{
			"slug":            info.Slug,
			"unusedValidTime": info.UnusedValidTime,
			"durationUnit":    info.DurationUnit,
			"activeType":      info.ActiveType,
			"retailPrice":     info.RetailPrice,
		},
	}
}

// ConvertToESIM 将eSIMAccess eSIM信息转换为统一模型
func ConvertToESIM(info ESIMInfo) esim.ESIM {
	// 解析时间
	expiryTime, _ := time.Parse(time.RFC3339, info.ExpiredTime)

	// 转换状态
	status := esim.ESIMStatus{
		Code:            info.ESIMStatus,
		SMDPStatus:      info.SMDPStatus,
		InstalledStatus: getInstalledStatus(info),
		Description:     getStatusDescription(info.ESIMStatus),
	}

	// 转换套餐
	packages := []esim.Package{}
	for _, p := range info.PackageList {
		packages = append(packages, ConvertToPackage(p))
	}

	// 转换SMS功能
	smsCapability := esim.SMSCapability{
		Supported: info.SMSStatus > 0,
		MSISDN:    info.MSISDN,
		APIOnly:   info.SMSStatus == 2,
	}

	return esim.ESIM{
		Identifier: esim.ESIMIdentifier{
			ICCID:      info.ICCID,
			ESIMTranNo: info.ESIMTranNo,
			OrderNo:    info.OrderNo,
		},
		Status:         status,
		ActivationCode: info.AC,
		QRCodeURL:      info.QRCodeURL,
		DataVolume:     info.TotalVolume,
		UsedData:       info.OrderUsage,
		ValidityDays:   info.TotalDuration,
		ExpiryTime:     expiryTime,
		ActiveType:     info.ActiveType,
		EID:            info.EID,
		Packages:       packages,
		PIN:            info.PIN,
		PUK:            info.PUK,
		APN:            info.APN,
		SMS:            smsCapability,
		ProviderSpecific: map[string]interface{}{
			"imsi":          info.IMSI,
			"shortUrl":      info.ShortURL,
			"durationUnit":  info.DurationUnit,
			"activateTime":  info.ActivateTime,
			"transactionId": info.TransactionId,
		},
	}
}

// ConvertToESIMListResult 将eSIMAccess查询结果转换为统一结果模型
func ConvertToESIMListResult(response ESIMQueryResponse) *esim.ESIMListResult {
	esims := []esim.ESIM{}
	for _, info := range response.ESIMList {
		esims = append(esims, ConvertToESIM(info))
	}

	return &esim.ESIMListResult{
		ESIMs: esims,
		Pagination: esim.PaginationResult{
			Total:    response.Pager.Total,
			PageSize: response.Pager.PageSize,
			PageNum:  response.Pager.PageNum,
		},
	}
}

// ConvertToTopUpResult 将eSIMAccess充值结果转换为统一结果模型
func ConvertToTopUpResult(response TopUpResponse) *esim.TopUpResult {
	expiryTime, _ := time.Parse(time.RFC3339, response.ExpiredTime)

	return &esim.TopUpResult{
		TransactionID: response.TransactionId,
		ExpiryTime:    expiryTime,
		TotalVolume:   response.TotalVolume,
		OrderUsage:    response.OrderUsage,
	}
}

// ConvertToAccountBalance 将eSIMAccess余额响应转换为统一模型
func ConvertToAccountBalance(response BalanceResponse) *esim.AccountBalance {
	return &esim.AccountBalance{
		Balance:        int64(response.Balance.Amount),
		Currency:       response.Balance.Currency,
		HasOverdraft:   false,
		OverdraftLimit: 0,
	}
}

// ConvertToRegions 将eSIMAccess区域响应转换为统一模型
func ConvertToRegions(response RegionResponse) []esim.Region {
	regions := []esim.Region{}

	for _, regionInfo := range response.LocationList {
		subLocations := []esim.SubLocation{}

		if regionInfo.SubLocationList != nil {
			for _, subInfo := range regionInfo.SubLocationList {
				subLocations = append(subLocations, esim.SubLocation{
					Code: subInfo.Code,
					Name: subInfo.Name,
				})
			}
		}

		regions = append(regions, esim.Region{
			Code:         regionInfo.Code,
			Name:         regionInfo.Name,
			Type:         regionInfo.Type,
			SubLocations: subLocations,
		})
	}

	return regions
}

// ConvertToESIMUsage 将eSIMAccess用量信息转换为统一模型
func ConvertToESIMUsage(info ESIMUsageInfo) *esim.ESIMUsage {
	lastUpdateTime, _ := time.Parse(time.RFC3339, info.LastUpdateTime)

	return &esim.ESIMUsage{
		DataUsage:      info.DataUsage,
		TotalData:      info.TotalData,
		LastUpdateTime: lastUpdateTime,
	}
}

// ConvertToWebhookEvent 将eSIMAccess Webhook事件转换为统一模型
func ConvertToWebhookEvent(event WebhookEvent) *esim.WebhookEvent {
	esimIdentifier := esim.ESIMIdentifier{}

	// 从content中提取eSIM标识
	if orderNo, ok := event.Content["orderNo"].(string); ok {
		esimIdentifier.OrderNo = orderNo
	}

	if iccid, ok := event.Content["iccid"].(string); ok {
		esimIdentifier.ICCID = iccid
	}

	if esimTranNo, ok := event.Content["esimTranNo"].(string); ok {
		esimIdentifier.ESIMTranNo = esimTranNo
	}

	return &esim.WebhookEvent{
		EventType:      event.NotifyType,
		ProviderType:   ProviderTypeESIMAccess,
		Timestamp:      time.Now(),
		Payload:        event.Content,
		ESIMIdentifier: esimIdentifier,
		Data:           event.Content,
	}
}

// 辅助函数

// getDataTypeString 获取数据类型字符串
func getDataTypeString(dataType int) string {
	switch dataType {
	case 1:
		return "FIXED"
	case 2:
		return "UNLIMITED_DAILY"
	default:
		return "UNKNOWN"
	}
}

// getInstalledStatus 获取安装状态
func getInstalledStatus(info ESIMInfo) string {
	if info.EID != "" {
		return "INSTALLED"
	}
	return "NOT_INSTALLED"
}

// getStatusDescription 获取状态描述
func getStatusDescription(status string) string {
	statusMap := map[string]string{
		"CREATE":           "Created",
		"PAYING":           "Paying",
		"PAID":             "Paid",
		"GETTING_RESOURCE": "Getting Resource",
		"GOT_RESOURCE":     "Ready to Download",
		"IN_USE":           "In Use",
		"USED_UP":          "Data Used Up",
		"UNUSED_EXPIRED":   "Expired Before Use",
		"USED_EXPIRED":     "Expired After Use",
		"CANCEL":           "Cancelled",
		"SUSPENDED":        "Suspended",
		"REVOKE":           "Revoked",
	}

	if desc, ok := statusMap[status]; ok {
		return desc
	}
	return "Unknown Status"
}
