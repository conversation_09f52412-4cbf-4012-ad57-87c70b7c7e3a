package esimaccess

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

// APIClientInterface is the interface for ESIMAccess API client
type APIClientInterface interface {
	ListPackages(ctx context.Context, regionCode string) ([]Package, error)
	GetPackageDetails(ctx context.Context, packageID string) (*PackageDetails, error)
	CreateESIM(ctx context.Context, req CreateESIMRequest) (*ESIM, error)
	GetESIMDetails(ctx context.Context, esimID string) (*ESIM, error)
	ListESIMs(ctx context.Context, userID string) ([]ESIM, error)
	CancelESIM(ctx context.Context, esimID string) error
	SuspendESIM(ctx context.Context, esimID string) error
	ResumeESIM(ctx context.Context, esimID string) error
	TopUpESIM(ctx context.Context, esimID string, req TopUpRequest) (*ESIM, error)
	GetESIMUsage(ctx context.Context, esimID string) (*Usage, error)
	GetAccountBalance(ctx context.Context) (*Balance, error)
	GetSupportedRegions(ctx context.Context) ([]Region, error)
	ProcessWebhook(ctx context.Context, payload []byte, headers map[string][]string) (*WebhookEvent, error)
}

// APIClient is the client for ESIMAccess API
type APIClient struct {
	baseURL    string
	apiKey     string
	apiSecret  string
	httpClient *http.Client
}

// NewAPIClient creates a new ESIMAccess API client
func NewAPIClient(baseURL, apiKey, apiSecret string) *APIClient {
	return &APIClient{
		baseURL:    baseURL,
		apiKey:     apiKey,
		apiSecret:  apiSecret,
		httpClient: &http.Client{Timeout: 30 * time.Second},
	}
}

// calculateSignature calculates the HMAC-SHA256 signature for the request
func (c *APIClient) calculateSignature(path string, timestamp int64, payload []byte) string {
	// 1. Concatenate path, timestamp, and payload
	data := fmt.Sprintf("%s%d%s", path, timestamp, string(payload))

	// 2. Calculate HMAC-SHA256 using API secret
	h := hmac.New(sha256.New, []byte(c.apiSecret))
	h.Write([]byte(data))

	// 3. Return hex-encoded signature
	return hex.EncodeToString(h.Sum(nil))
}

// sendRequest sends an API request to ESIMAccess
func (c *APIClient) sendRequest(ctx context.Context, method, path string, payload interface{}, result interface{}) error {
	// 1. Create the URL
	reqURL, err := url.JoinPath(c.baseURL, path)
	if err != nil {
		return fmt.Errorf("failed to build URL: %w", err)
	}

	// 2. Marshal payload if provided
	var body []byte
	if payload != nil {
		body, err = json.Marshal(payload)
		if err != nil {
			return fmt.Errorf("failed to marshal payload: %w", err)
		}
	}

	// 3. Create the request
	req, err := http.NewRequestWithContext(ctx, method, reqURL, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 4. Set headers
	timestamp := time.Now().Unix()
	signature := c.calculateSignature(path, timestamp, body)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-Key", c.apiKey)
	req.Header.Set("X-Timestamp", strconv.FormatInt(timestamp, 10))
	req.Header.Set("X-Signature", signature)

	// 5. Send the request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 6. Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// 7. Check if status code is not successful
	if resp.StatusCode >= 400 {
		var errResp ErrorResponse
		if err := json.Unmarshal(respBody, &errResp); err != nil {
			return fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(respBody))
		}
		return fmt.Errorf("API error: %s - %s", errResp.Code, errResp.Message)
	}

	// 8. Parse the response
	var apiResp APIResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return fmt.Errorf("failed to parse API response: %w", err)
	}

	if !apiResp.Success {
		return fmt.Errorf("API error: %s - %s", apiResp.Code, apiResp.Message)
	}

	// 9. Unmarshal the data into the result
	if result != nil && len(apiResp.Data) > 0 {
		if err := json.Unmarshal(apiResp.Data, result); err != nil {
			return fmt.Errorf("failed to unmarshal response data: %w", err)
		}
	}

	return nil
}

// ListPackages returns a list of available packages
func (c *APIClient) ListPackages(ctx context.Context, regionCode string) ([]Package, error) {
	path := "/api/v1/packages"
	if regionCode != "" {
		path = fmt.Sprintf("%s?region=%s", path, regionCode)
	}

	var result PackagesResponse
	err := c.sendRequest(ctx, http.MethodGet, path, nil, &result)
	if err != nil {
		return nil, err
	}

	return result.Packages, nil
}

// GetPackageDetails returns the details of a specific package
func (c *APIClient) GetPackageDetails(ctx context.Context, packageID string) (*PackageDetails, error) {
	path := fmt.Sprintf("/api/v1/packages/%s", packageID)

	var result PackageDetails
	err := c.sendRequest(ctx, http.MethodGet, path, nil, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// CreateESIM creates a new eSIM with the specified package
func (c *APIClient) CreateESIM(ctx context.Context, req CreateESIMRequest) (*ESIM, error) {
	path := "/api/v1/esims"

	var result ESIMResponse
	err := c.sendRequest(ctx, http.MethodPost, path, req, &result)
	if err != nil {
		return nil, err
	}

	return &result.ESIM, nil
}

// GetESIMDetails returns the details of a specific eSIM
func (c *APIClient) GetESIMDetails(ctx context.Context, esimID string) (*ESIM, error) {
	path := fmt.Sprintf("/api/v1/esims/%s", esimID)

	var result ESIMResponse
	err := c.sendRequest(ctx, http.MethodGet, path, nil, &result)
	if err != nil {
		return nil, err
	}

	return &result.ESIM, nil
}

// ListESIMs returns a list of eSIMs for the specified user
func (c *APIClient) ListESIMs(ctx context.Context, userID string) ([]ESIM, error) {
	path := fmt.Sprintf("/api/v1/users/%s/esims", userID)

	var result ESIMListResponse
	err := c.sendRequest(ctx, http.MethodGet, path, nil, &result)
	if err != nil {
		return nil, err
	}

	return result.ESIMs, nil
}

// CancelESIM cancels an eSIM
func (c *APIClient) CancelESIM(ctx context.Context, esimID string) error {
	path := fmt.Sprintf("/api/v1/esims/%s/cancel", esimID)
	return c.sendRequest(ctx, http.MethodPost, path, nil, nil)
}

// SuspendESIM suspends an eSIM
func (c *APIClient) SuspendESIM(ctx context.Context, esimID string) error {
	path := fmt.Sprintf("/api/v1/esims/%s/suspend", esimID)
	return c.sendRequest(ctx, http.MethodPost, path, nil, nil)
}

// ResumeESIM resumes a suspended eSIM
func (c *APIClient) ResumeESIM(ctx context.Context, esimID string) error {
	path := fmt.Sprintf("/api/v1/esims/%s/resume", esimID)
	return c.sendRequest(ctx, http.MethodPost, path, nil, nil)
}

// TopUpESIM adds a package to an existing eSIM
func (c *APIClient) TopUpESIM(ctx context.Context, esimID string, req TopUpRequest) (*ESIM, error) {
	path := fmt.Sprintf("/api/v1/esims/%s/topup", esimID)

	var result ESIMResponse
	err := c.sendRequest(ctx, http.MethodPost, path, req, &result)
	if err != nil {
		return nil, err
	}

	return &result.ESIM, nil
}

// GetESIMUsage returns the usage details for a specific eSIM
func (c *APIClient) GetESIMUsage(ctx context.Context, esimID string) (*Usage, error) {
	path := fmt.Sprintf("/api/v1/esims/%s/usage", esimID)

	var result UsageResponse
	err := c.sendRequest(ctx, http.MethodGet, path, nil, &result)
	if err != nil {
		return nil, err
	}

	return &result.Usage, nil
}

// GetAccountBalance returns the current account balance
func (c *APIClient) GetAccountBalance(ctx context.Context) (*Balance, error) {
	path := "/api/v1/account/balance"

	var result BalanceResponse
	err := c.sendRequest(ctx, http.MethodGet, path, nil, &result)
	if err != nil {
		return nil, err
	}

	return &result.Balance, nil
}

// GetSupportedRegions returns a list of supported regions
func (c *APIClient) GetSupportedRegions(ctx context.Context) ([]Region, error) {
	path := "/api/v1/regions"

	var result RegionsResponse
	err := c.sendRequest(ctx, http.MethodGet, path, nil, &result)
	if err != nil {
		return nil, err
	}

	return result.Regions, nil
}

// ProcessWebhook processes a webhook event from ESIMAccess
func (c *APIClient) ProcessWebhook(ctx context.Context, payload []byte, headers map[string][]string) (*WebhookEvent, error) {
	var event WebhookEvent
	if err := json.Unmarshal(payload, &event); err != nil {
		return nil, fmt.Errorf("failed to parse webhook payload: %w", err)
	}

	// 验证签名(如果需要)
	if _, ok := headers["X-ESIMAccess-Signature"]; ok {
		// TODO: 实现签名验证逻辑
	}

	return &event, nil
}
