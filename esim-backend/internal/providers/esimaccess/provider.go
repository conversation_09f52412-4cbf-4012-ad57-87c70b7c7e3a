package esimaccess

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"vereal/letsesim/pkg/esim"

	"go.uber.org/zap"
)

const (
	// API endpoints
	endpointPackages         = "/api/v1/packages"
	endpointPackageDetails   = "/api/v1/packages/%s"
	endpointESIMs            = "/api/v1/esims"
	endpointESIMDetails      = "/api/v1/esims/%s"
	endpointUserESIMs        = "/api/v1/users/%s/esims"
	endpointESIMCancel       = "/api/v1/esims/%s/cancel"
	endpointESIMSuspend      = "/api/v1/esims/%s/suspend"
	endpointESIMResume       = "/api/v1/esims/%s/resume"
	endpointESIMTopUp        = "/api/v1/esims/%s/topup"
	endpointESIMUsage        = "/api/v1/esims/%s/usage"
	endpointAccountBalance   = "/api/v1/account/balance"
	endpointSupportedRegions = "/api/v1/regions"
)

// ESIMAccessProvider 实现ESIMProvider接口
type ESIMAccessProvider struct {
	client   APIClientInterface
	baseURL  string
	apiKey   string
	apiToken string
	enabled  bool
	logger   *zap.Logger
}

// NewESIMAccessProvider 创建新的ESIMAccess提供商实例
func NewESIMAccessProvider(configManager esim.ProviderConfigManagerInterface, logger *zap.Logger) (esim.ESIMProviderInterface, error) {
	// 获取提供商配置
	ctx := context.Background()
	config, err := configManager.GetConfig(ctx, "esim_access")
	if err != nil {
		if logger != nil {
			logger.Error("Failed to get eSIM Access provider config",
				zap.Error(err),
				zap.String("provider", "esim_access"))
		}
		return nil, err
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建API客户端
	client := NewAPIClient(
		config.BaseURL,
		config.APIKey,
		config.APISecret,
	)

	// 设置HTTP客户端
	client.httpClient = httpClient

	return &ESIMAccessProvider{
		client:   client,
		baseURL:  config.BaseURL,
		apiKey:   config.APIKey,
		apiToken: config.APISecret,
		enabled:  config.Enabled,
		logger:   logger,
	}, nil
}

// GetProviderInfo 返回提供商信息
func (p *ESIMAccessProvider) GetProviderInfo(ctx context.Context) esim.ProviderInfo {
	// 获取支持的区域(可能会失败，如果失败不影响返回)
	var regions []string
	regionsList, err := p.client.GetSupportedRegions(ctx)
	if err == nil && len(regionsList) > 0 {
		regions = make([]string, len(regionsList))
		for i, region := range regionsList {
			regions[i] = region.Code
		}
	}

	// 构建能力映射
	capabilities := map[string]bool{
		esim.CapTopUp:   true,
		esim.CapCancel:  true,
		esim.CapSuspend: true,
		esim.CapResume:  true,
		esim.CapSMS:     false,
		esim.CapWebhook: true,
	}

	return esim.ProviderInfo{
		Type:         ProviderTypeESIMAccess,
		Name:         "eSIM Access",
		BaseURL:      p.baseURL,
		Capabilities: capabilities,
	}
}

// ListPackages 获取套餐列表
func (p *ESIMAccessProvider) ListPackages(ctx context.Context, params esim.PackageQueryParams) ([]esim.Package, *esim.PaginationResult, error) {
	// 调用API获取套餐列表
	response, err := p.client.ListPackages(ctx, params.LocationCode)
	if err != nil {
		return nil, nil, err
	}

	// 转换为统一格式
	packages := make([]esim.Package, 0, len(response))
	for _, pkg := range response {
		packages = append(packages, esim.Package{
			ID:            pkg.ID,
			Name:          pkg.Name,
			Description:   pkg.Description,
			Price:         int64(pkg.Price), // 转换float64到int64
			Currency:      pkg.Currency,
			DataVolume:    pkg.DataAmount,
			ValidityDays:  pkg.Duration,
			LocationCodes: []string{pkg.RegionCode},
		})
	}

	// 分页结果
	pagination := &esim.PaginationResult{
		Total:    int64(len(packages)),
		PageSize: len(packages),
		PageNum:  1,
	}

	return packages, pagination, nil
}

// GetPackageDetails 获取套餐详情
func (p *ESIMAccessProvider) GetPackageDetails(ctx context.Context, packageID string) (*esim.Package, error) {
	// 调用API获取套餐详情
	response, err := p.client.GetPackageDetails(ctx, packageID)
	if err != nil {
		return nil, err
	}

	// 转换为统一格式
	pkg := &esim.Package{
		ID:            response.ID,
		Name:          response.Name,
		Description:   response.Description,
		Price:         int64(response.Price), // 转换float64到int64
		Currency:      response.Currency,
		DataVolume:    response.DataAmount,
		ValidityDays:  response.Duration,
		LocationCodes: []string{response.RegionCode},
	}

	return pkg, nil
}

// CreateESIM 创建新的eSIM
func (p *ESIMAccessProvider) CreateESIM(ctx context.Context, order *esim.ESIMOrder) (*esim.ESIMOrderResult, error) {
	// 创建API请求
	request := CreateESIMRequest{
		PackageID: order.PackageID,
		// 如果需要用户ID，可以从ProviderSpecific中获取
	}

	// 从ProviderSpecific获取额外参数
	if order.ProviderSpecific != nil {
		if userID, ok := order.ProviderSpecific["user_id"].(string); ok {
			request.UserID = userID
		}
	}

	if p.logger != nil {
		p.logger.Info("Creating eSIM with eSIM Access",
			zap.String("packageID", order.PackageID),
			zap.String("transactionID", order.TransactionID),
			zap.Int("count", order.Count))
	}

	// 调用API创建eSIM
	response, err := p.client.CreateESIM(ctx, request)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to create eSIM with eSIM Access",
				zap.Error(err),
				zap.String("packageID", order.PackageID),
				zap.String("transactionID", order.TransactionID))
		}
		return nil, err
	}

	// 转换为统一格式
	esimResult := &esim.ESIMOrderResult{
		OrderNo:       response.ID,
		TransactionID: order.TransactionID,
		ESIMs: []esim.ESIM{
			{
				Identifier: esim.ESIMIdentifier{
					ICCID: response.ICCID,
				},
				Status: esim.ESIMStatus{
					Code:        convertStatus(response.Status),
					Description: "eSIM created successfully",
					SMDPStatus:  response.Status,
				},
				ActivationCode: response.ActivationCode,
				QRCodeURL:      response.ActivationQR, // 使用正确的字段
				DataVolume:     response.DataTotal,
				UsedData:       response.DataUsed,
				ValidityDays:   calculateDays(response.ValidFrom, response.ValidUntil),
				ExpiryTime:     response.ValidUntil,
			},
		},
	}

	if p.logger != nil {
		p.logger.Info("Successfully created eSIM with eSIM Access",
			zap.String("orderNo", esimResult.OrderNo),
			zap.String("transactionID", order.TransactionID),
			zap.String("iccid", response.ICCID),
			zap.String("status", response.Status))
	}

	return esimResult, nil
}

// GetESIMDetails 获取eSIM详情
func (p *ESIMAccessProvider) GetESIMDetails(ctx context.Context, esimID esim.ESIMIdentifier) (*esim.ESIM, error) {
	// 检查标识符
	if esimID.ICCID == "" && esimID.ESIMTranNo == "" {
		return nil, fmt.Errorf("missing ICCID or transaction number")
	}

	// 确定使用哪个标识符
	identifier := esimID.ICCID
	if identifier == "" {
		identifier = esimID.ESIMTranNo
	}

	// 调用API获取eSIM详情
	response, err := p.client.GetESIMDetails(ctx, identifier)
	if err != nil {
		return nil, err
	}

	// 转换为统一格式
	esimDetails := &esim.ESIM{
		Identifier: esim.ESIMIdentifier{
			ICCID: response.ICCID,
		},
		Status: esim.ESIMStatus{
			Code:        convertStatus(response.Status),
			Description: "eSIM details",
			SMDPStatus:  response.Status,
		},
		ActivationCode: response.ActivationCode,
		QRCodeURL:      response.ActivationQR,
		DataVolume:     response.DataTotal,
		UsedData:       response.DataUsed,
		ValidityDays:   calculateDays(response.ValidFrom, response.ValidUntil),
		ExpiryTime:     response.ValidUntil,
	}

	return esimDetails, nil
}

// ListESIMs 获取eSIM列表
func (p *ESIMAccessProvider) ListESIMs(ctx context.Context, params esim.ESIMQueryParams) (*esim.ESIMListResult, error) {
	// 调用API获取eSIM列表
	response, err := p.client.ListESIMs(ctx, params.CustomerID)
	if err != nil {
		return nil, err
	}

	// 转换为统一格式
	esims := make([]esim.ESIM, 0, len(response))
	for _, apiEsim := range response {
		esims = append(esims, esim.ESIM{
			Identifier: esim.ESIMIdentifier{
				ICCID: apiEsim.ICCID,
			},
			Status: esim.ESIMStatus{
				Code:       convertStatus(apiEsim.Status),
				SMDPStatus: apiEsim.Status,
			},
			ActivationCode: apiEsim.ActivationCode,
			QRCodeURL:      apiEsim.ActivationQR,
			DataVolume:     apiEsim.DataTotal,
			UsedData:       apiEsim.DataUsed,
			ValidityDays:   calculateDays(apiEsim.ValidFrom, apiEsim.ValidUntil),
			ExpiryTime:     apiEsim.ValidUntil,
		})
	}

	result := &esim.ESIMListResult{
		ESIMs: esims,
		Pagination: esim.PaginationResult{
			Total:    int64(len(esims)),
			PageSize: len(esims),
			PageNum:  1,
		},
	}

	return result, nil
}

// CancelESIM 取消eSIM
func (p *ESIMAccessProvider) CancelESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	// 检查标识符
	if esimID.ICCID == "" && esimID.ESIMTranNo == "" {
		return fmt.Errorf("missing ICCID or transaction number")
	}

	// 确定使用哪个标识符
	identifier := esimID.ICCID
	if identifier == "" {
		identifier = esimID.ESIMTranNo
	}

	// 调用API取消eSIM
	return p.client.CancelESIM(ctx, identifier)
}

// SuspendESIM 暂停eSIM
func (p *ESIMAccessProvider) SuspendESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	// 检查标识符
	if esimID.ICCID == "" && esimID.ESIMTranNo == "" {
		return fmt.Errorf("missing ICCID or transaction number")
	}

	// 确定使用哪个标识符
	identifier := esimID.ICCID
	if identifier == "" {
		identifier = esimID.ESIMTranNo
	}

	// 调用API暂停eSIM
	return p.client.SuspendESIM(ctx, identifier)
}

// ResumeESIM 恢复eSIM
func (p *ESIMAccessProvider) ResumeESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	// 检查标识符
	if esimID.ICCID == "" && esimID.ESIMTranNo == "" {
		return fmt.Errorf("missing ICCID or transaction number")
	}

	// 确定使用哪个标识符
	identifier := esimID.ICCID
	if identifier == "" {
		identifier = esimID.ESIMTranNo
	}

	// 调用API恢复eSIM
	return p.client.ResumeESIM(ctx, identifier)
}

// RevokeESIM 撤销eSIM
func (p *ESIMAccessProvider) RevokeESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	// ESIMAccess可能不支持撤销操作，这里使用取消操作代替
	return p.CancelESIM(ctx, esimID)
}

// TopUpESIM 充值eSIM
func (p *ESIMAccessProvider) TopUpESIM(ctx context.Context, params esim.TopUpParams) (*esim.TopUpResult, error) {
	// 检查标识符
	if params.ESIMID.ICCID == "" && params.ESIMID.ESIMTranNo == "" {
		return nil, fmt.Errorf("missing ICCID or transaction number")
	}

	// 确定使用哪个标识符
	identifier := params.ESIMID.ICCID
	if identifier == "" {
		identifier = params.ESIMID.ESIMTranNo
	}

	// 创建API请求
	request := TopUpRequest{
		PackageID: params.PackageID,
	}

	// 调用API充值eSIM
	response, err := p.client.TopUpESIM(ctx, identifier, request)
	if err != nil {
		return nil, err
	}

	// 转换为统一格式
	result := &esim.TopUpResult{
		TransactionID: params.TransactionID,
		ExpiryTime:    response.ValidUntil,
		TotalVolume:   response.DataTotal,
		OrderUsage:    response.DataUsed,
	}

	return result, nil
}

// SendSMS 发送短信(ESIMAccess不支持)
func (p *ESIMAccessProvider) SendSMS(ctx context.Context, params esim.SMSParams) error {
	return esim.NewESIMError(
		esim.ErrNotImplemented,
		"SMS service not supported by ESIMAccess",
		nil,
		"",
	)
}

// GetESIMUsage 获取eSIM使用情况
func (p *ESIMAccessProvider) GetESIMUsage(ctx context.Context, esimID esim.ESIMIdentifier) (*esim.ESIMUsage, error) {
	// 检查标识符
	if esimID.ICCID == "" && esimID.ESIMTranNo == "" {
		if p.logger != nil {
			p.logger.Error("Missing ICCID or transaction number for eSIM Access usage query",
				zap.Any("esimID", esimID))
		}
		return nil, fmt.Errorf("missing ICCID or transaction number for eSIM Access usage query")
	}

	// 确定使用哪个标识符
	identifier := esimID.ICCID
	if identifier == "" {
		identifier = esimID.ESIMTranNo
	}

	if p.logger != nil {
		p.logger.Debug("Querying eSIM usage from eSIM Access",
			zap.String("identifier", identifier))
	}

	// 调用API获取eSIM详情
	response, err := p.client.GetESIMUsage(ctx, identifier)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to get eSIM usage from eSIM Access",
				zap.Error(err),
				zap.String("identifier", identifier))
		}
		return nil, err
	}

	// 转换为统一格式
	usage := &esim.ESIMUsage{
		DataUsage:      response.DataUsed,
		TotalData:      response.DataTotal,
		LastUpdateTime: time.Now(),
	}

	if p.logger != nil {
		p.logger.Debug("Successfully retrieved eSIM usage from eSIM Access",
			zap.String("identifier", identifier),
			zap.Int64("dataUsage", usage.DataUsage),
			zap.Int64("totalData", usage.TotalData))
	}

	return usage, nil
}

// GetAccountBalance 获取账户余额
func (p *ESIMAccessProvider) GetAccountBalance(ctx context.Context) (*esim.AccountBalance, error) {
	if p.logger != nil {
		p.logger.Debug("Querying account balance from eSIM Access")
	}

	response, err := p.client.GetAccountBalance(ctx)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to get account balance from eSIM Access",
				zap.Error(err))
		}
		return nil, err
	}

	balance := &esim.AccountBalance{
		Balance:  int64(response.Amount * 100), // 转换为分
		Currency: response.Currency,
	}

	if p.logger != nil {
		p.logger.Debug("Successfully retrieved account balance from eSIM Access",
			zap.Int64("balance", balance.Balance),
			zap.String("currency", balance.Currency))
	}

	return balance, nil
}

// ListSupportedRegions 获取支持的区域
func (p *ESIMAccessProvider) ListSupportedRegions(ctx context.Context) ([]esim.Region, error) {
	// 调用API获取支持的区域
	response, err := p.client.GetSupportedRegions(ctx)
	if err != nil {
		return nil, err
	}

	// 转换为统一格式
	regions := make([]esim.Region, 0, len(response))
	for _, region := range response {
		regions = append(regions, esim.Region{
			Code: region.Code,
			Name: getRegionName(region.Code), // 这里可以有一个映射函数转换代码到名称
		})
	}

	return regions, nil
}

// ProcessWebhookEvent 处理Webhook事件
func (p *ESIMAccessProvider) ProcessWebhookEvent(ctx context.Context, payload []byte, headers map[string][]string) (*esim.WebhookEvent, error) {
	// 调用API处理Webhook事件
	event, err := p.client.ProcessWebhook(ctx, payload, headers)
	if err != nil {
		return nil, err
	}

	// 从内容中提取相关信息
	var esimID string
	var timestamp time.Time

	if event.Content != nil {
		if iccid, ok := event.Content["iccid"].(string); ok {
			esimID = iccid
		}
		// 尝试解析时间戳，如果存在
		if ts, ok := event.Content["timestamp"].(string); ok {
			if t, err := time.Parse(time.RFC3339, ts); err == nil {
				timestamp = t
			}
		}
	}

	// 转换为统一格式
	webhookEvent := &esim.WebhookEvent{
		EventType:    event.NotifyType,
		ProviderType: "esim_access",
		Timestamp:    timestamp,
		Payload:      event.Content,
		ESIMIdentifier: esim.ESIMIdentifier{
			ICCID: esimID,
		},
	}

	return webhookEvent, nil
}

// 工具函数

// convertStatus 将API状态转换为统一状态
func convertStatus(status string) string {
	switch status {
	case "PENDING":
		return "PENDING"
	case "ACTIVE":
		return "ACTIVE"
	case "SUSPENDED":
		return "SUSPENDED"
	case "CANCELLED":
		return "CANCELLED"
	case "EXPIRED":
		return "EXPIRED"
	default:
		return "UNKNOWN"
	}
}

// calculateDays 计算两个时间之间的天数
func calculateDays(start, end time.Time) int {
	duration := end.Sub(start)
	return int(duration.Hours() / 24)
}

// calculateDaysRemaining 计算剩余天数
func calculateDaysRemaining(now, end time.Time) int {
	if now.After(end) {
		return 0
	}
	duration := end.Sub(now)
	return int(duration.Hours() / 24)
}

// getRegionName 根据区域代码获取区域名称
func getRegionName(code string) string {
	// 这里可以有一个更完整的区域代码到名称的映射
	regionMap := map[string]string{
		"CN": "China",
		"US": "United States",
		"JP": "Japan",
		"KR": "South Korea",
		"GB": "United Kingdom",
		"DE": "Germany",
		"FR": "France",
		"IT": "Italy",
		"ES": "Spain",
		"AU": "Australia",
		// 更多国家映射...
	}

	if name, ok := regionMap[code]; ok {
		return name
	}
	return code // 如果找不到映射，返回原始代码
}
