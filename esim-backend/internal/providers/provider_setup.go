package providers

import (
	"context"

	"vereal/letsesim/pkg/esim"
)

// SetupProviders 初始化并注册所有提供商
func SetupProviders(ctx context.Context, factory esim.ESIMProviderFactoryInterface, configManager esim.ProviderConfigManagerInterface) error {
	// 确保配置已初始化
	if cm, ok := configManager.(*DefaultProviderConfigManager); ok {
		cm.InitializeDefaultConfigs()
	}

	// 注册eSIM Access提供商
	// 注意：这里的初始化会在实现特定提供商后进行补充
	esimAccessProvider, err := NewESIMAccessProvider(configManager)
	if err != nil {
		return err
	}
	if err := factory.RegisterProvider(ctx, esimAccessProvider); err != nil {
		return err
	}

	// 注册Maya Mobile提供商
	mayaMobileProvider, err := NewMayaMobileProvider(configManager)
	if err != nil {
		return err
	}
	if err := factory.RegisterProvider(ctx, mayaMobileProvider); err != nil {
		return err
	}

	return nil
}

// 这些函数仅为占位符，会在实现具体提供商时完成
func NewESIMAccessProvider(configManager esim.ProviderConfigManagerInterface) (esim.ESIMProviderInterface, error) {
	// 将在实现eSIMAccess提供商时完成
	return nil, esim.NewESIMError(
		esim.ErrNotImplemented,
		"ESIMAccess provider not implemented yet",
		nil,
		"",
	)
}

func NewMayaMobileProvider(configManager esim.ProviderConfigManagerInterface) (esim.ESIMProviderInterface, error) {
	// 将在实现MayaMobile提供商时完成
	return nil, esim.NewESIMError(
		esim.ErrNotImplemented,
		"MayaMobile provider not implemented yet",
		nil,
		"",
	)
}
