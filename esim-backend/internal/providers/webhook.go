package providers

import (
	"context"
	"sync"

	"go.uber.org/zap"

	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

// DefaultWebhookHandler 是WebhookHandler的默认实现
type DefaultWebhookHandler struct {
	esimRepo     repository.ESIMRepository
	logger       *zap.Logger
	handlers     map[string]func(ctx context.Context, event *esim.WebhookEvent) error
	handlerMutex sync.RWMutex
}

// NewWebhookHandler 创建一个新的WebhookHandler
func NewWebhookHandler(esimRepo repository.ESIMRepository, logger *zap.Logger) *DefaultWebhookHandler {
	return &DefaultWebhookHandler{
		esimRepo: esimRepo,
		logger:   logger,
		handlers: make(map[string]func(ctx context.Context, event *esim.WebhookEvent) error),
	}
}

// RegisterEventHandler 注册事件处理函数
func (h *DefaultWebhookHandler) RegisterEventHandler(eventType string, handler func(ctx context.Context, event *esim.WebhookEvent) error) {
	h.handlerMutex.Lock()
	defer h.handlerMutex.Unlock()

	h.handlers[eventType] = handler
}

// HandleWebhook 处理webhook
func (h *DefaultWebhookHandler) HandleWebhook(ctx context.Context, providerType string, payload []byte, headers map[string][]string) error {
	// 提取provider类型并解析payload
	provider, err := GetProviderForType(providerType)
	if err != nil {
		h.logger.Error("获取提供商失败", zap.String("providerType", providerType), zap.Error(err))
		return err
	}

	// 处理webhook事件
	event, err := provider.ProcessWebhookEvent(ctx, payload, headers)
	if err != nil {
		h.logger.Error("处理webhook事件失败", zap.String("providerType", providerType), zap.Error(err))
		return err
	}

	// 查找并执行处理函数
	h.handlerMutex.RLock()
	handler, exists := h.handlers[event.EventType]
	h.handlerMutex.RUnlock()

	if exists {
		if err := handler(ctx, event); err != nil {
			h.logger.Error("执行webhook处理函数失败",
				zap.String("providerType", providerType),
				zap.String("eventType", event.EventType),
				zap.Error(err))
			return err
		}
	} else {
		h.logger.Warn("未找到webhook事件处理函数",
			zap.String("providerType", providerType),
			zap.String("eventType", event.EventType))
	}

	return nil
}

// GetWebhookConfig 获取webhook配置
func (h *DefaultWebhookHandler) GetWebhookConfig(ctx context.Context, providerType string) (*esim.WebhookConfig, error) {
	// 此处应从配置管理器获取webhook配置
	// 临时实现返回一个基本配置
	return &esim.WebhookConfig{
		URL:          "", // 在生产环境中应该从配置获取
		Secret:       "", // 在生产环境中应该从配置获取
		ProviderType: providerType,
		EventTypes:   []string{esim.EventTypeOrderStatus, esim.EventTypeESIMStatus},
	}, nil
}

// GetProviderForType 根据提供商类型获取对应的提供商实例
// 此函数应该被实际的工厂方法替代
func GetProviderForType(providerType string) (esim.ESIMProviderInterface, error) {
	// 临时实现，实际应该使用依赖注入的提供商工厂
	return nil, esim.NewESIMError(
		esim.ErrNotImplemented,
		"Provider factory not implemented",
		nil,
		"",
	)
}
