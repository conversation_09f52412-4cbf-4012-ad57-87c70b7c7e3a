package mayamobile

// 请求结构

// ESIMCreateRequest eSIM创建请求
type ESIMCreateRequest struct {
	ICCID       string              `json:"iccid,omitempty"`
	Region      string              `json:"region,omitempty"`
	CountryCode string              `json:"countryCode,omitempty"`
	CustomerID  string              `json:"customerId,omitempty"`
	Tag         string              `json:"tag,omitempty"`
	InitialPlan *InitialPlanRequest `json:"initialPlan,omitempty"`
}

// InitialPlanRequest 初始计划请求
type InitialPlanRequest struct {
	PlanTypeID string `json:"planTypeId,omitempty"`
	ProductID  string `json:"productId,omitempty"`
}

// ESIMUpdateRequest eSIM更新请求
type ESIMUpdateRequest struct {
	Enabled    *bool  `json:"enabled,omitempty"`
	CustomerID string `json:"customerId,omitempty"`
	Tag        string `json:"tag,omitempty"`
}

// PlanCreateRequest 计划创建请求
type PlanCreateRequest struct {
	PlanTypeID string `json:"planTypeId,omitempty"`
	ProductID  string `json:"productId,omitempty"`
	CustomerID string `json:"customerId,omitempty"`
	Reference  string `json:"reference,omitempty"`
}

// SMSRequest 短信发送请求
type SMSRequest struct {
	Message string `json:"message"`
	To      string `json:"to,omitempty"`     // ICCID或MSISDN
	ToType  string `json:"toType,omitempty"` // 'ICCID' 或 'MSISDN'
}

// PlanTypeRequest 计划类型创建请求
type PlanTypeRequest struct {
	Name         string `json:"name"`
	Description  string `json:"description,omitempty"`
	DataBytes    int64  `json:"dataBytes"`
	ValidityDays int    `json:"validityDays"`
}

// CustomerRequest 客户创建/更新请求
type CustomerRequest struct {
	Name         string            `json:"name,omitempty"`
	Email        string            `json:"email,omitempty"`
	ExternalID   string            `json:"externalId,omitempty"`
	Phone        string            `json:"phone,omitempty"`
	CustomFields map[string]string `json:"customFields,omitempty"`
}

// 响应结构

// ESIMResponse eSIM响应
type ESIMResponse struct {
	ICCID         string         `json:"iccid"`
	CustomerID    string         `json:"customerId,omitempty"`
	Tag           string         `json:"tag,omitempty"`
	Status        string         `json:"status"`
	Enabled       bool           `json:"enabled"`
	QRCode        string         `json:"qrCode"`
	ActivationURL string         `json:"activationUrl"`
	EID           string         `json:"eid,omitempty"`
	IMSI          string         `json:"imsi,omitempty"`
	MSISDN        string         `json:"msisdn,omitempty"`
	CreatedAt     string         `json:"createdAt"`
	UpdatedAt     string         `json:"updatedAt"`
	Plans         []PlanResponse `json:"plans,omitempty"`
}

// PlanResponse 计划响应
type PlanResponse struct {
	ID            string           `json:"id"`
	ICCID         string           `json:"iccid"`
	Status        string           `json:"status"`
	Enabled       bool             `json:"enabled"`
	DataBytes     int64            `json:"dataBytes"`
	DataBytesUsed int64            `json:"dataBytesUsed"`
	ValidityDays  int              `json:"validityDays"`
	StartDate     string           `json:"startDate,omitempty"`
	EndDate       string           `json:"endDate,omitempty"`
	PlanType      PlanTypeResponse `json:"planType"`
	CreatedAt     string           `json:"createdAt"`
	UpdatedAt     string           `json:"updatedAt"`
}

// PlanTypeResponse 计划类型响应
type PlanTypeResponse struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	Description  string `json:"description,omitempty"`
	DataBytes    int64  `json:"dataBytes"`
	ValidityDays int    `json:"validityDays"`
	CreatedAt    string `json:"createdAt"`
}

// ProductResponse 产品响应
type ProductResponse struct {
	ID           string   `json:"id"`
	Name         string   `json:"name"`
	Description  string   `json:"description,omitempty"`
	Type         string   `json:"type"`
	DataBytes    int64    `json:"dataBytes"`
	ValidityDays int      `json:"validityDays"`
	Price        int64    `json:"price"`
	Currency     string   `json:"currency"`
	Region       string   `json:"region,omitempty"`
	CountryCodes []string `json:"countryCodes,omitempty"`
	CreatedAt    string   `json:"createdAt"`
}

// NetworkResponse 网络响应
type NetworkResponse struct {
	MCCMNC      string `json:"mccmnc"`
	Name        string `json:"name"`
	Country     string `json:"country"`
	CountryCode string `json:"countryCode"`
	Supports5G  bool   `json:"supports5G"`
	Supports4G  bool   `json:"supports4G"`
}

// RegionResponse 区域响应
type RegionResponse struct {
	Code      string   `json:"code"`
	Name      string   `json:"name"`
	Countries []string `json:"countries"`
}

// CustomerResponse 客户响应
type CustomerResponse struct {
	ID           string            `json:"id"`
	Name         string            `json:"name,omitempty"`
	Email        string            `json:"email,omitempty"`
	ExternalID   string            `json:"externalId,omitempty"`
	Phone        string            `json:"phone,omitempty"`
	CustomFields map[string]string `json:"customFields,omitempty"`
	ESIMs        []ESIMResponse    `json:"esims,omitempty"`
	CreatedAt    string            `json:"createdAt"`
	UpdatedAt    string            `json:"updatedAt"`
}

// AccountBalanceResponse 账户余额响应
type AccountBalanceResponse struct {
	AvailableCredit int64  `json:"availableCredit"`
	Currency        string `json:"currency"`
	HasOverdraft    bool   `json:"hasOverdraft"`
	OverdraftLimit  int64  `json:"overdraftLimit"`
	LastUpdated     string `json:"lastUpdated"`
}

// SMSResponse 短信响应
type SMSResponse struct {
	ID        string `json:"id"`
	Status    string `json:"status"`
	Message   string `json:"message"`
	CreatedAt string `json:"createdAt"`
}

// ListResponse 列表响应泛型
type ListResponse struct {
	Items  []interface{} `json:"items"`
	Total  int           `json:"total"`
	Limit  int           `json:"limit"`
	Offset int           `json:"offset"`
}
