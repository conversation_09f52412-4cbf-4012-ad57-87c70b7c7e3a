package mayamobile

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"vereal/letsesim/pkg/esim"

	"go.uber.org/zap"
)

const ProviderTypeMayaMobile = "maya_mobile"

// Provider Maya Mobile提供商实现
type Provider struct {
	client ClientInterface
	config *esim.ProviderConfig
	logger *zap.Logger
}

// NewProvider 创建新的Maya Mobile提供商实例
func NewProvider(configManager esim.ProviderConfigManagerInterface, logger *zap.Logger) (*Provider, error) {
	ctx := context.Background()
	config, err := configManager.GetConfig(ctx, ProviderTypeMayaMobile)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to get Maya Mobile provider config",
				zap.Error(err),
				zap.String("provider", ProviderTypeMayaMobile))
		}
		return nil, err
	}

	client := NewClient(config)

	return &Provider{
		client: client,
		config: config,
		logger: logger,
	}, nil
}

// GetProviderInfo 获取提供商信息
func (p *Provider) GetProviderInfo(ctx context.Context) esim.ProviderInfo {
	return esim.ProviderInfo{
		Type:    ProviderTypeMayaMobile,
		Name:    "Maya Mobile",
		BaseURL: p.config.BaseURL,
		Capabilities: map[string]bool{
			esim.CapSMS:            true,
			esim.CapTopUp:          true,
			esim.CapCancel:         true, // 通过DELETE /esim/{iccid}
			esim.CapSuspend:        true, // 通过PATCH /esim/{iccid} enabled=false
			esim.CapResume:         true, // 通过PATCH /esim/{iccid} enabled=true
			esim.CapCustomerManage: true,
		},
	}
}

// ListPackages 获取套餐列表
func (p *Provider) ListPackages(ctx context.Context, params esim.PackageQueryParams) ([]esim.Package, *esim.PaginationResult, error) {
	// 构建查询参数
	queryParams := make(map[string]string)

	if params.Region != "" {
		queryParams["region"] = params.Region
	}

	if params.Country != "" {
		queryParams["country"] = params.Country
	}

	// 设置分页
	limit := 100 // 默认值
	offset := 0

	if params.PageSize > 0 {
		limit = params.PageSize
	}

	if params.PageNum > 0 {
		offset = (params.PageNum - 1) * limit
	}

	queryParams["limit"] = strconv.Itoa(limit)
	queryParams["offset"] = strconv.Itoa(offset)

	req := Request{
		Method:      http.MethodGet,
		Path:        "/account/products",
		QueryParams: queryParams,
	}

	var listResp ListResponse
	_, err := p.client.DoRequest(ctx, req, &listResp)
	if err != nil {
		return nil, nil, err
	}

	// 转换产品列表
	packages := make([]esim.Package, 0, len(listResp.Items))
	for _, item := range listResp.Items {
		// 需要将interface{}转换为ProductResponse
		productBytes, err := json.Marshal(item)
		if err != nil {
			continue
		}

		var product ProductResponse
		if err := json.Unmarshal(productBytes, &product); err != nil {
			continue
		}

		packages = append(packages, ConvertToPackage(product))
	}

	pagination := &esim.PaginationResult{
		Total:    int64(listResp.Total),
		PageSize: limit,
		PageNum:  (offset / limit) + 1,
	}

	return packages, pagination, nil
}

// GetPackageDetails 获取套餐详情
func (p *Provider) GetPackageDetails(ctx context.Context, packageId string) (*esim.Package, error) {
	req := Request{
		Method: http.MethodGet,
		Path:   "/product/" + packageId,
	}

	var product ProductResponse
	_, err := p.client.DoRequest(ctx, req, &product)
	if err != nil {
		return nil, err
	}

	pkg := ConvertToPackage(product)
	return &pkg, nil
}

// CreateESIM 创建eSIM订单
func (p *Provider) CreateESIM(ctx context.Context, order *esim.ESIMOrder) (*esim.ESIMOrderResult, error) {
	// 构建创建请求
	createReq := ESIMCreateRequest{
		CustomerID: order.CustomerID,
	}

	// 添加初始计划
	if order.PackageID != "" {
		createReq.InitialPlan = &InitialPlanRequest{
			ProductID: order.PackageID,
		}
	}

	// 从ProviderSpecific中获取额外参数
	if order.ProviderSpecific != nil {
		if region, ok := order.ProviderSpecific["region"].(string); ok {
			createReq.Region = region
		}

		if countryCode, ok := order.ProviderSpecific["countryCode"].(string); ok {
			createReq.CountryCode = countryCode
		}

		if tag, ok := order.ProviderSpecific["tag"].(string); ok {
			createReq.Tag = tag
		}

		if planTypeID, ok := order.ProviderSpecific["planTypeId"].(string); ok && createReq.InitialPlan != nil {
			createReq.InitialPlan.PlanTypeID = planTypeID
		}
	}

	// Maya Mobile不支持批量创建，需要逐个创建
	esims := make([]esim.ESIM, 0, order.Count)

	if p.logger != nil {
		p.logger.Info("Starting Maya Mobile eSIM creation batch",
			zap.Int("batchSize", order.Count),
			zap.String("packageID", order.PackageID),
			zap.String("customerID", order.CustomerID),
			zap.String("transactionID", order.TransactionID))
	}

	for i := 0; i < order.Count; i++ {
		req := Request{
			Method: http.MethodPost,
			Path:   "/esim",
			Body:   createReq,
		}

		var esimResp ESIMResponse
		_, err := p.client.DoRequest(ctx, req, &esimResp)
		if err != nil {
			if p.logger != nil {
				p.logger.Error("Failed to create eSIM",
					zap.Error(err),
					zap.Int("itemIndex", i+1),
					zap.Int("totalCount", order.Count),
					zap.String("packageID", order.PackageID),
					zap.String("customerID", order.CustomerID),
					zap.String("transactionID", order.TransactionID))
			}
			return nil, fmt.Errorf("failed to create eSIM %d of %d: %w", i+1, order.Count, err)
		}

		if p.logger != nil {
			p.logger.Debug("Successfully created eSIM",
				zap.Int("itemIndex", i+1),
				zap.Int("totalCount", order.Count),
				zap.String("iccid", esimResp.ICCID),
				zap.String("customerID", order.CustomerID))
		}

		esims = append(esims, ConvertToESIM(esimResp))
	}

	// 生成一个唯一的订单号
	orderNo := fmt.Sprintf("MM-%d", time.Now().UnixNano())

	orderResult := &esim.ESIMOrderResult{
		OrderNo:       orderNo,
		TransactionID: order.TransactionID,
		ESIMs:         esims,
		IsPending:     false, // Maya Mobile是同步创建的
	}

	if p.logger != nil {
		p.logger.Info("Successfully completed Maya Mobile eSIM batch creation",
			zap.String("orderNo", orderNo),
			zap.String("transactionID", order.TransactionID),
			zap.Int("createdCount", len(esims)),
			zap.Int("requestedCount", order.Count))
	}

	return orderResult, nil
}

// GetESIMDetails 获取eSIM详情
func (p *Provider) GetESIMDetails(ctx context.Context, esimID esim.ESIMIdentifier) (*esim.ESIM, error) {
	if esimID.ICCID == "" {
		return nil, esim.NewESIMError(
			esim.ErrInvalidParams,
			"ICCID is required for Maya Mobile",
			nil,
			"",
		)
	}

	req := Request{
		Method: http.MethodGet,
		Path:   "/esim/" + esimID.ICCID,
	}

	var esimResp ESIMResponse
	_, err := p.client.DoRequest(ctx, req, &esimResp)
	if err != nil {
		return nil, err
	}

	// 获取计划信息
	plansReq := Request{
		Method: http.MethodGet,
		Path:   "/esim/" + esimID.ICCID + "/plans",
	}

	var plansResp []PlanResponse
	_, err = p.client.DoRequest(ctx, plansReq, &plansResp)
	if err == nil {
		esimResp.Plans = plansResp
	}

	esimInfo := ConvertToESIM(esimResp)
	return &esimInfo, nil
}

// ListESIMs 获取eSIM列表
func (p *Provider) ListESIMs(ctx context.Context, params esim.ESIMQueryParams) (*esim.ESIMListResult, error) {
	// 构建查询参数
	queryParams := make(map[string]string)

	if params.CustomerID != "" {
		// Maya Mobile通过客户API获取客户的eSIMs
		return p.listCustomerESIMs(ctx, params.CustomerID, params.PageSize, params.PageNum)
	}

	// 设置分页
	limit := 100 // 默认值
	offset := 0

	if params.PageSize > 0 {
		limit = params.PageSize
	}

	if params.PageNum > 0 {
		offset = (params.PageNum - 1) * limit
	}

	queryParams["limit"] = strconv.Itoa(limit)
	queryParams["offset"] = strconv.Itoa(offset)

	req := Request{
		Method:      http.MethodGet,
		Path:        "/account/esims",
		QueryParams: queryParams,
	}

	var listResp ListResponse
	_, err := p.client.DoRequest(ctx, req, &listResp)
	if err != nil {
		return nil, err
	}

	// 转换eSIM列表
	esimResponses := make([]ESIMResponse, 0, len(listResp.Items))
	for _, item := range listResp.Items {
		esimBytes, err := json.Marshal(item)
		if err != nil {
			continue
		}

		var esimResp ESIMResponse
		if err := json.Unmarshal(esimBytes, &esimResp); err != nil {
			continue
		}

		// 获取计划信息
		plansReq := Request{
			Method: http.MethodGet,
			Path:   "/esim/" + esimResp.ICCID + "/plans",
		}

		var plansResp []PlanResponse
		_, err = p.client.DoRequest(ctx, plansReq, &plansResp)
		if err == nil {
			esimResp.Plans = plansResp
		}

		esimResponses = append(esimResponses, esimResp)
	}

	return ConvertToESIMListResult(esimResponses, listResp.Total, listResp.Limit, listResp.Offset), nil
}

// listCustomerESIMs 获取客户的eSIM列表
func (p *Provider) listCustomerESIMs(ctx context.Context, customerID string, pageSize, pageNum int) (*esim.ESIMListResult, error) {
	req := Request{
		Method: http.MethodGet,
		Path:   "/customer/" + customerID,
	}

	var customer CustomerResponse
	_, err := p.client.DoRequest(ctx, req, &customer)
	if err != nil {
		return nil, err
	}

	// 分页处理
	limit := 100 // 默认值
	offset := 0

	if pageSize > 0 {
		limit = pageSize
	}

	if pageNum > 0 {
		offset = (pageNum - 1) * limit
	}

	total := len(customer.ESIMs)

	// 手动分页
	endIndex := offset + limit
	if endIndex > total {
		endIndex = total
	}

	var esimSlice []ESIMResponse
	if offset < total {
		esimSlice = customer.ESIMs[offset:endIndex]
	} else {
		esimSlice = []ESIMResponse{}
	}

	// 获取每个eSIM的计划信息
	for i, esimResp := range esimSlice {
		plansReq := Request{
			Method: http.MethodGet,
			Path:   "/esim/" + esimResp.ICCID + "/plans",
		}

		var plansResp []PlanResponse
		_, err = p.client.DoRequest(ctx, plansReq, &plansResp)
		if err == nil {
			esimSlice[i].Plans = plansResp
		}
	}

	return ConvertToESIMListResult(esimSlice, total, limit, offset), nil
}

// CancelESIM 取消eSIM
func (p *Provider) CancelESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	if esimID.ICCID == "" {
		return esim.NewESIMError(
			esim.ErrInvalidParams,
			"ICCID is required for Maya Mobile",
			nil,
			"",
		)
	}

	req := Request{
		Method: http.MethodDelete,
		Path:   "/esim/" + esimID.ICCID,
	}

	_, err := p.client.DoRequest(ctx, req, nil)
	return err
}

// SuspendESIM 暂停eSIM
func (p *Provider) SuspendESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	if esimID.ICCID == "" {
		return esim.NewESIMError(
			esim.ErrInvalidParams,
			"ICCID is required for Maya Mobile",
			nil,
			"",
		)
	}

	enabled := false
	req := Request{
		Method: http.MethodPatch,
		Path:   "/esim/" + esimID.ICCID,
		Body: ESIMUpdateRequest{
			Enabled: &enabled,
		},
	}

	_, err := p.client.DoRequest(ctx, req, nil)
	return err
}

// ResumeESIM 恢复eSIM
func (p *Provider) ResumeESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	if esimID.ICCID == "" {
		return esim.NewESIMError(
			esim.ErrInvalidParams,
			"ICCID is required for Maya Mobile",
			nil,
			"",
		)
	}

	enabled := true
	req := Request{
		Method: http.MethodPatch,
		Path:   "/esim/" + esimID.ICCID,
		Body: ESIMUpdateRequest{
			Enabled: &enabled,
		},
	}

	_, err := p.client.DoRequest(ctx, req, nil)
	return err
}

// RevokeESIM 撤销eSIM
func (p *Provider) RevokeESIM(ctx context.Context, esimID esim.ESIMIdentifier) error {
	// Maya Mobile的API没有明确的revoke操作
	// 使用delete代替
	return p.CancelESIM(ctx, esimID)
}

// TopUpESIM 充值eSIM
func (p *Provider) TopUpESIM(ctx context.Context, params esim.TopUpParams) (*esim.TopUpResult, error) {
	if params.ESIMID.ICCID == "" {
		return nil, esim.NewESIMError(
			esim.ErrInvalidParams,
			"ICCID is required for Maya Mobile",
			nil,
			"",
		)
	}

	// 创建充值计划
	req := Request{
		Method: http.MethodPost,
		Path:   "/esim/" + params.ESIMID.ICCID + "/plan/" + params.PackageID,
		Body: PlanCreateRequest{
			ProductID: params.PackageID,
			Reference: params.TransactionID,
		},
	}

	var planResp PlanResponse
	_, err := p.client.DoRequest(ctx, req, &planResp)
	if err != nil {
		return nil, err
	}

	return ConvertToTopUpResult(planResp, params.TransactionID), nil
}

// SendSMS 发送短信
func (p *Provider) SendSMS(ctx context.Context, params esim.SMSParams) error {
	if params.ESIMID.ICCID == "" {
		return esim.NewESIMError(
			esim.ErrInvalidParams,
			"ICCID is required for Maya Mobile",
			nil,
			"",
		)
	}

	req := Request{
		Method: http.MethodPost,
		Path:   "/sms",
		Body: SMSRequest{
			Message: params.Message,
			To:      params.ESIMID.ICCID,
			ToType:  "ICCID",
		},
	}

	_, err := p.client.DoRequest(ctx, req, nil)
	return err
}

// GetESIMUsage 获取eSIM使用情况
func (p *Provider) GetESIMUsage(ctx context.Context, esimID esim.ESIMIdentifier) (*esim.ESIMUsage, error) {
	if esimID.ICCID == "" {
		errMsg := "ICCID is required for Maya Mobile usage query"
		if p.logger != nil {
			p.logger.Error(errMsg,
				zap.Any("esimID", esimID))
		}
		return nil, esim.NewESIMError(
			esim.ErrInvalidParams,
			errMsg,
			nil,
			"",
		)
	}

	// 首先获取ESIM详情
	esimReq := Request{
		Method: http.MethodGet,
		Path:   "/esim/" + esimID.ICCID,
	}

	var esimResp ESIMResponse
	_, err := p.client.DoRequest(ctx, esimReq, &esimResp)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to get eSIM details for usage query",
				zap.Error(err),
				zap.String("iccid", esimID.ICCID))
		}
		return nil, err
	}

	// 获取计划详情
	plansReq := Request{
		Method: http.MethodGet,
		Path:   "/esim/" + esimID.ICCID + "/plans",
	}

	var plansResp []PlanResponse
	_, err = p.client.DoRequest(ctx, plansReq, &plansResp)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to get eSIM plans",
				zap.Error(err),
				zap.String("iccid", esimID.ICCID))
		}
		return nil, err
	}

	if len(plansResp) == 0 {
		if p.logger != nil {
			p.logger.Warn("No plans found for eSIM",
				zap.String("iccid", esimID.ICCID))
		}
		return &esim.ESIMUsage{
			DataUsage:      0,
			TotalData:      0,
			LastUpdateTime: time.Now(),
		}, nil
	}

	// 使用第一个计划的数据（通常只有一个活动计划）
	activePlan := plansResp[0]

	// 解析时间
	startTime, _ := time.Parse(time.RFC3339, activePlan.StartDate)
	endTime, _ := time.Parse(time.RFC3339, activePlan.EndDate)

	usage := &esim.ESIMUsage{
		DataUsage:      activePlan.DataBytesUsed, // 已使用数据
		TotalData:      activePlan.DataBytes,     // 总数据
		LastUpdateTime: time.Now(),
	}

	if p.logger != nil {
		p.logger.Debug("Successfully retrieved eSIM usage",
			zap.String("iccid", esimID.ICCID),
			zap.Int64("dataUsed", usage.DataUsage),
			zap.Int64("totalData", usage.TotalData),
			zap.Time("startTime", startTime),
			zap.Time("endTime", endTime))
	}

	return usage, nil
}

// GetAccountBalance 获取账户余额
func (p *Provider) GetAccountBalance(ctx context.Context) (*esim.AccountBalance, error) {
	req := Request{
		Method: http.MethodGet,
		Path:   "/account/balance",
	}

	var balanceResp AccountBalanceResponse
	_, err := p.client.DoRequest(ctx, req, &balanceResp)
	if err != nil {
		return nil, err
	}

	return ConvertToAccountBalance(balanceResp), nil
}

// ListSupportedRegions 获取支持的区域列表
func (p *Provider) ListSupportedRegions(ctx context.Context) ([]esim.Region, error) {
	// 首先获取网络列表，然后按区域分组
	req := Request{
		Method: http.MethodGet,
		Path:   "/account/networks",
	}

	var networksResp []NetworkResponse
	_, err := p.client.DoRequest(ctx, req, &networksResp)
	if err != nil {
		return nil, err
	}

	// 按区域分组国家
	regionMap := make(map[string][]string)
	for _, network := range networksResp {
		if network.CountryCode != "" {
			// 使用大陆区域作为分组
			region := getRegionForCountry(network.CountryCode)
			regionMap[region] = appendUnique(regionMap[region], network.CountryCode)
		}
	}

	// 转换为区域列表
	regions := make([]RegionResponse, 0, len(regionMap))
	for regionCode, countries := range regionMap {
		regions = append(regions, RegionResponse{
			Code:      regionCode,
			Name:      getRegionName(regionCode),
			Countries: countries,
		})
	}

	return ConvertToRegions(regions), nil
}

// ProcessWebhookEvent 处理Webhook事件
func (p *Provider) ProcessWebhookEvent(ctx context.Context, payload []byte, headers map[string][]string) (*esim.WebhookEvent, error) {
	// Maya Mobile文档中未明确提及Webhook，这里返回不支持错误
	return nil, esim.NewESIMError(
		esim.ErrNotImplemented,
		"Maya Mobile does not support webhooks or they are not documented",
		nil,
		"",
	)
}

// 辅助函数

// getRegionForCountry 根据国家代码获取区域代码
func getRegionForCountry(countryCode string) string {
	// 简化的区域分配
	europeCountries := []string{"AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "GB"}
	asiaCountries := []string{"CN", "JP", "KR", "IN", "ID", "MY", "PH", "SG", "TH", "VN", "HK", "TW"}
	americasCountries := []string{"US", "CA", "MX", "BR", "AR", "CL", "CO", "PE"}

	for _, country := range europeCountries {
		if country == countryCode {
			return "europe"
		}
	}

	for _, country := range asiaCountries {
		if country == countryCode {
			return "apac"
		}
	}

	for _, country := range americasCountries {
		if country == countryCode {
			return "americas"
		}
	}

	return "other"
}

// getRegionName 获取区域名称
func getRegionName(regionCode string) string {
	regionNames := map[string]string{
		"europe":   "Europe",
		"apac":     "Asia Pacific",
		"americas": "Americas",
		"other":    "Other Regions",
	}

	if name, ok := regionNames[regionCode]; ok {
		return name
	}
	return regionCode
}

// appendUnique 将唯一值添加到切片中
func appendUnique(slice []string, value string) []string {
	for _, item := range slice {
		if item == value {
			return slice
		}
	}
	return append(slice, value)
}
