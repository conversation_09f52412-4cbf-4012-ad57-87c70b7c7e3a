package mayamobile

import (
	"strings"
	"time"

	"vereal/letsesim/pkg/esim"
)

// ConvertToPackage 将Maya Mobile产品转换为统一Package模型
func ConvertToPackage(product ProductResponse) esim.Package {
	// 网络类型处理
	networkTypes := []string{"4G"}
	if product.Type == "5G" {
		networkTypes = []string{"4G", "5G"}
	}

	return esim.Package{
		ID:            product.ID,
		Name:          product.Name,
		Description:   product.Description,
		Price:         product.Price,
		Currency:      product.Currency,
		DataVolume:    product.DataBytes,
		ValidityDays:  product.ValidityDays,
		LocationCodes: product.CountryCodes,
		SupportsSMS:   true,    // 假设Maya Mobile支持SMS
		DataType:      "FIXED", // Maya Mobile文档中未明确提及无限流量计划
		NetworkTypes:  networkTypes,
		SupportTopUp:  true, // 假设支持充值
		ProviderSpecific: map[string]interface{}{
			"type":      product.Type,
			"region":    product.Region,
			"createdAt": product.CreatedAt,
		},
	}
}

// ConvertToESIM 将Maya Mobile eSIM响应转换为统一ESIM模型
func ConvertToESIM(esimResp ESIMResponse) esim.ESIM {
	// 解析时间
	var expiryTime time.Time

	// 尝试从最后一个计划获取到期时间
	if len(esimResp.Plans) > 0 {
		lastPlan := esimResp.Plans[len(esimResp.Plans)-1]
		if lastPlan.EndDate != "" {
			expiryTime, _ = time.Parse(time.RFC3339, lastPlan.EndDate)
		}
	}

	// 计算总数据量和已使用数据量
	var totalDataVolume int64
	var usedDataVolume int64
	for _, plan := range esimResp.Plans {
		totalDataVolume += plan.DataBytes
		usedDataVolume += plan.DataBytesUsed
	}

	// 转换状态
	status := esim.ESIMStatus{
		Code:            esimResp.Status,
		SMDPStatus:      mapStatusToSMDPStatus(esimResp.Status),
		InstalledStatus: getInstalledStatus(esimResp),
		Description:     getStatusDescription(esimResp.Status),
	}

	// 转换套餐
	packages := []esim.Package{}
	for _, plan := range esimResp.Plans {
		// 创建一个简单的Package，因为计划中没有完整的产品信息
		packages = append(packages, esim.Package{
			ID:           plan.PlanType.ID,
			Name:         plan.PlanType.Name,
			Description:  plan.PlanType.Description,
			DataVolume:   plan.DataBytes,
			ValidityDays: plan.ValidityDays,
			ProviderSpecific: map[string]interface{}{
				"planId":    plan.ID,
				"status":    plan.Status,
				"startDate": plan.StartDate,
				"endDate":   plan.EndDate,
			},
		})
	}

	// 转换SMS功能
	smsCapability := esim.SMSCapability{
		Supported: esimResp.MSISDN != "",
		MSISDN:    esimResp.MSISDN,
		APIOnly:   false, // 假设支持设备上的SMS
	}

	return esim.ESIM{
		Identifier: esim.ESIMIdentifier{
			ICCID: esimResp.ICCID,
		},
		Status:         status,
		ActivationCode: extractActivationCode(esimResp.ActivationURL),
		QRCodeURL:      esimResp.QRCode,
		DataVolume:     totalDataVolume,
		UsedData:       usedDataVolume,
		ValidityDays:   calculateValidityDays(esimResp.Plans),
		ExpiryTime:     expiryTime,
		ActiveType:     1, // 假设为首次安装激活
		EID:            esimResp.EID,
		Packages:       packages,
		SMS:            smsCapability,
		ProviderSpecific: map[string]interface{}{
			"imsi":       esimResp.IMSI,
			"customerId": esimResp.CustomerID,
			"tag":        esimResp.Tag,
			"enabled":    esimResp.Enabled,
			"createdAt":  esimResp.CreatedAt,
			"updatedAt":  esimResp.UpdatedAt,
		},
	}
}

// ConvertToESIMListResult 将Maya Mobile eSIM列表响应转换为统一ESIMListResult模型
func ConvertToESIMListResult(esims []ESIMResponse, total, limit, offset int) *esim.ESIMListResult {
	result := &esim.ESIMListResult{
		ESIMs: make([]esim.ESIM, len(esims)),
		Pagination: esim.PaginationResult{
			Total:    int64(total),
			PageSize: limit,
			PageNum:  (offset / limit) + 1,
		},
	}

	for i, esimResp := range esims {
		result.ESIMs[i] = ConvertToESIM(esimResp)
	}

	return result
}

// ConvertToTopUpResult 将Maya Mobile计划响应转换为统一TopUpResult模型
func ConvertToTopUpResult(planResp PlanResponse, transactionID string) *esim.TopUpResult {
	var expiryTime time.Time
	if planResp.EndDate != "" {
		expiryTime, _ = time.Parse(time.RFC3339, planResp.EndDate)
	}

	return &esim.TopUpResult{
		TransactionID: transactionID,
		ExpiryTime:    expiryTime,
		TotalVolume:   planResp.DataBytes,
		OrderUsage:    planResp.DataBytesUsed,
	}
}

// ConvertToAccountBalance 将Maya Mobile账户余额响应转换为统一AccountBalance模型
func ConvertToAccountBalance(balanceResp AccountBalanceResponse) *esim.AccountBalance {
	return &esim.AccountBalance{
		Balance:        balanceResp.AvailableCredit,
		Currency:       balanceResp.Currency,
		HasOverdraft:   balanceResp.HasOverdraft,
		OverdraftLimit: balanceResp.OverdraftLimit,
	}
}

// ConvertToRegions 将Maya Mobile区域响应转换为统一Region模型
func ConvertToRegions(regions []RegionResponse) []esim.Region {
	result := make([]esim.Region, len(regions))

	for i, region := range regions {
		// 为每个国家创建子区域
		subLocations := make([]esim.SubLocation, len(region.Countries))
		for j, country := range region.Countries {
			subLocations[j] = esim.SubLocation{
				Code: country,
				Name: country, // 使用国家代码作为名称
			}
		}

		result[i] = esim.Region{
			Code:         region.Code,
			Name:         region.Name,
			Type:         2, // 多国家类型
			SubLocations: subLocations,
		}
	}

	return result
}

// ConvertToESIMUsage 将Maya Mobile计划响应转换为统一ESIMUsage模型
func ConvertToESIMUsage(planResp PlanResponse) *esim.ESIMUsage {
	updatedTime, _ := time.Parse(time.RFC3339, planResp.UpdatedAt)

	return &esim.ESIMUsage{
		DataUsage:      planResp.DataBytesUsed,
		TotalData:      planResp.DataBytes,
		LastUpdateTime: updatedTime,
	}
}

// 辅助函数

// mapStatusToSMDPStatus 将状态映射到SMDP状态
func mapStatusToSMDPStatus(status string) string {
	switch status {
	case "READY":
		return "RELEASED"
	case "INSTALLED":
		return "INSTALLATION"
	case "ENABLED":
		return "ENABLED"
	case "DISABLED":
		return "DISABLED"
	case "DELETED":
		return "DELETED"
	default:
		return ""
	}
}

// getInstalledStatus 获取安装状态
func getInstalledStatus(esimResp ESIMResponse) string {
	if esimResp.EID != "" {
		return "INSTALLED"
	}
	return "NOT_INSTALLED"
}

// getStatusDescription 获取状态描述
func getStatusDescription(status string) string {
	statusMap := map[string]string{
		"READY":     "Ready for Download",
		"INSTALLED": "Installed on Device",
		"ENABLED":   "Enabled",
		"DISABLED":  "Disabled",
		"DELETED":   "Deleted",
	}

	if desc, ok := statusMap[status]; ok {
		return desc
	}
	return "Unknown Status"
}

// extractActivationCode 从激活URL中提取激活码
func extractActivationCode(activationURL string) string {
	// Maya Mobile文档中未明确激活码的格式
	// 这里假设激活URL中包含激活码
	if activationURL == "" {
		return ""
	}

	// 例如：从URL中提取LPA部分
	if strings.Contains(activationURL, "LPA:") {
		return activationURL
	}

	// 如果URL不是直接的激活码，可能需要构造
	return "LPA:" + activationURL
}

// calculateValidityDays 计算有效期天数
func calculateValidityDays(plans []PlanResponse) int {
	if len(plans) == 0 {
		return 0
	}

	// 使用最后一个计划的有效期
	return plans[len(plans)-1].ValidityDays
}
