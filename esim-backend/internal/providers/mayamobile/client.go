package mayamobile

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"vereal/letsesim/pkg/esim"
)

// ClientInterface Maya Mobile API 客户端接口
type ClientInterface interface {
	DoRequest(ctx context.Context, req Request, result interface{}) (*Response, error)
}

// Client Maya Mobile API客户端
type Client struct {
	httpClient *http.Client
	config     *esim.ProviderConfig
	baseURL    string
}

// NewClient 创建新的Maya Mobile API客户端
func NewClient(config *esim.ProviderConfig) *Client {
	client := &http.Client{
		Timeout: time.Duration(config.TimeoutSeconds) * time.Second,
	}

	return &Client{
		httpClient: client,
		config:     config,
		baseURL:    config.BaseURL,
	}
}

// Request API请求结构
type Request struct {
	Method      string
	Path        string
	QueryParams map[string]string
	Body        interface{}
}

// Response API响应结构
type Response struct {
	StatusCode int
	Headers    http.Header
	Body       []byte
	RequestID  string
}

// DoRequest 执行API请求
func (c *Client) DoRequest(ctx context.Context, req Request, result interface{}) (*Response, error) {
	var bodyData []byte
	var err error

	if req.Body != nil {
		bodyData, err = json.Marshal(req.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
	}

	// 构建URL
	requestURL := c.baseURL + req.Path
	if len(req.QueryParams) > 0 {
		params := url.Values{}
		for key, value := range req.QueryParams {
			params.Add(key, value)
		}
		requestURL = requestURL + "?" + params.Encode()
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, req.Method, requestURL, bytes.NewReader(bodyData))
	if err != nil {
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置Basic Auth
	authToken := base64.StdEncoding.EncodeToString([]byte(c.config.APIKey + ":" + c.config.APISecret))
	httpReq.Header.Set("Authorization", "Basic "+authToken)

	// 设置Content-Type
	if req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// 设置User-Agent
	httpReq.Header.Set("User-Agent", "MayaMobile-GoClient/1.0")

	// 执行请求
	httpResp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 读取响应内容
	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 解析请求ID
	requestID := httpResp.Header.Get("X-Request-ID")

	// 构建响应
	response := &Response{
		StatusCode: httpResp.StatusCode,
		Headers:    httpResp.Header,
		Body:       respBody,
		RequestID:  requestID,
	}

	// 检查错误状态码
	if httpResp.StatusCode >= 400 {
		return response, c.handleErrorResponse(response, requestID)
	}

	// 解析结果
	if result != nil && len(respBody) > 0 {
		if err := json.Unmarshal(respBody, result); err != nil {
			return response, fmt.Errorf("failed to unmarshal response: %w", err)
		}
	}

	return response, nil
}

// handleErrorResponse 处理错误响应
func (c *Client) handleErrorResponse(response *Response, requestID string) error {
	var errorResponse struct {
		Error       string `json:"error"`
		Description string `json:"error_description"`
		Status      int    `json:"status"`
	}

	if len(response.Body) > 0 {
		if err := json.Unmarshal(response.Body, &errorResponse); err != nil {
			// 无法解析错误信息，使用状态码
			return c.mapHTTPErrorToESIMError(response.StatusCode, "Unknown error", string(response.Body), requestID)
		}
	}

	errorMessage := errorResponse.Description
	if errorMessage == "" {
		errorMessage = errorResponse.Error
	}
	if errorMessage == "" {
		errorMessage = http.StatusText(response.StatusCode)
	}

	return c.mapHTTPErrorToESIMError(response.StatusCode, errorMessage, string(response.Body), requestID)
}

// mapHTTPErrorToESIMError 将HTTP错误映射到ESIM错误
func (c *Client) mapHTTPErrorToESIMError(statusCode int, message, rawBody, requestID string) error {
	var code string

	switch statusCode {
	case http.StatusBadRequest:
		code = esim.ErrInvalidParams
	case http.StatusUnauthorized:
		code = esim.ErrAuthFailed
	case http.StatusForbidden:
		code = esim.ErrOperationNotAllowed
	case http.StatusNotFound:
		code = esim.ErrNotFound
	case http.StatusTooManyRequests:
		code = esim.ErrRateLimited
	case http.StatusInternalServerError:
		code = esim.ErrServerError
	default:
		code = esim.ErrProviderSpecific
	}

	return esim.NewESIMError(
		code,
		message,
		map[string]interface{}{
			"httpStatus": statusCode,
			"rawBody":    rawBody,
		},
		requestID,
	)
}
