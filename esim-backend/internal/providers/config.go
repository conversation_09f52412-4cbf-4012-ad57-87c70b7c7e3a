package providers

import (
	"context"
	"fmt"
	"sync"
	"time"

	"vereal/letsesim/pkg/esim"
)

// DefaultProviderConfigManager 默认的提供商配置管理器实现
type DefaultProviderConfigManager struct {
	configs map[string]*esim.ProviderConfig
	mu      sync.RWMutex
	// 在实际应用中，可能会使用数据库存储配置
	// db *sql.DB
}

// NewProviderConfigManager 创建新的配置管理器
func NewProviderConfigManager() *DefaultProviderConfigManager {
	return &DefaultProviderConfigManager{
		configs: make(map[string]*esim.ProviderConfig),
	}
}

// InitializeDefaultConfigs 初始化默认配置
func (m *DefaultProviderConfigManager) InitializeDefaultConfigs() {
	// 初始化eSIMAccess提供商默认配置
	m.configs[ProviderTypeESIMAccess] = &esim.ProviderConfig{
		Type:    ProviderTypeESIMAccess,
		Enabled: true,
		BaseURL: "https://api.esimaccess.com",
		WebhookConfig: esim.WebhookConfig{
			URL:          "",
			Secret:       "",
			ProviderType: ProviderTypeESIMAccess,
			EventTypes: []string{
				esim.EventTypeOrderStatus,
				esim.EventTypeESIMStatus,
				esim.EventTypeDataUsage,
				esim.EventTypeValidityUsage,
			},
		},
		RateLimitPerSec: 8, // 根据文档，每秒8个请求
		TimeoutSeconds:  30,
		RetryConfig: esim.RetryConfig{
			MaxRetries:       3,
			RetryInterval:    time.Second * 1,
			MaxRetryInterval: time.Second * 5,
		},
		AdditionalConfig: map[string]interface{}{},
	}

	// 初始化MayaMobile提供商默认配置
	m.configs[ProviderTypeMayaMobile] = &esim.ProviderConfig{
		Type:    ProviderTypeMayaMobile,
		Enabled: true,
		BaseURL: "https://api.maya.net/connectivity/v1",
		WebhookConfig: esim.WebhookConfig{
			URL:          "",
			Secret:       "",
			ProviderType: ProviderTypeMayaMobile,
			EventTypes:   []string{}, // Maya未明确提及Webhook
		},
		RateLimitPerSec: 10, // 假设的值
		TimeoutSeconds:  30,
		RetryConfig: esim.RetryConfig{
			MaxRetries:       3,
			RetryInterval:    time.Second * 1,
			MaxRetryInterval: time.Second * 5,
		},
		AdditionalConfig: map[string]interface{}{},
	}
}

// GetConfig 获取提供商配置
func (m *DefaultProviderConfigManager) GetConfig(ctx context.Context, providerType string) (*esim.ProviderConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	config, exists := m.configs[providerType]
	if !exists {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Configuration for provider '%s' not found", providerType),
			nil,
			"",
		)
	}

	// 返回配置的副本，避免外部修改
	configCopy := *config
	return &configCopy, nil
}

// UpdateConfig 更新提供商配置
func (m *DefaultProviderConfigManager) UpdateConfig(ctx context.Context, config *esim.ProviderConfig) error {
	if config == nil {
		return esim.NewESIMError(
			esim.ErrInvalidParams,
			"Config cannot be nil",
			nil,
			"",
		)
	}

	if config.Type == "" {
		return esim.NewESIMError(
			esim.ErrInvalidParams,
			"Provider type cannot be empty",
			nil,
			"",
		)
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 在生产环境中，这里可能需要进行更复杂的验证和合并
	// 例如，检查必填字段，合并默认值等
	m.configs[config.Type] = config

	// 在实际应用中，这里应该将配置持久化到数据库
	return nil
}

// SetProviderEnabled 启用或禁用提供商
func (m *DefaultProviderConfigManager) SetProviderEnabled(ctx context.Context, providerType string, enabled bool) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	config, exists := m.configs[providerType]
	if !exists {
		return esim.NewESIMError(
			esim.ErrNotFound,
			fmt.Sprintf("Configuration for provider '%s' not found", providerType),
			nil,
			"",
		)
	}

	config.Enabled = enabled
	// 在实际应用中，这里应该将更新持久化到数据库
	return nil
}

// ListConfigs 获取所有提供商配置
func (m *DefaultProviderConfigManager) ListConfigs(ctx context.Context) ([]*esim.ProviderConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	configs := make([]*esim.ProviderConfig, 0, len(m.configs))
	for _, config := range m.configs {
		// 返回配置的副本，避免外部修改
		configCopy := *config
		configs = append(configs, &configCopy)
	}

	return configs, nil
}
