package di

import (
	"context"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"vereal/letsesim/config"
	"vereal/letsesim/internal/api"
	"vereal/letsesim/internal/api/handlers"
	apimiddleware "vereal/letsesim/internal/api/middleware"
	"vereal/letsesim/internal/async"
	"vereal/letsesim/internal/providers"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/internal/repository/postgres"
	"vereal/letsesim/internal/service"
	"vereal/letsesim/pkg/cache"
	"vereal/letsesim/pkg/database"
	"vereal/letsesim/pkg/jwt"
	"vereal/letsesim/pkg/logger"
	redisclient "vereal/letsesim/pkg/redis"
)

// 自定义验证器
type CustomValidator struct {
	validator *validator.Validate
}

// Validate 实现 echo.Validator 接口
func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.validator.Struct(i)
}

// App 应用程序
type App struct {
	Echo                  *echo.Echo
	DB                    *gorm.DB
	RedisClient           *redisclient.Client
	Logger                *zap.Logger
	UserService           *service.UserService
	CreditService         *service.CreditService
	RateLimiterMiddleware *apimiddleware.RateLimiterMiddleware
	QueryDecorator        *database.QueryDecorator
	Cache                 cache.Cache
	TaskManager           *async.TaskManager
}

// InitializeApp 初始化应用程序
func InitializeApp(cfg *config.Config) (*App, error) {
	// 初始化日志
	log, err := logger.NewLogger(&cfg.Log)
	if err != nil {
		return nil, err
	}

	// 初始化数据库
	db, err := postgres.NewDB(cfg.Database)
	if err != nil {
		return nil, err
	}

	// 初始化数据库查询装饰器
	queryDecorator := database.NewQueryDecorator(db)

	// 初始化Redis客户端
	redisClient, err := redisclient.NewClient(&cfg.Redis)
	if err != nil {
		return nil, err
	}

	// 初始化缓存系统
	cacheConfig := &cache.Config{
		Enabled:      true,
		DefaultTTL:   30 * time.Minute,
		LocalMaxSize: 10000,
	}

	// 创建本地缓存
	localCache, err := cache.NewLocalCache(cacheConfig)
	if err != nil {
		log.Error("Failed to initialize local cache", zap.Error(err))
		return nil, err
	}

	// 创建Redis缓存
	redisCache := cache.NewRedisCache(redisClient, cacheConfig)

	// 创建混合缓存（本地+Redis）
	hybridCache := cache.NewHybridCache(localCache, redisCache, cacheConfig)

	// 初始化任务追踪器
	taskTracker := async.NewRedisTaskTrackerImpl(redisClient)

	// 初始化任务管理器
	taskManagerOpts := async.DefaultTaskManagerOptions()
	taskManager := async.NewTaskManager(taskTracker, redisClient, taskManagerOpts)

	// 启动任务管理器
	go taskManager.Start(context.Background())

	// 初始化Echo框架
	e := echo.New()
	e.HideBanner = true
	e.Validator = &CustomValidator{validator: validator.New()}

	// 创建基础仓储
	userRepo := postgres.NewUserRepository(db)
	creditRepo := postgres.NewCreditRepository(db)
	resellerRepo := postgres.NewResellerRepository(db)
	rateRepo := postgres.NewRateRepository(db)
	promotionRepo := postgres.NewPromotionRepository(db)
	orderRepo := postgres.NewOrderRepository(db)
	enterpriseRepo := postgres.NewEnterpriseRepository(db)
	esimRepo := postgres.NewESIMRepository(db)
	packageRepo := postgres.NewPackageRepository(db)
	regionRepo := postgres.NewRegionRepository(db)

	// 创建基础服务
	tokenService := jwt.NewTokenService(cfg.Auth.JWTSecret, cfg.Auth.JWTExpiration)
	userService := service.NewUserService(userRepo, creditRepo, db)
	creditService := service.NewCreditService(creditRepo)
	resellerService := service.NewResellerService(resellerRepo, userService, db)
	rateService := service.NewRateService(rateRepo)
	enterpriseService := service.NewEnterpriseService(enterpriseRepo, userRepo, esimRepo, log)
	promotionService := service.NewPromotionService(promotionRepo)

	// 初始化ESIM服务相关组件
	providerConfigManager := providers.NewProviderConfigManager()
	providerFactory := providers.NewProviderFactory(providerConfigManager)

	// 初始化配置
	providerConfigManager.InitializeDefaultConfigs()

	// 注册提供商，这里只设置基本结构，不直接使用测试中的模拟
	ctx := context.Background()
	providers.SetupProviders(ctx, providerFactory, providerConfigManager)

	// 创建webhook处理器
	webhookHandler := providers.NewWebhookHandler(esimRepo, log)

	// 创建完整的ESIMService
	esimService := service.NewESIMService(
		providerFactory,
		taskTracker,
		webhookHandler,
		packageRepo,
		esimRepo,
		regionRepo,
	)

	// 创建 orderService
	orderService := service.NewOrderService(orderRepo, userService, esimService, promotionService, db)

	// 创建中间件
	loggerMiddleware := apimiddleware.NewLoggerMiddleware(log)
	errorMiddleware := apimiddleware.NewErrorMiddleware(log)
	authMiddleware := apimiddleware.NewAuthMiddleware(userService, resellerService, tokenService, redisClient, log)
	limiterConfig := apimiddleware.DefaultRateLimiterConfig()
	rateLimiterMiddleware := apimiddleware.NewRateLimiterMiddleware(redisClient, limiterConfig, log)

	// 创建所有处理器
	userHandler := handlers.NewUserHandler(userService, tokenService)
	creditHandler := handlers.NewCreditHandler(creditService)
	resellerHandler := handlers.NewResellerHandler(resellerService)
	rateHandler := handlers.NewRateHandler(rateService)
	enterpriseHandler := handlers.NewEnterpriseHandler(enterpriseService, log)
	esimHandler := handlers.NewESIMHandler(esimService, orderService)
	orderHandler := handlers.NewOrderHandler(orderService)
	promotionHandler := handlers.NewPromotionHandler(promotionService)

	// 添加全局中间件
	e.Use(loggerMiddleware.Logger())

	// 设置全局错误处理器
	e.HTTPErrorHandler = errorMiddleware.ErrorHandler()

	// 添加全局速率限制（每分钟1000个请求）
	e.Use(rateLimiterMiddleware.GlobalRateLimit(1000))

	// 添加熔断器（5个错误后触发熔断，熔断时间30秒）
	e.Use(rateLimiterMiddleware.CircuitBreaker(5, 30*time.Second))

	// 设置监控系统
	apimiddleware.SetupMonitoring(e)

	// 设置路由
	api.SetupRouter(
		e,
		authMiddleware,
		loggerMiddleware,
		errorMiddleware,
		userHandler,
		esimHandler,
		orderHandler,
		promotionHandler,
		resellerHandler,
		enterpriseHandler,
		creditHandler,
		rateHandler,
		rateLimiterMiddleware,
	)

	// 返回应用程序
	return &App{
		Echo:                  e,
		DB:                    db,
		RedisClient:           redisClient,
		Logger:                log,
		UserService:           userService,
		CreditService:         creditService,
		RateLimiterMiddleware: rateLimiterMiddleware,
		QueryDecorator:        queryDecorator,
		Cache:                 hybridCache,
		TaskManager:           taskManager,
	}, nil
}

// 以下是简化版的创建函数，直接内联到app.go中

// CreateRepositories 创建所有仓储实例
func CreateRepositories(db *gorm.DB) *RepositoryContainer {
	return &RepositoryContainer{
		UserRepo:     NewUserRepository(db),
		CreditRepo:   NewCreditRepository(db),
		OrderRepo:    NewOrderRepository(db),
		ResellerRepo: NewResellerRepository(db),
		RateRepo:     NewRateRepository(db),
	}
}

// CreateServices 创建所有服务实例
func CreateServices(cfg *config.Config, db *gorm.DB, repos *RepositoryContainer) *ServiceContainer {
	// 创建基础服务
	tokenService := NewTokenService(cfg.Auth.JWTSecret, cfg.Auth.JWTExpiration)
	userService := NewUserService(repos.UserRepo, repos.CreditRepo, db)
	creditService := NewCreditService(repos.CreditRepo)
	resellerService := NewResellerService(repos.ResellerRepo, userService, db)
	rateService := NewRateService(repos.RateRepo)

	// 创建依赖性更复杂的服务
	esimService := (*service.ESIMService)(nil)
	orderService := (*service.OrderService)(nil)

	return &ServiceContainer{
		TokenService:    tokenService,
		UserService:     userService,
		CreditService:   creditService,
		ResellerService: resellerService,
		RateService:     rateService,
		ESIMService:     esimService,
		OrderService:    orderService,
	}
}

// CreateHandlers 创建所有处理器实例
func CreateHandlers(services *ServiceContainer) *HandlerContainer {
	// 创建基本处理器
	userHandler := NewUserHandler(services.UserService, services.TokenService)
	creditHandler := NewCreditHandler(services.CreditService)
	resellerHandler := NewResellerHandler(services.ResellerService)
	rateHandler := NewRateHandler(services.RateService)

	// 创建依赖性更复杂的处理器
	var esimHandler handlers.ESIMHandlerInterface = nil
	var orderHandler handlers.OrderHandlerInterface = nil
	var promotionHandler handlers.PromotionHandlerInterface = nil
	var enterpriseHandler handlers.EnterpriseHandlerInterface = nil

	return &HandlerContainer{
		UserHandler:       userHandler,
		CreditHandler:     creditHandler,
		ESIMHandler:       esimHandler,
		OrderHandler:      orderHandler,
		PromotionHandler:  promotionHandler,
		ResellerHandler:   resellerHandler,
		EnterpriseHandler: enterpriseHandler,
		RateHandler:       rateHandler,
	}
}

// CreateMiddlewares 创建所有中间件实例
func CreateMiddlewares(log *zap.Logger, redisClient *redisclient.Client, services *ServiceContainer) *MiddlewareContainer {
	return &MiddlewareContainer{
		LoggerMiddleware:      NewLoggerMiddleware(log),
		ErrorMiddleware:       NewErrorMiddleware(log),
		AuthMiddleware:        NewAuthMiddleware(services.UserService, services.ResellerService, services.TokenService, redisClient, log),
		RateLimiterMiddleware: NewRateLimiterMiddleware(redisClient, nil, log),
	}
}

// 定义简化的数据类型，直接在内部实现
type RepositoryContainer struct {
	UserRepo     repository.UserRepository
	CreditRepo   repository.CreditRepository
	OrderRepo    repository.OrderRepository
	ResellerRepo repository.ResellerRepository
	RateRepo     repository.RateRepository
}

type ServiceContainer struct {
	TokenService    *jwt.TokenService
	UserService     *service.UserService
	CreditService   *service.CreditService
	ResellerService *service.ResellerService
	RateService     *service.RateService
	ESIMService     *service.ESIMService
	OrderService    *service.OrderService
}

type HandlerContainer struct {
	UserHandler       handlers.UserHandlerInterface
	CreditHandler     handlers.CreditHandlerInterface
	ESIMHandler       handlers.ESIMHandlerInterface
	OrderHandler      handlers.OrderHandlerInterface
	PromotionHandler  handlers.PromotionHandlerInterface
	ResellerHandler   handlers.ResellerHandlerInterface
	EnterpriseHandler handlers.EnterpriseHandlerInterface
	RateHandler       handlers.RateHandlerInterface
}

type MiddlewareContainer struct {
	LoggerMiddleware      *apimiddleware.LoggerMiddleware
	ErrorMiddleware       *apimiddleware.ErrorMiddleware
	AuthMiddleware        *apimiddleware.AuthMiddleware
	RateLimiterMiddleware *apimiddleware.RateLimiterMiddleware
}

// 工厂函数别名，以统一命名风格
func NewDB(cfg config.DatabaseConfig) (*gorm.DB, error) {
	return postgres.NewDB(cfg)
}

func NewUserRepository(db *gorm.DB) repository.UserRepository {
	return postgres.NewUserRepository(db)
}

func NewCreditRepository(db *gorm.DB) repository.CreditRepository {
	return postgres.NewCreditRepository(db)
}

func NewOrderRepository(db *gorm.DB) repository.OrderRepository {
	return postgres.NewOrderRepository(db)
}

func NewResellerRepository(db *gorm.DB) repository.ResellerRepository {
	return postgres.NewResellerRepository(db)
}

func NewRateRepository(db *gorm.DB) repository.RateRepository {
	return postgres.NewRateRepository(db)
}

func NewTokenService(secret string, expiration time.Duration) *jwt.TokenService {
	return jwt.NewTokenService(secret, expiration)
}

func NewUserService(userRepo repository.UserRepository, creditRepo repository.CreditRepository, db *gorm.DB) *service.UserService {
	return service.NewUserService(userRepo, creditRepo, db)
}

func NewCreditService(creditRepo repository.CreditRepository) *service.CreditService {
	return service.NewCreditService(creditRepo)
}

func NewResellerService(resellerRepo repository.ResellerRepository, userService *service.UserService, db *gorm.DB) *service.ResellerService {
	return service.NewResellerService(resellerRepo, userService, db)
}

func NewRateService(rateRepo repository.RateRepository) *service.RateService {
	return service.NewRateService(rateRepo)
}

func NewUserHandler(userService *service.UserService, tokenService *jwt.TokenService) handlers.UserHandlerInterface {
	return handlers.NewUserHandler(userService, tokenService)
}

func NewCreditHandler(creditService *service.CreditService) handlers.CreditHandlerInterface {
	return handlers.NewCreditHandler(creditService)
}

func NewResellerHandler(resellerService *service.ResellerService) handlers.ResellerHandlerInterface {
	return handlers.NewResellerHandler(resellerService)
}

func NewRateHandler(rateService *service.RateService) handlers.RateHandlerInterface {
	return handlers.NewRateHandler(rateService)
}

func NewLoggerMiddleware(logger *zap.Logger) *apimiddleware.LoggerMiddleware {
	return apimiddleware.NewLoggerMiddleware(logger)
}

func NewErrorMiddleware(logger *zap.Logger) *apimiddleware.ErrorMiddleware {
	return apimiddleware.NewErrorMiddleware(logger)
}

func NewAuthMiddleware(userService *service.UserService, resellerService *service.ResellerService,
	tokenService *jwt.TokenService, redisClient *redisclient.Client, logger *zap.Logger) *apimiddleware.AuthMiddleware {
	return apimiddleware.NewAuthMiddleware(userService, resellerService, tokenService, redisClient, logger)
}

// NewRateLimiterMiddleware 创建速率限制中间件
func NewRateLimiterMiddleware(redisClient *redisclient.Client, options interface{}, logger *zap.Logger) *apimiddleware.RateLimiterMiddleware {
	// 转换配置类型或使用默认配置
	var config *apimiddleware.RateLimiterConfig
	if options != nil {
		if cfg, ok := options.(*apimiddleware.RateLimiterConfig); ok {
			config = cfg
		}
	}

	return apimiddleware.NewRateLimiterMiddleware(redisClient, config, logger)
}
