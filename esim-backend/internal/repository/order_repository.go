package repository

import (
	"context"
	"time"

	"vereal/letsesim/internal/domain/order"
)

// OrderRepository 订单存储库接口
type OrderRepository interface {
	// Create 创建订单
	Create(ctx context.Context, order *order.Order) error

	// GetByID 根据ID获取订单
	GetByID(ctx context.Context, id string) (*order.Order, error)

	// GetByTransactionID 根据交易ID获取订单
	GetByTransactionID(ctx context.Context, transactionID string) (*order.Order, error)

	// GetByProviderOrderNo 根据提供商订单号获取订单
	GetByProviderOrderNo(ctx context.Context, providerOrderNo string) (*order.Order, error)

	// Update 更新订单
	Update(ctx context.Context, order *order.Order) error

	// Delete 删除订单
	Delete(ctx context.Context, id string) error

	// List 获取订单列表
	List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*order.Order, int64, error)

	// GetUserOrders 获取用户订单
	GetUserOrders(ctx context.Context, userID string, startDate, endDate time.Time, page, pageSize int) ([]*order.Order, int64, error)

	// GetResellerOrders 获取代理商订单
	GetResellerOrders(ctx context.Context, resellerID string, startDate, endDate time.Time, page, pageSize int) ([]*order.Order, int64, error)
}
