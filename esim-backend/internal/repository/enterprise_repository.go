package repository

import (
	"context"

	"vereal/letsesim/internal/domain/enterprise"
)

// EnterpriseRepository 企业代理商存储库接口
type EnterpriseRepository interface {
	// 部门管理
	CreateDepartment(ctx context.Context, department *enterprise.Department) error
	GetDepartmentByID(ctx context.Context, id string) (*enterprise.Department, error)
	UpdateDepartment(ctx context.Context, department *enterprise.Department) error
	DeleteDepartment(ctx context.Context, id string) error
	ListDepartments(ctx context.Context, enterpriseID string, page, pageSize int) ([]*enterprise.Department, int64, error)

	// 员工管理
	CreateEmployee(ctx context.Context, employee *enterprise.Employee) error
	GetEmployeeByID(ctx context.Context, id string) (*enterprise.Employee, error)
	GetEmployeeByUserID(ctx context.Context, userID string) (*enterprise.Employee, error)
	UpdateEmployee(ctx context.Context, employee *enterprise.Employee) error
	DeleteEmployee(ctx context.Context, id string) error
	ListEmployees(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*enterprise.Employee, int64, error)

	// 员工配额管理
	CreateEmployeeQuota(ctx context.Context, quota *enterprise.EmployeeQuota) error
	GetEmployeeQuotaByID(ctx context.Context, id string) (*enterprise.EmployeeQuota, error)
	GetActiveQuotaByEmployeeID(ctx context.Context, employeeID string, quotaType enterprise.QuotaType) (*enterprise.EmployeeQuota, error)
	UpdateEmployeeQuota(ctx context.Context, quota *enterprise.EmployeeQuota) error
	DeleteEmployeeQuota(ctx context.Context, id string) error
	ListEmployeeQuotas(ctx context.Context, employeeID string, page, pageSize int) ([]*enterprise.EmployeeQuota, int64, error)
}
