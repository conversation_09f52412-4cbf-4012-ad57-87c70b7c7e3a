package postgres

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

var _ repository.UserRepository = (*UserRepository)(nil)

// UserModel 用户数据库模型
type UserModel struct {
	BaseModel
	Email          string `gorm:"uniqueIndex;not null"`
	HashedPassword string `gorm:"not null"`
	Name           string `gorm:"not null"`
	Mobile         string
	Role           string `gorm:"not null"`
	Status         string `gorm:"not null"`
	LastLoginAt    time.Time
}

// TableName 表名
func (UserModel) TableName() string {
	return "users"
}

// UserRepository 用户存储库
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户存储库
func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{
		db: db,
	}
}

// toDomain 转换为领域模型
func (m *UserModel) toDomain() *user.User {
	u := &user.User{
		ID:             m.ID,
		Email:          m.Email,
		HashedPassword: m.HashedPassword,
		Name:           m.Name,
		Mobile:         m.Mobile,
		Role:           user.Role(m.Role),
		Status:         m.Status,
		CreatedAt:      m.CreatedAt,
		UpdatedAt:      m.UpdatedAt,
		LastLoginAt:    m.LastLoginAt,
	}

	// 如果记录已被逻辑删除，设置删除时间
	if m.DeletedAt.Valid {
		deletedAt := m.DeletedAt.Time
		u.DeletedAt = &deletedAt
	}

	return u
}

// fromDomain 从领域模型转换
func fromUserDomain(u *user.User) *UserModel {
	model := &UserModel{
		Email:          u.Email,
		HashedPassword: u.HashedPassword,
		Name:           u.Name,
		Mobile:         u.Mobile,
		Role:           string(u.Role),
		Status:         u.Status,
		BaseModel: BaseModel{
			ID:        u.ID,
			CreatedAt: u.CreatedAt,
			UpdatedAt: u.UpdatedAt,
		},
		LastLoginAt: u.LastLoginAt,
	}

	// 设置删除时间（如果有）
	if u.DeletedAt != nil {
		model.DeletedAt.Time = *u.DeletedAt
		model.DeletedAt.Valid = true
	}

	return model
}

// Create 创建用户
func (r *UserRepository) Create(ctx context.Context, user *user.User) error {
	model := fromUserDomain(user)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if user.ID == "" {
		user.ID = model.ID
	}

	return result.Error
}

// GetByID 根据ID获取用户
func (r *UserRepository) GetByID(ctx context.Context, id string) (*user.User, error) {
	var model UserModel
	// GORM会自动过滤掉软删除的记录
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"User not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetByEmail 根据邮箱获取用户
func (r *UserRepository) GetByEmail(ctx context.Context, email string) (*user.User, error) {
	var model UserModel
	// GORM会自动过滤掉软删除的记录
	result := r.db.WithContext(ctx).Where("email = ?", email).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"User not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// Update 更新用户
func (r *UserRepository) Update(ctx context.Context, user *user.User) error {
	model := fromUserDomain(user)
	result := r.db.WithContext(ctx).Model(&UserModel{}).Where("id = ?", user.ID).Updates(model)
	return result.Error
}

// Delete 删除用户
func (r *UserRepository) Delete(ctx context.Context, id string) error {
	// 使用GORM的软删除功能
	result := r.db.WithContext(ctx).Delete(&UserModel{}, "id = ?", id)
	return result.Error
}

// List 获取用户列表
func (r *UserRepository) List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*user.User, int64, error) {
	var models []UserModel
	var total int64

	// GORM会自动过滤掉软删除的记录
	query := r.db.WithContext(ctx).Model(&UserModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	users := make([]*user.User, len(models))
	for i, model := range models {
		users[i] = model.toDomain()
	}

	return users, total, nil
}

// CountUsers 获取用户总数
func (r *UserRepository) CountUsers(ctx context.Context) (int64, error) {
	var count int64
	// GORM会自动过滤掉软删除的记录
	result := r.db.WithContext(ctx).Model(&UserModel{}).Count(&count)
	return count, result.Error
}
