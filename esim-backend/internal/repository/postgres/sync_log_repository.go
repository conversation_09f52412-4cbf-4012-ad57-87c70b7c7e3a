package postgres

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"vereal/letsesim/internal/repository"
	pkgesim "vereal/letsesim/pkg/esim"
)

// SyncLogModel 同步日志数据库模型
type SyncLogModel struct {
	BaseModel
	EntityType   string `gorm:"index;not null"` // package, region, esim
	ProviderType string `gorm:"index;not null"`
	SyncType     string `gorm:"not null"` // full, incremental
	StartTime    time.Time
	EndTime      time.Time
	AddedCount   int
	UpdatedCount int
	DeletedCount int
	ErrorCount   int
	ErrorDetails string
	Status       string `gorm:"index;not null"` // running, completed, failed
}

// TableName 表名
func (SyncLogModel) TableName() string {
	return "sync_logs"
}

// SyncLogRepository 同步日志存储库
type SyncLogRepository struct {
	db *gorm.DB
}

// NewSyncLogRepository 创建同步日志存储库
func NewSyncLogRepository(db *gorm.DB) *SyncLogRepository {
	return &SyncLogRepository{
		db: db,
	}
}

// toDomain 转换为领域模型
func (m *SyncLogModel) toDomain() *repository.SyncLog {
	return &repository.SyncLog{
		ID:           m.ID,
		EntityType:   m.EntityType,
		ProviderType: m.ProviderType,
		SyncType:     m.SyncType,
		StartTime:    m.StartTime,
		EndTime:      m.EndTime,
		AddedCount:   m.AddedCount,
		UpdatedCount: m.UpdatedCount,
		DeletedCount: m.DeletedCount,
		ErrorCount:   m.ErrorCount,
		ErrorDetails: m.ErrorDetails,
		Status:       m.Status,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
	}
}

// fromDomain 从领域模型转换
func fromSyncLogDomain(s *repository.SyncLog) *SyncLogModel {
	return &SyncLogModel{
		BaseModel: BaseModel{
			ID:        s.ID,
			CreatedAt: s.CreatedAt,
			UpdatedAt: s.UpdatedAt,
		},
		EntityType:   s.EntityType,
		ProviderType: s.ProviderType,
		SyncType:     s.SyncType,
		StartTime:    s.StartTime,
		EndTime:      s.EndTime,
		AddedCount:   s.AddedCount,
		UpdatedCount: s.UpdatedCount,
		DeletedCount: s.DeletedCount,
		ErrorCount:   s.ErrorCount,
		ErrorDetails: s.ErrorDetails,
		Status:       s.Status,
	}
}

// Create 创建同步日志
func (r *SyncLogRepository) Create(ctx context.Context, syncLog *repository.SyncLog) error {
	model := fromSyncLogDomain(syncLog)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if syncLog.ID == "" {
		syncLog.ID = model.ID
	}

	return result.Error
}

// Update 更新同步日志
func (r *SyncLogRepository) Update(ctx context.Context, syncLog *repository.SyncLog) error {
	model := fromSyncLogDomain(syncLog)
	result := r.db.WithContext(ctx).Model(&SyncLogModel{}).Where("id = ?", syncLog.ID).Updates(model)
	return result.Error
}

// GetByID 根据ID获取同步日志
func (r *SyncLogRepository) GetByID(ctx context.Context, id string) (*repository.SyncLog, error) {
	var model SyncLogModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"Sync log not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetLatestByEntity 获取指定实体类型的最新同步日志
func (r *SyncLogRepository) GetLatestByEntity(ctx context.Context, entityType, providerType string) (*repository.SyncLog, error) {
	var model SyncLogModel
	result := r.db.WithContext(ctx).
		Where("entity_type = ? AND provider_type = ?", entityType, providerType).
		Order("created_at DESC").
		First(&model)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"Sync log not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// List 获取同步日志列表
func (r *SyncLogRepository) List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*repository.SyncLog, int64, error) {
	var models []SyncLogModel
	var total int64

	query := r.db.WithContext(ctx).Model(&SyncLogModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	syncLogs := make([]*repository.SyncLog, len(models))
	for i, model := range models {
		syncLogs[i] = model.toDomain()
	}

	return syncLogs, total, nil
}
