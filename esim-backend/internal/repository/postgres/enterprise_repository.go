package postgres

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/enterprise"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

var _ repository.EnterpriseRepository = (*EnterpriseRepository)(nil)

// DepartmentModel 部门数据库模型
type DepartmentModel struct {
	BaseModel
	EnterpriseID string `gorm:"index;not null"`
	Name         string `gorm:"not null"`
	Description  string
}

// TableName 表名
func (DepartmentModel) TableName() string {
	return "departments"
}

// EmployeeModel 员工数据库模型
type EmployeeModel struct {
	BaseModel
	UserID       string `gorm:"uniqueIndex;not null"`
	EnterpriseID string `gorm:"index;not null"`
	DepartmentID string `gorm:"index"`
	Position     string
	EmployeeCode string
}

// TableName 表名
func (EmployeeModel) TableName() string {
	return "employees"
}

// EmployeeQuotaModel 员工配额数据库模型
type EmployeeQuotaModel struct {
	BaseModel
	EmployeeID   string `gorm:"index;not null"`
	QuotaType    string `gorm:"not null"`
	QuotaValue   int64  `gorm:"not null"`
	UsedValue    int64  `gorm:"not null"`
	RefreshCycle string
	StartDate    time.Time `gorm:"not null"`
	EndDate      time.Time
	IsActive     bool `gorm:"not null;default:true"`
}

// TableName 表名
func (EmployeeQuotaModel) TableName() string {
	return "employee_quotas"
}

// EnterpriseRepository 企业代理商存储库
type EnterpriseRepository struct {
	db *gorm.DB
}

// NewEnterpriseRepository 创建企业代理商存储库
func NewEnterpriseRepository(db *gorm.DB) *EnterpriseRepository {
	return &EnterpriseRepository{
		db: db,
	}
}

// 领域模型与数据库模型互相转换的方法

// toDomainDepartment 将部门数据库模型转换为领域模型
func (m *DepartmentModel) toDomainDepartment() *enterprise.Department {
	return &enterprise.Department{
		ID:           m.ID,
		EnterpriseID: m.EnterpriseID,
		Name:         m.Name,
		Description:  m.Description,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
	}
}

// fromDomainDepartment 将部门领域模型转换为数据库模型
func fromDomainDepartment(dept *enterprise.Department) *DepartmentModel {
	return &DepartmentModel{
		BaseModel: BaseModel{
			ID:        dept.ID,
			CreatedAt: dept.CreatedAt,
			UpdatedAt: dept.UpdatedAt,
		},
		EnterpriseID: dept.EnterpriseID,
		Name:         dept.Name,
		Description:  dept.Description,
	}
}

// toDomainEmployee 将员工数据库模型转换为领域模型
func (m *EmployeeModel) toDomainEmployee() *enterprise.Employee {
	return &enterprise.Employee{
		ID:           m.ID,
		UserID:       m.UserID,
		EnterpriseID: m.EnterpriseID,
		DepartmentID: m.DepartmentID,
		Position:     m.Position,
		EmployeeCode: m.EmployeeCode,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
	}
}

// fromDomainEmployee 将员工领域模型转换为数据库模型
func fromDomainEmployee(emp *enterprise.Employee) *EmployeeModel {
	return &EmployeeModel{
		BaseModel: BaseModel{
			ID:        emp.ID,
			CreatedAt: emp.CreatedAt,
			UpdatedAt: emp.UpdatedAt,
		},
		UserID:       emp.UserID,
		EnterpriseID: emp.EnterpriseID,
		DepartmentID: emp.DepartmentID,
		Position:     emp.Position,
		EmployeeCode: emp.EmployeeCode,
	}
}

// toDomainEmployeeQuota 将员工配额数据库模型转换为领域模型
func (m *EmployeeQuotaModel) toDomainEmployeeQuota() *enterprise.EmployeeQuota {
	return &enterprise.EmployeeQuota{
		ID:           m.ID,
		EmployeeID:   m.EmployeeID,
		QuotaType:    enterprise.QuotaType(m.QuotaType),
		QuotaValue:   m.QuotaValue,
		UsedValue:    m.UsedValue,
		RefreshCycle: m.RefreshCycle,
		StartDate:    m.StartDate,
		EndDate:      m.EndDate,
		IsActive:     m.IsActive,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
	}
}

// fromDomainEmployeeQuota 将员工配额领域模型转换为数据库模型
func fromDomainEmployeeQuota(quota *enterprise.EmployeeQuota) *EmployeeQuotaModel {
	return &EmployeeQuotaModel{
		BaseModel: BaseModel{
			ID:        quota.ID,
			CreatedAt: quota.CreatedAt,
			UpdatedAt: quota.UpdatedAt,
		},
		EmployeeID:   quota.EmployeeID,
		QuotaType:    string(quota.QuotaType),
		QuotaValue:   quota.QuotaValue,
		UsedValue:    quota.UsedValue,
		RefreshCycle: quota.RefreshCycle,
		StartDate:    quota.StartDate,
		EndDate:      quota.EndDate,
		IsActive:     quota.IsActive,
	}
}

// 部门管理方法实现

// CreateDepartment 创建部门
func (r *EnterpriseRepository) CreateDepartment(ctx context.Context, department *enterprise.Department) error {
	if department.ID == "" {
		department.ID = uuid.New().String()
	}

	model := fromDomainDepartment(department)
	result := r.db.WithContext(ctx).Create(model)
	return result.Error
}

// GetDepartmentByID 根据ID获取部门
func (r *EnterpriseRepository) GetDepartmentByID(ctx context.Context, id string) (*enterprise.Department, error) {
	var model DepartmentModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Department not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainDepartment(), nil
}

// UpdateDepartment 更新部门
func (r *EnterpriseRepository) UpdateDepartment(ctx context.Context, department *enterprise.Department) error {
	model := fromDomainDepartment(department)
	result := r.db.WithContext(ctx).Model(&DepartmentModel{}).Where("id = ?", department.ID).Updates(model)
	return result.Error
}

// DeleteDepartment 删除部门
func (r *EnterpriseRepository) DeleteDepartment(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).Delete(&DepartmentModel{}, "id = ?", id)
	return result.Error
}

// ListDepartments 获取部门列表
func (r *EnterpriseRepository) ListDepartments(ctx context.Context, enterpriseID string, page, pageSize int) ([]*enterprise.Department, int64, error) {
	var models []DepartmentModel
	var total int64

	query := r.db.WithContext(ctx).Model(&DepartmentModel{}).Where("enterprise_id = ?", enterpriseID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	departments := make([]*enterprise.Department, len(models))
	for i, model := range models {
		departments[i] = model.toDomainDepartment()
	}

	return departments, total, nil
}

// 员工管理方法实现

// CreateEmployee 创建员工
func (r *EnterpriseRepository) CreateEmployee(ctx context.Context, employee *enterprise.Employee) error {
	if employee.ID == "" {
		employee.ID = uuid.New().String()
	}

	model := fromDomainEmployee(employee)
	result := r.db.WithContext(ctx).Create(model)
	return result.Error
}

// GetEmployeeByID 根据ID获取员工
func (r *EnterpriseRepository) GetEmployeeByID(ctx context.Context, id string) (*enterprise.Employee, error) {
	var model EmployeeModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Employee not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainEmployee(), nil
}

// GetEmployeeByUserID 根据用户ID获取员工
func (r *EnterpriseRepository) GetEmployeeByUserID(ctx context.Context, userID string) (*enterprise.Employee, error) {
	var model EmployeeModel
	result := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Employee not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainEmployee(), nil
}

// UpdateEmployee 更新员工
func (r *EnterpriseRepository) UpdateEmployee(ctx context.Context, employee *enterprise.Employee) error {
	model := fromDomainEmployee(employee)
	result := r.db.WithContext(ctx).Model(&EmployeeModel{}).Where("id = ?", employee.ID).Updates(model)
	return result.Error
}

// DeleteEmployee 删除员工
func (r *EnterpriseRepository) DeleteEmployee(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).Delete(&EmployeeModel{}, "id = ?", id)
	return result.Error
}

// ListEmployees 获取员工列表
func (r *EnterpriseRepository) ListEmployees(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*enterprise.Employee, int64, error) {
	var models []EmployeeModel
	var total int64

	query := r.db.WithContext(ctx).Model(&EmployeeModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	employees := make([]*enterprise.Employee, len(models))
	for i, model := range models {
		employees[i] = model.toDomainEmployee()
	}

	return employees, total, nil
}

// 员工配额管理方法实现

// CreateEmployeeQuota 创建员工配额
func (r *EnterpriseRepository) CreateEmployeeQuota(ctx context.Context, quota *enterprise.EmployeeQuota) error {
	if quota.ID == "" {
		quota.ID = uuid.New().String()
	}

	model := fromDomainEmployeeQuota(quota)
	result := r.db.WithContext(ctx).Create(model)
	return result.Error
}

// GetEmployeeQuotaByID 根据ID获取员工配额
func (r *EnterpriseRepository) GetEmployeeQuotaByID(ctx context.Context, id string) (*enterprise.EmployeeQuota, error) {
	var model EmployeeQuotaModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Employee quota not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainEmployeeQuota(), nil
}

// GetActiveQuotaByEmployeeID 获取员工当前活动的配额
func (r *EnterpriseRepository) GetActiveQuotaByEmployeeID(ctx context.Context, employeeID string, quotaType enterprise.QuotaType) (*enterprise.EmployeeQuota, error) {
	var model EmployeeQuotaModel
	now := time.Now()

	result := r.db.WithContext(ctx).Where(
		"employee_id = ? AND quota_type = ? AND is_active = ? AND start_date <= ? AND end_date > ?",
		employeeID, string(quotaType), true, now, now,
	).First(&model)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Active employee quota not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainEmployeeQuota(), nil
}

// UpdateEmployeeQuota 更新员工配额
func (r *EnterpriseRepository) UpdateEmployeeQuota(ctx context.Context, quota *enterprise.EmployeeQuota) error {
	model := fromDomainEmployeeQuota(quota)
	result := r.db.WithContext(ctx).Model(&EmployeeQuotaModel{}).Where("id = ?", quota.ID).Updates(model)
	return result.Error
}

// DeleteEmployeeQuota 删除员工配额
func (r *EnterpriseRepository) DeleteEmployeeQuota(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).Delete(&EmployeeQuotaModel{}, "id = ?", id)
	return result.Error
}

// ListEmployeeQuotas 获取员工配额列表
func (r *EnterpriseRepository) ListEmployeeQuotas(ctx context.Context, employeeID string, page, pageSize int) ([]*enterprise.EmployeeQuota, int64, error) {
	var models []EmployeeQuotaModel
	var total int64

	query := r.db.WithContext(ctx).Model(&EmployeeQuotaModel{}).Where("employee_id = ?", employeeID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	quotas := make([]*enterprise.EmployeeQuota, len(models))
	for i, model := range models {
		quotas[i] = model.toDomainEmployeeQuota()
	}

	return quotas, total, nil
}
