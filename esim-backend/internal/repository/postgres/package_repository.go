package postgres

import (
	"context"
	"errors"
	"strings"
	"time"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/esim"
	pkgesim "vereal/letsesim/pkg/esim"
)

// PackageModel 套餐数据库模型
type PackageModel struct {
	BaseModel
	ProviderType  string `gorm:"index;not null"`
	ExternalID    string `gorm:"uniqueIndex:idx_provider_external_id;not null"`
	Name          string `gorm:"not null"`
	Description   string
	Price         float64 `gorm:"not null"`
	Currency      string  `gorm:"not null"`
	DataVolume    int64   `gorm:"not null"`
	ValidityDays  int     `gorm:"not null"`
	LocationCodes string
	SupportsSMS   bool `gorm:"not null;default:false"`
	DataType      string
	NetworkTypes  string
	SupportTopUp  bool   `gorm:"not null;default:false"`
	Status        string `gorm:"index;not null;default:'ACTIVE'"`
	LastSyncAt    time.Time
}

// TableName 表名
func (PackageModel) TableName() string {
	return "packages"
}

// PackageRepository 套餐存储库
type PackageRepository struct {
	db *gorm.DB
}

// NewPackageRepository 创建套餐存储库
func NewPackageRepository(db *gorm.DB) *PackageRepository {
	return &PackageRepository{
		db: db,
	}
}

// toDomain 转换为领域模型
func (m *PackageModel) toDomain() *esim.Package {
	// 处理字符串分割
	var locationCodes []string
	if m.LocationCodes != "" {
		locationCodes = strings.Split(m.LocationCodes, ",")
	}

	var networkTypes []string
	if m.NetworkTypes != "" {
		networkTypes = strings.Split(m.NetworkTypes, ",")
	}

	return &esim.Package{
		ID:            m.ID,
		ProviderType:  m.ProviderType,
		ExternalID:    m.ExternalID,
		Name:          m.Name,
		Description:   m.Description,
		Price:         m.Price,
		Currency:      m.Currency,
		DataVolume:    m.DataVolume,
		ValidityDays:  m.ValidityDays,
		LocationCodes: locationCodes,
		SupportsSMS:   m.SupportsSMS,
		DataType:      m.DataType,
		NetworkTypes:  networkTypes,
		SupportTopUp:  m.SupportTopUp,
		Status:        esim.PackageStatus(m.Status),
		CreatedAt:     m.CreatedAt,
		UpdatedAt:     m.UpdatedAt,
		LastSyncAt:    m.LastSyncAt,
	}
}

// fromDomain 从领域模型转换
func fromPackageDomain(p *esim.Package) *PackageModel {
	// 处理数组合并
	var locationCodes string
	if len(p.LocationCodes) > 0 {
		locationCodes = strings.Join(p.LocationCodes, ",")
	}

	var networkTypes string
	if len(p.NetworkTypes) > 0 {
		networkTypes = strings.Join(p.NetworkTypes, ",")
	}

	return &PackageModel{
		BaseModel: BaseModel{
			ID:        p.ID,
			CreatedAt: p.CreatedAt,
			UpdatedAt: p.UpdatedAt,
		},
		ProviderType:  p.ProviderType,
		ExternalID:    p.ExternalID,
		Name:          p.Name,
		Description:   p.Description,
		Price:         p.Price,
		Currency:      p.Currency,
		DataVolume:    p.DataVolume,
		ValidityDays:  p.ValidityDays,
		LocationCodes: locationCodes,
		SupportsSMS:   p.SupportsSMS,
		DataType:      p.DataType,
		NetworkTypes:  networkTypes,
		SupportTopUp:  p.SupportTopUp,
		Status:        string(p.Status),
		LastSyncAt:    p.LastSyncAt,
	}
}

// Create 创建套餐
func (r *PackageRepository) Create(ctx context.Context, pkg *esim.Package) error {
	model := fromPackageDomain(pkg)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if pkg.ID == "" {
		pkg.ID = model.ID
	}

	return result.Error
}

// GetByID 根据ID获取套餐
func (r *PackageRepository) GetByID(ctx context.Context, id string) (*esim.Package, error) {
	var model PackageModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"Package not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetByExternalID 根据提供商套餐ID获取套餐
func (r *PackageRepository) GetByExternalID(ctx context.Context, providerType, externalID string) (*esim.Package, error) {
	var model PackageModel
	result := r.db.WithContext(ctx).Where("provider_type = ? AND external_id = ?", providerType, externalID).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"Package not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// Update 更新套餐
func (r *PackageRepository) Update(ctx context.Context, pkg *esim.Package) error {
	model := fromPackageDomain(pkg)
	result := r.db.WithContext(ctx).Model(&PackageModel{}).Where("id = ?", pkg.ID).Updates(model)
	return result.Error
}

// ListPackages 获取套餐列表，支持条件过滤
func (r *PackageRepository) ListPackages(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Package, int64, error) {
	var models []PackageModel
	var total int64

	query := r.db.WithContext(ctx).Model(&PackageModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	packages := make([]*esim.Package, len(models))
	for i, model := range models {
		packages[i] = model.toDomain()
	}

	return packages, total, nil
}

// ListByProvider 获取指定提供商的所有套餐
func (r *PackageRepository) ListByProvider(ctx context.Context, providerType string) ([]*esim.Package, error) {
	var models []PackageModel
	result := r.db.WithContext(ctx).Where("provider_type = ?", providerType).Find(&models)
	if result.Error != nil {
		return nil, result.Error
	}

	// 转换为领域模型
	packages := make([]*esim.Package, len(models))
	for i, model := range models {
		packages[i] = model.toDomain()
	}

	return packages, nil
}

// ListActive 获取活跃套餐列表
func (r *PackageRepository) ListActive(ctx context.Context, providerType string, page, pageSize int) ([]*esim.Package, int64, error) {
	var models []PackageModel
	var total int64

	query := r.db.WithContext(ctx).Model(&PackageModel{}).
		Where("provider_type = ? AND status = ?", providerType, string(esim.PackageStatusActive))

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	packages := make([]*esim.Package, len(models))
	for i, model := range models {
		packages[i] = model.toDomain()
	}

	return packages, total, nil
}
