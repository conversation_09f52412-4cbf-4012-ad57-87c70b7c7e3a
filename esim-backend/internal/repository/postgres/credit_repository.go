package postgres

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/credit"
	"vereal/letsesim/pkg/esim"
)

// CreditModel 用户积分数据库模型
type CreditModel struct {
	BaseModel
	UserID      string  `gorm:"uniqueIndex;not null"`
	Balance     float64 `gorm:"not null;default:0"`
	Currency    string  `gorm:"not null;default:'USD'"`
	LastUpdated time.Time
}

// TableName 表名
func (CreditModel) TableName() string {
	return "credits"
}

// TransactionModel 积分交易记录数据库模型
type TransactionModel struct {
	BaseModel
	UserID      string  `gorm:"index;not null"`
	Amount      float64 `gorm:"not null"`
	Balance     float64 `gorm:"not null"`
	Type        string  `gorm:"not null"`
	Description string
	OrderID     string `gorm:"index"`
	Status      string `gorm:"not null;default:'COMPLETED'"`
}

// TableName 表名
func (TransactionModel) TableName() string {
	return "credit_transactions"
}

// CreditRepository 用户积分存储库
type CreditRepository struct {
	db *gorm.DB
}

// NewCreditRepository 创建用户积分存储库
func NewCreditRepository(db *gorm.DB) *CreditRepository {
	return &CreditRepository{
		db: db,
	}
}

// toDomain 将数据库模型转换为领域模型
func (m *CreditModel) toDomain() *credit.Credit {
	return &credit.Credit{
		ID:          m.ID,
		UserID:      m.UserID,
		Balance:     m.Balance,
		Currency:    m.Currency,
		LastUpdated: m.LastUpdated,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
	}
}

// fromDomain 将领域模型转换为数据库模型
func fromCreditDomain(c *credit.Credit) *CreditModel {
	return &CreditModel{
		BaseModel: BaseModel{
			ID:        c.ID,
			CreatedAt: c.CreatedAt,
			UpdatedAt: c.UpdatedAt,
		},
		UserID:      c.UserID,
		Balance:     c.Balance,
		Currency:    c.Currency,
		LastUpdated: c.LastUpdated,
	}
}

// toDomain 将交易数据库模型转换为领域模型
func (m *TransactionModel) toDomain() *credit.Transaction {
	return &credit.Transaction{
		ID:          m.ID,
		UserID:      m.UserID,
		Amount:      m.Amount,
		Balance:     m.Balance,
		Type:        credit.TransactionType(m.Type),
		Description: m.Description,
		OrderID:     m.OrderID,
		Status:      credit.Status(m.Status),
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
	}
}

// fromDomain 将交易领域模型转换为数据库模型
func fromTransactionDomain(t *credit.Transaction) *TransactionModel {
	return &TransactionModel{
		BaseModel: BaseModel{
			ID:        t.ID,
			CreatedAt: t.CreatedAt,
			UpdatedAt: t.UpdatedAt,
		},
		UserID:      t.UserID,
		Amount:      t.Amount,
		Balance:     t.Balance,
		Type:        string(t.Type),
		Description: t.Description,
		OrderID:     t.OrderID,
		Status:      string(t.Status),
	}
}

// Create 创建用户积分
func (r *CreditRepository) Create(ctx context.Context, credit *credit.Credit) error {
	model := fromCreditDomain(credit)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if credit.ID == "" {
		credit.ID = model.ID
	}

	return result.Error
}

// GetByUserID 根据用户ID获取积分
func (r *CreditRepository) GetByUserID(ctx context.Context, userID string) (*credit.Credit, error) {
	var model CreditModel
	result := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Credit not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// Update 更新积分
func (r *CreditRepository) Update(ctx context.Context, credit *credit.Credit) error {
	model := fromCreditDomain(credit)
	result := r.db.WithContext(ctx).Model(&CreditModel{}).Where("id = ?", credit.ID).Updates(model)
	return result.Error
}

// UpdateBalance 更新余额
func (r *CreditRepository) UpdateBalance(ctx context.Context, userID string, amount float64) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var model CreditModel
		result := tx.Where("user_id = ?", userID).First(&model)
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return esim.NewESIMError(
				esim.ErrNotFound,
				"Credit not found",
				nil,
				"",
			)
		}

		if result.Error != nil {
			return result.Error
		}

		// 更新余额
		model.Balance += amount
		model.LastUpdated = time.Now()
		model.UpdatedAt = time.Now()

		return tx.Save(&model).Error
	})
}

// CreateTransaction 创建交易记录
func (r *CreditRepository) CreateTransaction(ctx context.Context, transaction *credit.Transaction) error {
	model := fromTransactionDomain(transaction)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if transaction.ID == "" {
		transaction.ID = model.ID
	}

	return result.Error
}

// GetTransactionByID 根据ID获取交易记录
func (r *CreditRepository) GetTransactionByID(ctx context.Context, id string) (*credit.Transaction, error) {
	var model TransactionModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Transaction not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// UpdateTransaction 更新交易记录
func (r *CreditRepository) UpdateTransaction(ctx context.Context, transaction *credit.Transaction) error {
	model := fromTransactionDomain(transaction)
	result := r.db.WithContext(ctx).Model(&TransactionModel{}).Where("id = ?", transaction.ID).Updates(model)
	return result.Error
}

// ListTransactions 获取用户交易记录列表
func (r *CreditRepository) ListTransactions(ctx context.Context, userID string, page, pageSize int) ([]*credit.Transaction, int64, error) {
	var models []TransactionModel
	var total int64

	query := r.db.WithContext(ctx).Model(&TransactionModel{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	transactions := make([]*credit.Transaction, len(models))
	for i, model := range models {
		transactions[i] = model.toDomain()
	}

	return transactions, total, nil
}

// GetTransactionsByOrderID 根据订单ID获取交易记录
func (r *CreditRepository) GetTransactionsByOrderID(ctx context.Context, orderID string) ([]*credit.Transaction, error) {
	var models []TransactionModel
	result := r.db.WithContext(ctx).Where("order_id = ?", orderID).Find(&models)
	if result.Error != nil {
		return nil, result.Error
	}

	// 转换为领域模型
	transactions := make([]*credit.Transaction, len(models))
	for i, model := range models {
		transactions[i] = model.toDomain()
	}

	return transactions, nil
}
