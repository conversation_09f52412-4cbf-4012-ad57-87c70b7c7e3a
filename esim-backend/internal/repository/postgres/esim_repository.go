package postgres

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/esim"
	pkgesim "vereal/letsesim/pkg/esim"
)

// ESIMModel eSIM数据库模型
type ESIMModel struct {
	BaseModel
	UserID          string `gorm:"index;not null"`
	OrderID         string `gorm:"index;not null"`
	ProviderType    string `gorm:"index;not null"`
	ICCID           string `gorm:"index"`
	ESIMTranNo      string `gorm:"index"`
	ExternalOrderNo string `gorm:"index"`
	ActivationCode  string
	QRCodeURL       string
	Status          string `gorm:"index;not null"`
	ValidityDays    int
	ExpiryTime      time.Time `gorm:"index"`
	SupportsSMS     bool      `gorm:"not null;default:false"`
	MSISDN          string
	SMSAPIOnly      bool `gorm:"not null;default:true"`
	DataPackageInfo string
	LastSyncAt      time.Time
}

// TableName 表名
func (ESIMModel) TableName() string {
	return "esims"
}

// ESIMUsageModel eSIM使用情况数据库模型
type ESIMUsageModel struct {
	BaseModel
	ESIMID           string    `gorm:"index;not null"`
	DataUsed         int64     `gorm:"not null"`
	DataTotal        int64     `gorm:"not null"`
	DataRemaining    int64     `gorm:"not null"`
	ValidDays        int       `gorm:"not null"`
	DaysUsed         int       `gorm:"not null"`
	DaysRemaining    int       `gorm:"not null"`
	LastUpdateTime   time.Time `gorm:"not null"`
	NextUpdateTime   time.Time `gorm:"not null"`
	CurrentProvider  string
	CurrentCountry   string
	ConnectionStatus string
}

// TableName 表名
func (ESIMUsageModel) TableName() string {
	return "esim_usages"
}

// ESIMRepository eSIM存储库
type ESIMRepository struct {
	db *gorm.DB
}

// NewESIMRepository 创建eSIM存储库
func NewESIMRepository(db *gorm.DB) *ESIMRepository {
	return &ESIMRepository{
		db: db,
	}
}

// toDomain 转换为领域模型
func (m *ESIMModel) toDomain() *esim.ESIM {
	return &esim.ESIM{
		ID:              m.ID,
		UserID:          m.UserID,
		OrderID:         m.OrderID,
		ProviderType:    m.ProviderType,
		ICCID:           m.ICCID,
		ESIMTranNo:      m.ESIMTranNo,
		ExternalOrderNo: m.ExternalOrderNo,
		ActivationCode:  m.ActivationCode,
		QRCodeURL:       m.QRCodeURL,
		Status:          esim.Status(m.Status),
		ValidityDays:    m.ValidityDays,
		ExpiryTime:      m.ExpiryTime,
		SupportsSMS:     m.SupportsSMS,
		MSISDN:          m.MSISDN,
		SMSAPIOnly:      m.SMSAPIOnly,
		DataPackageInfo: m.DataPackageInfo,
		CreatedAt:       m.CreatedAt,
		UpdatedAt:       m.UpdatedAt,
		LastSyncAt:      m.LastSyncAt,
	}
}

// fromDomain 从领域模型转换
func fromESIMDomain(e *esim.ESIM) *ESIMModel {
	return &ESIMModel{
		BaseModel: BaseModel{
			ID:        e.ID,
			CreatedAt: e.CreatedAt,
			UpdatedAt: e.UpdatedAt,
		},
		UserID:          e.UserID,
		OrderID:         e.OrderID,
		ProviderType:    e.ProviderType,
		ICCID:           e.ICCID,
		ESIMTranNo:      e.ESIMTranNo,
		ExternalOrderNo: e.ExternalOrderNo,
		ActivationCode:  e.ActivationCode,
		QRCodeURL:       e.QRCodeURL,
		Status:          string(e.Status),
		ValidityDays:    e.ValidityDays,
		ExpiryTime:      e.ExpiryTime,
		SupportsSMS:     e.SupportsSMS,
		MSISDN:          e.MSISDN,
		SMSAPIOnly:      e.SMSAPIOnly,
		DataPackageInfo: e.DataPackageInfo,
		LastSyncAt:      e.LastSyncAt,
	}
}

// Create 创建eSIM
func (r *ESIMRepository) Create(ctx context.Context, esimData *esim.ESIM) error {
	model := fromESIMDomain(esimData)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if esimData.ID == "" {
		esimData.ID = model.ID
	}

	return result.Error
}

// CreateESIM 创建eSIM (实现接口)
func (r *ESIMRepository) CreateESIM(ctx context.Context, esimData *esim.ESIM) error {
	// 调用已实现的Create方法
	return r.Create(ctx, esimData)
}

// GetByID 根据ID获取eSIM
func (r *ESIMRepository) GetByID(ctx context.Context, id string) (*esim.ESIM, error) {
	var model ESIMModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"ESIM not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetESIMByID 根据ID获取eSIM（实现接口）
func (r *ESIMRepository) GetESIMByID(ctx context.Context, id string) (*esim.ESIM, error) {
	return r.GetByID(ctx, id)
}

// GetByICCID 根据ICCID获取eSIM
func (r *ESIMRepository) GetByICCID(ctx context.Context, iccid string) (*esim.ESIM, error) {
	var model ESIMModel
	result := r.db.WithContext(ctx).Where("iccid = ?", iccid).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"ESIM not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetESIMByICCID 根据ICCID获取eSIM（实现接口）
func (r *ESIMRepository) GetESIMByICCID(ctx context.Context, iccid string) (*esim.ESIM, error) {
	return r.GetByICCID(ctx, iccid)
}

// GetByESIMTranNo 根据ESIMTranNo获取eSIM
func (r *ESIMRepository) GetByESIMTranNo(ctx context.Context, esimTranNo string) (*esim.ESIM, error) {
	var model ESIMModel
	result := r.db.WithContext(ctx).Where("esim_tran_no = ?", esimTranNo).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"ESIM not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetESIMByTranNo 根据TranNo获取eSIM（实现接口）
func (r *ESIMRepository) GetESIMByTranNo(ctx context.Context, tranNo string) (*esim.ESIM, error) {
	return r.GetByESIMTranNo(ctx, tranNo)
}

// GetByExternalOrderNo 根据提供商订单号获取eSIM
func (r *ESIMRepository) GetByExternalOrderNo(ctx context.Context, externalOrderNo string) (*esim.ESIM, error) {
	var model ESIMModel
	result := r.db.WithContext(ctx).Where("external_order_no = ?", externalOrderNo).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"ESIM not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// Update 更新eSIM
func (r *ESIMRepository) Update(ctx context.Context, esimData *esim.ESIM) error {
	model := fromESIMDomain(esimData)
	result := r.db.WithContext(ctx).Model(&ESIMModel{}).Where("id = ?", esimData.ID).Updates(model)
	return result.Error
}

// UpdateESIM 更新eSIM（实现接口）
func (r *ESIMRepository) UpdateESIM(ctx context.Context, esimData *esim.ESIM) error {
	return r.Update(ctx, esimData)
}

// GetUserESIMs 获取用户的eSIM列表
func (r *ESIMRepository) GetUserESIMs(ctx context.Context, userID string, page, pageSize int) ([]*esim.ESIM, int64, error) {
	var models []ESIMModel
	var total int64

	query := r.db.WithContext(ctx).Model(&ESIMModel{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	esims := make([]*esim.ESIM, len(models))
	for i, model := range models {
		esims[i] = model.toDomain()
	}

	return esims, total, nil
}

// GetByOrderID 根据订单ID获取eSIM
func (r *ESIMRepository) GetByOrderID(ctx context.Context, orderID string) ([]*esim.ESIM, error) {
	var models []ESIMModel
	result := r.db.WithContext(ctx).Where("order_id = ?", orderID).Find(&models)
	if result.Error != nil {
		return nil, result.Error
	}

	// 转换为领域模型
	esims := make([]*esim.ESIM, len(models))
	for i, model := range models {
		esims[i] = model.toDomain()
	}

	return esims, nil
}

// ListESIMs 获取eSIM列表，支持多条件过滤
func (r *ESIMRepository) ListESIMs(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.ESIM, int64, error) {
	var models []ESIMModel
	var total int64

	query := r.db.WithContext(ctx).Model(&ESIMModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	esims := make([]*esim.ESIM, len(models))
	for i, model := range models {
		esims[i] = model.toDomain()
	}

	return esims, total, nil
}

// GetExpiredESIMs 获取已过期的eSIM
func (r *ESIMRepository) GetExpiredESIMs(ctx context.Context, before time.Time, limit int) ([]*esim.ESIM, error) {
	var models []ESIMModel
	result := r.db.WithContext(ctx).Where("expiry_time < ? AND status != ?", before, string(esim.StatusExpired)).
		Limit(limit).Find(&models)
	if result.Error != nil {
		return nil, result.Error
	}

	// 转换为领域模型
	esims := make([]*esim.ESIM, len(models))
	for i, model := range models {
		esims[i] = model.toDomain()
	}

	return esims, nil
}

// toDomainESIMUsage 将数据库模型转换为领域模型
func (m *ESIMUsageModel) toDomainESIMUsage() *esim.ESIMUsage {
	return &esim.ESIMUsage{
		ID:               m.ID,
		ESIMID:           m.ESIMID,
		DataUsed:         m.DataUsed,
		DataTotal:        m.DataTotal,
		DataRemaining:    m.DataRemaining,
		ValidDays:        m.ValidDays,
		DaysUsed:         m.DaysUsed,
		DaysRemaining:    m.DaysRemaining,
		LastUpdateTime:   m.LastUpdateTime,
		NextUpdateTime:   m.NextUpdateTime,
		CurrentProvider:  m.CurrentProvider,
		CurrentCountry:   m.CurrentCountry,
		ConnectionStatus: m.ConnectionStatus,
		CreatedAt:        m.CreatedAt,
		UpdatedAt:        m.UpdatedAt,
	}
}

// fromDomainESIMUsage 将领域模型转换为数据库模型
func fromDomainESIMUsage(u *esim.ESIMUsage) *ESIMUsageModel {
	return &ESIMUsageModel{
		BaseModel: BaseModel{
			ID:        u.ID,
			CreatedAt: u.CreatedAt,
			UpdatedAt: u.UpdatedAt,
		},
		ESIMID:           u.ESIMID,
		DataUsed:         u.DataUsed,
		DataTotal:        u.DataTotal,
		DataRemaining:    u.DataRemaining,
		ValidDays:        u.ValidDays,
		DaysUsed:         u.DaysUsed,
		DaysRemaining:    u.DaysRemaining,
		LastUpdateTime:   u.LastUpdateTime,
		NextUpdateTime:   u.NextUpdateTime,
		CurrentProvider:  u.CurrentProvider,
		CurrentCountry:   u.CurrentCountry,
		ConnectionStatus: u.ConnectionStatus,
	}
}

// DeleteESIM 删除eSIM（实现接口）
func (r *ESIMRepository) DeleteESIM(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&ESIMModel{}, "id = ?", id).Error
}

// ListESIMsByUserID 获取用户的eSIM列表（实现接口）
func (r *ESIMRepository) ListESIMsByUserID(ctx context.Context, userID string, status []esim.Status, page, pageSize int) ([]*esim.ESIM, int64, error) {
	var total int64
	var models []ESIMModel

	query := r.db.WithContext(ctx).Model(&ESIMModel{}).Where("user_id = ?", userID)

	// 如果传入了状态过滤
	if len(status) > 0 {
		statusStrings := make([]string, len(status))
		for i, s := range status {
			statusStrings[i] = string(s)
		}
		query = query.Where("status IN ?", statusStrings)
	}

	// 获取总记录数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, 0, err
	}

	// 转换为领域模型
	esims := make([]*esim.ESIM, len(models))
	for i, model := range models {
		esims[i] = model.toDomain()
	}

	return esims, total, nil
}

// CreateESIMUsage 创建eSIM使用情况（实现接口）
func (r *ESIMRepository) CreateESIMUsage(ctx context.Context, usage *esim.ESIMUsage) error {
	model := fromDomainESIMUsage(usage)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if usage.ID == "" {
		usage.ID = model.ID
	}

	return result.Error
}

// GetESIMUsageByID 根据ID获取eSIM使用情况（实现接口）
func (r *ESIMRepository) GetESIMUsageByID(ctx context.Context, id string) (*esim.ESIMUsage, error) {
	var model ESIMUsageModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"ESIM usage not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainESIMUsage(), nil
}

// GetESIMUsageByESIMID 根据ESIMID获取eSIM使用情况（实现接口）
func (r *ESIMRepository) GetESIMUsageByESIMID(ctx context.Context, esimID string) (*esim.ESIMUsage, error) {
	var model ESIMUsageModel
	result := r.db.WithContext(ctx).Where("esim_id = ?", esimID).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"ESIM usage not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainESIMUsage(), nil
}

// UpdateESIMUsage 更新eSIM使用情况（实现接口）
func (r *ESIMRepository) UpdateESIMUsage(ctx context.Context, usage *esim.ESIMUsage) error {
	model := fromDomainESIMUsage(usage)
	result := r.db.WithContext(ctx).Model(&ESIMUsageModel{}).Where("id = ?", usage.ID).Updates(model)
	return result.Error
}

// DeleteESIMUsage 删除eSIM使用情况（实现接口）
func (r *ESIMRepository) DeleteESIMUsage(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&ESIMUsageModel{}, "id = ?", id).Error
}

// CreatePackage 创建套餐（实现接口）
func (r *ESIMRepository) CreatePackage(ctx context.Context, pkg *esim.Package) error {
	// 这个方法应该放在PackageRepository中，但为了满足接口我们先简单实现
	return pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in PackageRepository",
		nil,
		"",
	)
}

// GetPackageByID 根据ID获取套餐（实现接口）
func (r *ESIMRepository) GetPackageByID(ctx context.Context, id string) (*esim.Package, error) {
	// 这个方法应该放在PackageRepository中，但为了满足接口我们先简单实现
	return nil, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in PackageRepository",
		nil,
		"",
	)
}

// GetPackageByExternalID 根据外部ID获取套餐（实现接口）
func (r *ESIMRepository) GetPackageByExternalID(ctx context.Context, providerType, externalID string) (*esim.Package, error) {
	// 这个方法应该放在PackageRepository中，但为了满足接口我们先简单实现
	return nil, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in PackageRepository",
		nil,
		"",
	)
}

// UpdatePackage 更新套餐（实现接口）
func (r *ESIMRepository) UpdatePackage(ctx context.Context, pkg *esim.Package) error {
	// 这个方法应该放在PackageRepository中，但为了满足接口我们先简单实现
	return pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in PackageRepository",
		nil,
		"",
	)
}

// DeletePackage 删除套餐（实现接口）
func (r *ESIMRepository) DeletePackage(ctx context.Context, id string) error {
	// 这个方法应该放在PackageRepository中，但为了满足接口我们先简单实现
	return pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in PackageRepository",
		nil,
		"",
	)
}

// ListPackages 列出套餐（实现接口）
func (r *ESIMRepository) ListPackages(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Package, int64, error) {
	// 这个方法应该放在PackageRepository中，但为了满足接口我们先简单实现
	return nil, 0, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in PackageRepository",
		nil,
		"",
	)
}

// ListPackagesByLocation 根据地区列出套餐（实现接口）
func (r *ESIMRepository) ListPackagesByLocation(ctx context.Context, locationCode string, page, pageSize int) ([]*esim.Package, int64, error) {
	// 这个方法应该放在PackageRepository中，但为了满足接口我们先简单实现
	return nil, 0, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in PackageRepository",
		nil,
		"",
	)
}

// CreateRegion 创建区域（实现接口）
func (r *ESIMRepository) CreateRegion(ctx context.Context, region *esim.Region) error {
	// 这个方法应该放在RegionRepository中，但为了满足接口我们先简单实现
	return pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in RegionRepository",
		nil,
		"",
	)
}

// GetRegionByID 根据ID获取区域（实现接口）
func (r *ESIMRepository) GetRegionByID(ctx context.Context, id string) (*esim.Region, error) {
	// 这个方法应该放在RegionRepository中，但为了满足接口我们先简单实现
	return nil, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in RegionRepository",
		nil,
		"",
	)
}

// GetRegionByCode 根据代码获取区域（实现接口）
func (r *ESIMRepository) GetRegionByCode(ctx context.Context, providerType, code string) (*esim.Region, error) {
	// 这个方法应该放在RegionRepository中，但为了满足接口我们先简单实现
	return nil, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in RegionRepository",
		nil,
		"",
	)
}

// UpdateRegion 更新区域（实现接口）
func (r *ESIMRepository) UpdateRegion(ctx context.Context, region *esim.Region) error {
	// 这个方法应该放在RegionRepository中，但为了满足接口我们先简单实现
	return pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in RegionRepository",
		nil,
		"",
	)
}

// DeleteRegion 删除区域（实现接口）
func (r *ESIMRepository) DeleteRegion(ctx context.Context, id string) error {
	// 这个方法应该放在RegionRepository中，但为了满足接口我们先简单实现
	return pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in RegionRepository",
		nil,
		"",
	)
}

// ListRegions 列出区域（实现接口）
func (r *ESIMRepository) ListRegions(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Region, int64, error) {
	// 这个方法应该放在RegionRepository中，但为了满足接口我们先简单实现
	return nil, 0, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in RegionRepository",
		nil,
		"",
	)
}

// ListRegionsByType 根据类型列出区域（实现接口）
func (r *ESIMRepository) ListRegionsByType(ctx context.Context, regionType esim.RegionType, page, pageSize int) ([]*esim.Region, int64, error) {
	// 这个方法应该放在RegionRepository中，但为了满足接口我们先简单实现
	return nil, 0, pkgesim.NewESIMError(
		pkgesim.ErrNotImplemented,
		"This method should be in RegionRepository",
		nil,
		"",
	)
}
