package postgres

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/rate"
	"vereal/letsesim/pkg/esim"
)

// RateModel 费率数据库模型
type RateModel struct {
	BaseModel
	PackageID string `gorm:"index:idx_rate_lookup,priority:1;not null"`
	Country   string `gorm:"index:idx_rate_lookup,priority:2"`
	Rate      int64  `gorm:"not null"`
	Currency  string `gorm:"not null;default:'CNY'"`
	OwnerType string `gorm:"index:idx_rate_lookup,priority:3;not null"`
	OwnerID   string `gorm:"index:idx_rate_lookup,priority:4"`
	IsActive  bool   `gorm:"not null;default:true"`
}

// TableName 表名
func (RateModel) TableName() string {
	return "rates"
}

// RateRepository 费率存储库
type RateRepository struct {
	db *gorm.DB
}

// NewRateRepository 创建费率存储库
func NewRateRepository(db *gorm.DB) *RateRepository {
	return &RateRepository{
		db: db,
	}
}

// toDomain 将数据库模型转换为领域模型
func (m *RateModel) toDomain() *rate.Rate {
	return &rate.Rate{
		ID:        m.ID,
		PackageID: m.PackageID,
		Country:   m.Country,
		Rate:      m.Rate,
		Currency:  m.Currency,
		OwnerType: m.OwnerType,
		OwnerID:   m.OwnerID,
		IsActive:  m.IsActive,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

// fromDomain 将领域模型转换为数据库模型
func fromRateDomain(r *rate.Rate) *RateModel {
	return &RateModel{
		BaseModel: BaseModel{
			ID:        r.ID,
			CreatedAt: r.CreatedAt,
			UpdatedAt: r.UpdatedAt,
		},
		PackageID: r.PackageID,
		Country:   r.Country,
		Rate:      r.Rate,
		Currency:  r.Currency,
		OwnerType: r.OwnerType,
		OwnerID:   r.OwnerID,
		IsActive:  r.IsActive,
	}
}

// Create 创建费率
func (r *RateRepository) Create(ctx context.Context, rate *rate.Rate) error {
	model := fromRateDomain(rate)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if rate.ID == "" {
		rate.ID = model.ID
	}

	return result.Error
}

// GetByID 根据ID获取费率
func (r *RateRepository) GetByID(ctx context.Context, id string) (*rate.Rate, error) {
	var model RateModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Rate not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetGlobalRateByPackage 获取套餐全局费率
func (r *RateRepository) GetGlobalRateByPackage(ctx context.Context, packageID, country string) (*rate.Rate, error) {
	var model RateModel
	query := r.db.WithContext(ctx).Where("package_id = ? AND owner_type = ? AND is_active = true",
		packageID, rate.OwnerTypeGlobal)

	if country != "" {
		query = query.Where("country = ? OR country = ''", country)
	}

	// 使用原生SQL排序确保特定国家的费率排在前面
	orderClause := fmt.Sprintf("CASE WHEN country = '%s' THEN 0 ELSE 1 END", country)
	result := query.Order(orderClause).First(&model)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Global rate not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetResellerRateByPackage 获取代理商费率
func (r *RateRepository) GetResellerRateByPackage(ctx context.Context, resellerID, packageID, country string) (*rate.Rate, error) {
	var model RateModel
	query := r.db.WithContext(ctx).Where("package_id = ? AND owner_type = ? AND owner_id = ? AND is_active = true",
		packageID, rate.OwnerTypeReseller, resellerID)

	if country != "" {
		query = query.Where("country = ? OR country = ''", country)
	}

	// 使用原生SQL排序确保特定国家的费率排在前面
	orderClause := fmt.Sprintf("CASE WHEN country = '%s' THEN 0 ELSE 1 END", country)
	result := query.Order(orderClause).First(&model)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Reseller rate not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetEnterpriseRateByPackage 获取企业代理商费率
func (r *RateRepository) GetEnterpriseRateByPackage(ctx context.Context, enterpriseID, packageID, country string) (*rate.Rate, error) {
	var model RateModel
	query := r.db.WithContext(ctx).Where("package_id = ? AND owner_type = ? AND owner_id = ? AND is_active = true",
		packageID, rate.OwnerTypeEnterprise, enterpriseID)

	if country != "" {
		query = query.Where("country = ? OR country = ''", country)
	}

	// 使用原生SQL排序确保特定国家的费率排在前面
	orderClause := fmt.Sprintf("CASE WHEN country = '%s' THEN 0 ELSE 1 END", country)
	result := query.Order(orderClause).First(&model)

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Enterprise rate not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// applyFilters 应用查询筛选条件
func applyFilters(query *gorm.DB, filters map[string]interface{}) *gorm.DB {
	if filters == nil {
		return query
	}

	for key, value := range filters {
		if value != nil && value != "" {
			query = query.Where(key, value)
		}
	}

	return query
}

// ListGlobalRates 列出全局费率
func (r *RateRepository) ListGlobalRates(ctx context.Context, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error) {
	var models []RateModel
	var total int64

	query := r.db.WithContext(ctx).Model(&RateModel{}).Where("owner_type = ?", rate.OwnerTypeGlobal)
	query = applyFilters(query, filters)

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	if err := query.Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, 0, err
	}

	// 转换为领域模型
	rates := make([]*rate.Rate, len(models))
	for i, model := range models {
		rates[i] = model.toDomain()
	}

	return rates, total, nil
}

// ListResellerRates 列出代理商费率
func (r *RateRepository) ListResellerRates(ctx context.Context, resellerID string, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error) {
	var models []RateModel
	var total int64

	query := r.db.WithContext(ctx).Model(&RateModel{}).Where("owner_type = ? AND owner_id = ?", rate.OwnerTypeReseller, resellerID)
	query = applyFilters(query, filters)

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	if err := query.Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, 0, err
	}

	// 转换为领域模型
	rates := make([]*rate.Rate, len(models))
	for i, model := range models {
		rates[i] = model.toDomain()
	}

	return rates, total, nil
}

// ListEnterpriseRates 列出企业代理商费率
func (r *RateRepository) ListEnterpriseRates(ctx context.Context, enterpriseID string, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error) {
	var models []RateModel
	var total int64

	query := r.db.WithContext(ctx).Model(&RateModel{}).Where("owner_type = ? AND owner_id = ?", rate.OwnerTypeEnterprise, enterpriseID)
	query = applyFilters(query, filters)

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	if err := query.Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, 0, err
	}

	// 转换为领域模型
	rates := make([]*rate.Rate, len(models))
	for i, model := range models {
		rates[i] = model.toDomain()
	}

	return rates, total, nil
}

// Update 更新费率
func (r *RateRepository) Update(ctx context.Context, rate *rate.Rate) error {
	model := fromRateDomain(rate)
	result := r.db.WithContext(ctx).Model(&RateModel{}).Where("id = ?", rate.ID).Updates(model)
	return result.Error
}

// Delete 删除费率
func (r *RateRepository) Delete(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).Delete(&RateModel{}, id)
	return result.Error
}
