package postgres

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/order"
	"vereal/letsesim/internal/repository"
)

var _ repository.OrderRepository = (*OrderRepository)(nil)

type OrderModel struct {
	BaseModel
	UserID          string
	ResellerID      string
	ProviderType    string
	TransactionID   string
	ProviderOrderNo string
	Status          string
	TotalAmount     int64
	Currency        string
	Items           []OrderItemModel `gorm:"foreignKey:OrderID"`
	ESIMs           []OrderESIMModel `gorm:"foreignKey:OrderID"`
	CompletedAt     *time.Time
	PromotionID     string
	Metadata        json.RawMessage `gorm:"type:jsonb"`
}

type OrderItemModel struct {
	ID            string `gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	OrderID       string `gorm:"index"`
	PackageID     string
	PackageName   string
	Quantity      int
	UnitPrice     int64
	Currency      string
	DataVolume    int64
	ValidityDays  int
	LocationCodes string // 逗号分隔
}

type OrderESIMModel struct {
	ID            string `gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	OrderID       string `gorm:"index"`
	ICCID         string
	ESIMTranNo    string
	Status        string
	ActivationURL string
	QRCodeURL     string
	DataVolume    int64
	ValidityDays  int
	ExpiryDate    *time.Time
	ActivatedAt   *time.Time
}

func (OrderModel) TableName() string     { return "orders" }
func (OrderItemModel) TableName() string { return "order_items" }
func (OrderESIMModel) TableName() string { return "order_esims" }

// --- 转换函数 ---
func toOrderModel(o *order.Order) *OrderModel {
	meta, _ := json.Marshal(o.Metadata)
	var completedAt *time.Time
	if !o.CompletedAt.IsZero() {
		completedAt = &o.CompletedAt
	}
	items := make([]OrderItemModel, len(o.Items))
	for i, it := range o.Items {
		items[i] = OrderItemModel{
			PackageID:     it.PackageID,
			PackageName:   it.PackageName,
			Quantity:      it.Quantity,
			UnitPrice:     it.UnitPrice,
			Currency:      it.Currency,
			DataVolume:    it.DataVolume,
			ValidityDays:  it.ValidityDays,
			LocationCodes: joinStringSlice(it.LocationCodes),
		}
	}
	esims := make([]OrderESIMModel, len(o.ESIMs))
	for i, e := range o.ESIMs {
		var expiry, activated *time.Time
		if !e.ExpiryDate.IsZero() {
			expiry = &e.ExpiryDate
		}
		if !e.ActivatedAt.IsZero() {
			activated = &e.ActivatedAt
		}
		esims[i] = OrderESIMModel{
			ICCID:         e.ICCID,
			ESIMTranNo:    e.ESIMTranNo,
			Status:        e.Status,
			ActivationURL: e.ActivationURL,
			QRCodeURL:     e.QRCodeURL,
			DataVolume:    e.DataVolume,
			ValidityDays:  e.ValidityDays,
			ExpiryDate:    expiry,
			ActivatedAt:   activated,
		}
	}
	return &OrderModel{
		BaseModel:       BaseModel{ID: o.ID, CreatedAt: o.CreatedAt, UpdatedAt: o.UpdatedAt},
		UserID:          o.UserID,
		ResellerID:      o.ResellerID,
		ProviderType:    o.ProviderType,
		TransactionID:   o.TransactionID,
		ProviderOrderNo: o.ProviderOrderNo,
		Status:          string(o.Status),
		TotalAmount:     o.TotalAmount,
		Currency:        o.Currency,
		Items:           items,
		ESIMs:           esims,
		CompletedAt:     completedAt,
		PromotionID:     o.PromotionID,
		Metadata:        meta,
	}
}

func toOrderDomain(m *OrderModel) *order.Order {
	var meta map[string]interface{}
	_ = json.Unmarshal(m.Metadata, &meta)
	items := make([]order.Item, len(m.Items))
	for i, it := range m.Items {
		items[i] = order.Item{
			PackageID:     it.PackageID,
			PackageName:   it.PackageName,
			Quantity:      it.Quantity,
			UnitPrice:     it.UnitPrice,
			Currency:      it.Currency,
			DataVolume:    it.DataVolume,
			ValidityDays:  it.ValidityDays,
			LocationCodes: splitStringSlice(it.LocationCodes),
		}
	}
	esims := make([]order.ESIM, len(m.ESIMs))
	for i, e := range m.ESIMs {
		esims[i] = order.ESIM{
			ICCID:         e.ICCID,
			ESIMTranNo:    e.ESIMTranNo,
			Status:        e.Status,
			ActivationURL: e.ActivationURL,
			QRCodeURL:     e.QRCodeURL,
			DataVolume:    e.DataVolume,
			ValidityDays:  e.ValidityDays,
		}
		if e.ExpiryDate != nil {
			esims[i].ExpiryDate = *e.ExpiryDate
		}
		if e.ActivatedAt != nil {
			esims[i].ActivatedAt = *e.ActivatedAt
		}
	}
	var completedAt time.Time
	if m.CompletedAt != nil {
		completedAt = *m.CompletedAt
	}
	return &order.Order{
		ID:              m.ID,
		UserID:          m.UserID,
		ResellerID:      m.ResellerID,
		ProviderType:    m.ProviderType,
		TransactionID:   m.TransactionID,
		ProviderOrderNo: m.ProviderOrderNo,
		Status:          order.Status(m.Status),
		TotalAmount:     m.TotalAmount,
		Currency:        m.Currency,
		Items:           items,
		ESIMs:           esims,
		CreatedAt:       m.CreatedAt,
		UpdatedAt:       m.UpdatedAt,
		CompletedAt:     completedAt,
		PromotionID:     m.PromotionID,
		Metadata:        meta,
	}
}

func joinStringSlice(s []string) string {
	if len(s) == 0 {
		return ""
	}
	return string(json.RawMessage(`"` + s[0] + `"`))
}

func splitStringSlice(s string) []string {
	if s == "" {
		return nil
	}
	var arr []string
	_ = json.Unmarshal([]byte(s), &arr)
	return arr
}

// OrderRepository 订单存储库
// 修复结构体定义和构造函数

type OrderRepository struct {
	db *gorm.DB
}

func NewOrderRepository(db *gorm.DB) *OrderRepository {
	return &OrderRepository{db: db}
}

// --- OrderRepository 方法实现 ---
func (r *OrderRepository) Create(ctx context.Context, o *order.Order) error {
	model := toOrderModel(o)
	result := r.db.WithContext(ctx).Create(model)
	if o.ID == "" {
		o.ID = model.ID
	}
	return result.Error
}

func (r *OrderRepository) GetByID(ctx context.Context, id string) (*order.Order, error) {
	var model OrderModel
	result := r.db.WithContext(ctx).Preload("Items").Preload("ESIMs").Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("order not found")
	}
	if result.Error != nil {
		return nil, result.Error
	}
	return toOrderDomain(&model), nil
}

func (r *OrderRepository) GetByTransactionID(ctx context.Context, transactionID string) (*order.Order, error) {
	var model OrderModel
	result := r.db.WithContext(ctx).Preload("Items").Preload("ESIMs").Where("transaction_id = ?", transactionID).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("order not found")
	}
	if result.Error != nil {
		return nil, result.Error
	}
	return toOrderDomain(&model), nil
}

func (r *OrderRepository) GetByProviderOrderNo(ctx context.Context, providerOrderNo string) (*order.Order, error) {
	var model OrderModel
	result := r.db.WithContext(ctx).Preload("Items").Preload("ESIMs").Where("provider_order_no = ?", providerOrderNo).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("order not found")
	}
	if result.Error != nil {
		return nil, result.Error
	}
	return toOrderDomain(&model), nil
}

func (r *OrderRepository) Update(ctx context.Context, o *order.Order) error {
	model := toOrderModel(o)
	return r.db.WithContext(ctx).Model(&OrderModel{}).Where("id = ?", o.ID).Updates(model).Error
}

func (r *OrderRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&OrderModel{}, "id = ?", id).Error
}

func (r *OrderRepository) List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*order.Order, int64, error) {
	var models []OrderModel
	var total int64
	query := r.db.WithContext(ctx).Model(&OrderModel{})
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	offset := (page - 1) * pageSize
	result := query.Preload("Items").Preload("ESIMs").Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}
	orders := make([]*order.Order, len(models))
	for i, model := range models {
		orders[i] = toOrderDomain(&model)
	}
	return orders, total, nil
}

func (r *OrderRepository) GetUserOrders(ctx context.Context, userID string, startDate, endDate time.Time, page, pageSize int) ([]*order.Order, int64, error) {
	var models []OrderModel
	var total int64
	query := r.db.WithContext(ctx).Model(&OrderModel{}).Where("user_id = ? AND created_at >= ? AND created_at <= ?", userID, startDate, endDate)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	offset := (page - 1) * pageSize
	result := query.Preload("Items").Preload("ESIMs").Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}
	orders := make([]*order.Order, len(models))
	for i, model := range models {
		orders[i] = toOrderDomain(&model)
	}
	return orders, total, nil
}

func (r *OrderRepository) GetResellerOrders(ctx context.Context, resellerID string, startDate, endDate time.Time, page, pageSize int) ([]*order.Order, int64, error) {
	var models []OrderModel
	var total int64
	query := r.db.WithContext(ctx).Model(&OrderModel{}).Where("reseller_id = ? AND created_at >= ? AND created_at <= ?", resellerID, startDate, endDate)
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	offset := (page - 1) * pageSize
	result := query.Preload("Items").Preload("ESIMs").Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}
	orders := make([]*order.Order, len(models))
	for i, model := range models {
		orders[i] = toOrderDomain(&model)
	}
	return orders, total, nil
}
