package postgres

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel 包含所有模型共用的基础字段
type BaseModel struct {
	ID        string         `gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	CreatedAt time.Time      `gorm:"autoCreateTime"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

// BeforeCreate 在创建记录前自动生成UUID
func (m *BaseModel) BeforeCreate(tx *gorm.DB) error {
	if m.ID == "" {
		m.ID = uuid.New().String()
	}
	return nil
}

// IsDeleted 检查记录是否已被逻辑删除
func (m *BaseModel) IsDeleted() bool {
	return m.DeletedAt.Valid
}

// SetDeleted 标记记录为已删除
func (m *BaseModel) SetDeleted() {
	m.DeletedAt.Time = time.Now()
	m.DeletedAt.Valid = true
}

// NewUUID 生成新的UUID字符串
func NewUUID() string {
	return uuid.New().String()
}
