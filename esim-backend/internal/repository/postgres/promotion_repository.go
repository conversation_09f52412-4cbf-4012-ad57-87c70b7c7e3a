package postgres

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/promotion"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

var _ repository.PromotionRepository = (*PromotionRepository)(nil)

// PromotionModel 促销数据库模型
type PromotionModel struct {
	BaseModel
	Code          string `gorm:"uniqueIndex;not null"`
	Description   string
	Type          string `gorm:"not null"`
	Value         int64  `gorm:"not null"`
	MinOrderValue int64
	MaxDiscount   int64
	StartDate     time.Time `gorm:"not null"`
	EndDate       time.Time `gorm:"not null"`
	UsageLimit    int
	CurrentUsage  int    `gorm:"not null;default:0"`
	Status        string `gorm:"not null"`
	ResellerID    string `gorm:"index"`
	Metadata      string // JSON格式存储
}

// TableName 表名
func (PromotionModel) TableName() string {
	return "promotions"
}

// PromotionRepository 促销存储库
type PromotionRepository struct {
	db *gorm.DB
}

// NewPromotionRepository 创建促销存储库
func NewPromotionRepository(db *gorm.DB) *PromotionRepository {
	return &PromotionRepository{
		db: db,
	}
}

// toDomainPromotion 将数据库模型转换为领域模型
func (m *PromotionModel) toDomainPromotion() *promotion.Promotion {
	var metadata map[string]interface{}
	// 这里需要从JSON字符串转换为map
	// 简化实现，在实际代码中应该使用json.Unmarshal
	metadata = make(map[string]interface{})

	return &promotion.Promotion{
		ID:            m.ID,
		Code:          m.Code,
		Description:   m.Description,
		Type:          promotion.Type(m.Type),
		Value:         m.Value,
		MinOrderValue: m.MinOrderValue,
		MaxDiscount:   m.MaxDiscount,
		StartDate:     m.StartDate,
		EndDate:       m.EndDate,
		UsageLimit:    m.UsageLimit,
		CurrentUsage:  m.CurrentUsage,
		Status:        promotion.Status(m.Status),
		ResellerID:    m.ResellerID,
		Metadata:      metadata,
		CreatedAt:     m.CreatedAt,
		UpdatedAt:     m.UpdatedAt,
	}
}

// fromDomainPromotion 将领域模型转换为数据库模型
func fromDomainPromotion(p *promotion.Promotion) *PromotionModel {
	// 这里需要将map转换为JSON字符串
	// 简化实现，在实际代码中应该使用json.Marshal
	metadata := ""

	return &PromotionModel{
		BaseModel: BaseModel{
			ID:        p.ID,
			CreatedAt: p.CreatedAt,
			UpdatedAt: p.UpdatedAt,
		},
		Code:          p.Code,
		Description:   p.Description,
		Type:          string(p.Type),
		Value:         p.Value,
		MinOrderValue: p.MinOrderValue,
		MaxDiscount:   p.MaxDiscount,
		StartDate:     p.StartDate,
		EndDate:       p.EndDate,
		UsageLimit:    p.UsageLimit,
		CurrentUsage:  p.CurrentUsage,
		Status:        string(p.Status),
		ResellerID:    p.ResellerID,
		Metadata:      metadata,
	}
}

// Create 创建促销
func (r *PromotionRepository) Create(ctx context.Context, promo *promotion.Promotion) error {
	if promo.ID == "" {
		promo.ID = uuid.New().String()
	}

	model := fromDomainPromotion(promo)
	result := r.db.WithContext(ctx).Create(model)
	return result.Error
}

// GetByID 根据ID获取促销
func (r *PromotionRepository) GetByID(ctx context.Context, id string) (*promotion.Promotion, error) {
	var model PromotionModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Promotion not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainPromotion(), nil
}

// GetByCode 根据代码获取促销
func (r *PromotionRepository) GetByCode(ctx context.Context, code string) (*promotion.Promotion, error) {
	var model PromotionModel
	result := r.db.WithContext(ctx).Where("code = ?", code).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, esim.NewESIMError(
			esim.ErrNotFound,
			"Promotion not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomainPromotion(), nil
}

// Update 更新促销
func (r *PromotionRepository) Update(ctx context.Context, promo *promotion.Promotion) error {
	model := fromDomainPromotion(promo)
	result := r.db.WithContext(ctx).Model(&PromotionModel{}).Where("id = ?", promo.ID).Updates(model)
	return result.Error
}

// Delete 删除促销
func (r *PromotionRepository) Delete(ctx context.Context, id string) error {
	result := r.db.WithContext(ctx).Delete(&PromotionModel{}, "id = ?", id)
	return result.Error
}

// List 获取促销列表
func (r *PromotionRepository) List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*promotion.Promotion, int64, error) {
	var models []PromotionModel
	var total int64

	query := r.db.WithContext(ctx).Model(&PromotionModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	promos := make([]*promotion.Promotion, len(models))
	for i, model := range models {
		promos[i] = model.toDomainPromotion()
	}

	return promos, total, nil
}

// ListActive 获取活动促销列表
func (r *PromotionRepository) ListActive(ctx context.Context, resellerID string, page, pageSize int) ([]*promotion.Promotion, int64, error) {
	var models []PromotionModel
	var total int64

	now := time.Now()
	query := r.db.WithContext(ctx).Model(&PromotionModel{}).
		Where("status = ? AND start_date <= ? AND end_date > ?",
			string(promotion.StatusActive), now, now)

	// 如果指定了代理商ID，只获取该代理商的促销和全局促销
	if resellerID != "" {
		query = query.Where("reseller_id = ? OR reseller_id IS NULL OR reseller_id = ''", resellerID)
	} else {
		// 只获取全局促销
		query = query.Where("reseller_id IS NULL OR reseller_id = ''")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	promos := make([]*promotion.Promotion, len(models))
	for i, model := range models {
		promos[i] = model.toDomainPromotion()
	}

	return promos, total, nil
}

// GetActivePromotions 获取活动促销
func (r *PromotionRepository) GetActivePromotions(ctx context.Context, filter map[string]interface{}) ([]*promotion.Promotion, error) {
	var models []PromotionModel

	now := time.Now()
	query := r.db.WithContext(ctx).Model(&PromotionModel{}).
		Where("status = ? AND start_date <= ? AND end_date > ?",
			string(promotion.StatusActive), now, now)

	// 应用自定义过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 添加使用限制过滤
	query = query.Where("usage_limit = 0 OR current_usage < usage_limit")

	// 执行查询
	result := query.Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, result.Error
	}

	// 转换为领域模型
	promos := make([]*promotion.Promotion, len(models))
	for i, model := range models {
		promos[i] = model.toDomainPromotion()
	}

	return promos, nil
}
