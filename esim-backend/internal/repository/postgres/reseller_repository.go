package postgres

import (
	"context"
	"encoding/json"
	"errors"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/reseller"
	"vereal/letsesim/internal/repository"
)

var _ repository.ResellerRepository = (*ResellerRepository)(nil)

type ResellerModel struct {
	BaseModel
	UserID         string
	CompanyName    string
	ContactPerson  string
	Email          string
	Phone          string
	Country        string
	Address        string
	CommissionRate int
	APIKey         string
	APISecret      string
	Status         string
	Balance        int64
	CallbackURL    string
	Metadata       json.RawMessage `gorm:"type:jsonb"`
}

func (ResellerModel) TableName() string { return "resellers" }

type ResellerRepository struct {
	db *gorm.DB
}

func NewResellerRepository(db *gorm.DB) *ResellerRepository {
	return &ResellerRepository{db: db}
}

func toResellerModel(r *reseller.Reseller) *ResellerModel {
	meta, _ := json.Marshal(r.Metadata)
	return &ResellerModel{
		BaseModel:      BaseModel{ID: r.ID, CreatedAt: r.<PERSON>, UpdatedAt: r.<PERSON>t},
		UserID:         r.User<PERSON>,
		CompanyName:    r.Company<PERSON>ame,
		ContactPerson:  r.<PERSON>,
		Email:          r.Email,
		Phone:          r.Phone,
		Country:        r.Country,
		Address:        r.Address,
		CommissionRate: r.CommissionRate,
		APIKey:         r.APIKey,
		APISecret:      r.APISecret,
		Status:         string(r.Status),
		Balance:        r.Balance,
		CallbackURL:    r.CallbackURL,
		Metadata:       meta,
	}
}

func toResellerDomain(m *ResellerModel) *reseller.Reseller {
	var meta map[string]interface{}
	_ = json.Unmarshal(m.Metadata, &meta)
	return &reseller.Reseller{
		ID:             m.ID,
		UserID:         m.UserID,
		CompanyName:    m.CompanyName,
		ContactPerson:  m.ContactPerson,
		Email:          m.Email,
		Phone:          m.Phone,
		Country:        m.Country,
		Address:        m.Address,
		CommissionRate: m.CommissionRate,
		APIKey:         m.APIKey,
		APISecret:      m.APISecret,
		Status:         reseller.Status(m.Status),
		Balance:        m.Balance,
		CallbackURL:    m.CallbackURL,
		Metadata:       meta,
		CreatedAt:      m.CreatedAt,
		UpdatedAt:      m.UpdatedAt,
	}
}

func (r *ResellerRepository) Create(ctx context.Context, reseller *reseller.Reseller) error {
	model := toResellerModel(reseller)
	result := r.db.WithContext(ctx).Create(model)
	if reseller.ID == "" {
		reseller.ID = model.ID
	}
	return result.Error
}

func (r *ResellerRepository) GetByID(ctx context.Context, id string) (*reseller.Reseller, error) {
	var model ResellerModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("reseller not found")
	}
	if result.Error != nil {
		return nil, result.Error
	}
	return toResellerDomain(&model), nil
}

func (r *ResellerRepository) GetByUserID(ctx context.Context, userID string) (*reseller.Reseller, error) {
	var model ResellerModel
	result := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("reseller not found")
	}
	if result.Error != nil {
		return nil, result.Error
	}
	return toResellerDomain(&model), nil
}

func (r *ResellerRepository) GetByAPIKey(ctx context.Context, apiKey string) (*reseller.Reseller, error) {
	var model ResellerModel
	result := r.db.WithContext(ctx).Where("api_key = ?", apiKey).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, errors.New("reseller not found")
	}
	if result.Error != nil {
		return nil, result.Error
	}
	return toResellerDomain(&model), nil
}

func (r *ResellerRepository) Update(ctx context.Context, reseller *reseller.Reseller) error {
	model := toResellerModel(reseller)
	return r.db.WithContext(ctx).Model(&ResellerModel{}).Where("id = ?", reseller.ID).Updates(model).Error
}

func (r *ResellerRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&ResellerModel{}, "id = ?", id).Error
}

func (r *ResellerRepository) List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*reseller.Reseller, int64, error) {
	var models []ResellerModel
	var total int64
	query := r.db.WithContext(ctx).Model(&ResellerModel{})
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}
	resellers := make([]*reseller.Reseller, len(models))
	for i, model := range models {
		resellers[i] = toResellerDomain(&model)
	}
	return resellers, total, nil
}

func (r *ResellerRepository) UpdateBalance(ctx context.Context, id string, amount int64) error {
	return r.db.WithContext(ctx).Model(&ResellerModel{}).Where("id = ?", id).UpdateColumn("balance", gorm.Expr("balance + ?", amount)).Error
}
