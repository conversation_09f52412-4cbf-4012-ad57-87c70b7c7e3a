package postgres

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/esim"
	pkgesim "vereal/letsesim/pkg/esim"
)

// RegionModel 区域数据库模型
type RegionModel struct {
	BaseModel
	ProviderType string `gorm:"index;not null"`
	Code         string `gorm:"uniqueIndex:idx_provider_region_code;not null"`
	Name         string `gorm:"not null"`
	Type         string
	LastSyncAt   time.Time
}

// TableName 表名
func (RegionModel) TableName() string {
	return "regions"
}

// SubLocationModel 子区域数据库模型
type SubLocationModel struct {
	BaseModel
	Code         string `gorm:"uniqueIndex:idx_provider_sublocation_code;not null"`
	Name         string `gorm:"not null"`
	ProviderType string `gorm:"uniqueIndex:idx_provider_sublocation_code;not null"`
}

// TableName 表名
func (SubLocationModel) TableName() string {
	return "sub_locations"
}

// RegionSubLocationMapModel 区域-子区域映射数据库模型
type RegionSubLocationMapModel struct {
	RegionID      string `gorm:"primaryKey;not null"`
	SubLocationID string `gorm:"primaryKey;not null"`
	ProviderType  string `gorm:"index;not null"`
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// TableName 表名
func (RegionSubLocationMapModel) TableName() string {
	return "region_sublocation_maps"
}

// RegionRepository 区域存储库
type RegionRepository struct {
	db *gorm.DB
}

// NewRegionRepository 创建区域存储库
func NewRegionRepository(db *gorm.DB) *RegionRepository {
	return &RegionRepository{
		db: db,
	}
}

// toDomain 转换为领域模型
func (m *RegionModel) toDomain() *esim.Region {
	return &esim.Region{
		ID:           m.ID,
		ProviderType: m.ProviderType,
		Code:         m.Code,
		Name:         m.Name,
		Type:         esim.RegionType(m.Type),
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
		LastSyncAt:   m.LastSyncAt,
	}
}

// fromDomain 从领域模型转换
func fromRegionDomain(r *esim.Region) *RegionModel {
	return &RegionModel{
		BaseModel: BaseModel{
			ID:        r.ID,
			CreatedAt: r.CreatedAt,
			UpdatedAt: r.UpdatedAt,
		},
		ProviderType: r.ProviderType,
		Code:         r.Code,
		Name:         r.Name,
		Type:         string(r.Type),
		LastSyncAt:   r.LastSyncAt,
	}
}

// Create 创建区域
func (r *RegionRepository) Create(ctx context.Context, region *esim.Region) error {
	model := fromRegionDomain(region)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if region.ID == "" {
		region.ID = model.ID
	}

	return result.Error
}

// GetByID 根据ID获取区域
func (r *RegionRepository) GetByID(ctx context.Context, id string) (*esim.Region, error) {
	var model RegionModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"Region not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetByCode 根据区域代码获取区域
func (r *RegionRepository) GetByCode(ctx context.Context, providerType, code string) (*esim.Region, error) {
	var model RegionModel
	result := r.db.WithContext(ctx).Where("provider_type = ? AND code = ?", providerType, code).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"Region not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// Update 更新区域
func (r *RegionRepository) Update(ctx context.Context, region *esim.Region) error {
	model := fromRegionDomain(region)
	result := r.db.WithContext(ctx).Model(&RegionModel{}).Where("id = ?", region.ID).Updates(model)
	return result.Error
}

// ListRegions 获取区域列表，支持条件过滤
func (r *RegionRepository) ListRegions(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Region, int64, error) {
	var models []RegionModel
	var total int64

	query := r.db.WithContext(ctx).Model(&RegionModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("name ASC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	regions := make([]*esim.Region, len(models))
	for i, model := range models {
		regions[i] = model.toDomain()
	}

	return regions, total, nil
}

// ListByProvider 获取指定提供商的所有区域
func (r *RegionRepository) ListByProvider(ctx context.Context, providerType string) ([]*esim.Region, error) {
	var models []RegionModel
	result := r.db.WithContext(ctx).Where("provider_type = ?", providerType).Order("name ASC").Find(&models)
	if result.Error != nil {
		return nil, result.Error
	}

	// 转换为领域模型
	regions := make([]*esim.Region, len(models))
	for i, model := range models {
		regions[i] = model.toDomain()
	}

	return regions, nil
}

// SubLocationRepository 子区域存储库
type SubLocationRepository struct {
	db *gorm.DB
}

// NewSubLocationRepository 创建子区域存储库
func NewSubLocationRepository(db *gorm.DB) *SubLocationRepository {
	return &SubLocationRepository{
		db: db,
	}
}

// toDomain 转换为领域模型
func (m *SubLocationModel) toDomain() *esim.SubLocation {
	return &esim.SubLocation{
		ID:           m.ID,
		Code:         m.Code,
		Name:         m.Name,
		ProviderType: m.ProviderType,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
	}
}

// fromDomain 从领域模型转换
func fromSubLocationDomain(s *esim.SubLocation) *SubLocationModel {
	return &SubLocationModel{
		BaseModel: BaseModel{
			ID:        s.ID,
			CreatedAt: s.CreatedAt,
			UpdatedAt: s.UpdatedAt,
		},
		Code:         s.Code,
		Name:         s.Name,
		ProviderType: s.ProviderType,
	}
}

// Create 创建子区域
func (r *SubLocationRepository) Create(ctx context.Context, subLocation *esim.SubLocation) error {
	model := fromSubLocationDomain(subLocation)
	result := r.db.WithContext(ctx).Create(model)

	// 将自动生成的ID回写到领域模型
	if subLocation.ID == "" {
		subLocation.ID = model.ID
	}

	return result.Error
}

// GetByID 根据ID获取子区域
func (r *SubLocationRepository) GetByID(ctx context.Context, id string) (*esim.SubLocation, error) {
	var model SubLocationModel
	result := r.db.WithContext(ctx).Where("id = ?", id).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"SubLocation not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// GetByCode 根据子区域代码获取子区域
func (r *SubLocationRepository) GetByCode(ctx context.Context, providerType, code string) (*esim.SubLocation, error) {
	var model SubLocationModel
	result := r.db.WithContext(ctx).Where("provider_type = ? AND code = ?", providerType, code).First(&model)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, pkgesim.NewESIMError(
			pkgesim.ErrNotFound,
			"SubLocation not found",
			nil,
			"",
		)
	}

	if result.Error != nil {
		return nil, result.Error
	}

	return model.toDomain(), nil
}

// Update 更新子区域
func (r *SubLocationRepository) Update(ctx context.Context, subLocation *esim.SubLocation) error {
	model := fromSubLocationDomain(subLocation)
	result := r.db.WithContext(ctx).Model(&SubLocationModel{}).Where("id = ?", subLocation.ID).Updates(model)
	return result.Error
}

// ListSubLocations 获取子区域列表，支持条件过滤
func (r *SubLocationRepository) ListSubLocations(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.SubLocation, int64, error) {
	var models []SubLocationModel
	var total int64

	query := r.db.WithContext(ctx).Model(&SubLocationModel{})

	// 应用过滤条件
	for key, value := range filter {
		query = query.Where(key+" = ?", value)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("name ASC").Find(&models)
	if result.Error != nil {
		return nil, 0, result.Error
	}

	// 转换为领域模型
	subLocations := make([]*esim.SubLocation, len(models))
	for i, model := range models {
		subLocations[i] = model.toDomain()
	}

	return subLocations, total, nil
}

// GetRegionSubLocations 通过区域-子区域映射获取指定区域的所有子区域
func (r *SubLocationRepository) GetRegionSubLocations(ctx context.Context, regionID string) ([]*esim.SubLocation, error) {
	var subLocationIDs []string

	// 获取映射关系中该区域对应的所有子区域ID
	if err := r.db.WithContext(ctx).Model(&RegionSubLocationMapModel{}).
		Where("region_id = ?", regionID).
		Pluck("sub_location_id", &subLocationIDs).Error; err != nil {
		return nil, err
	}

	if len(subLocationIDs) == 0 {
		return []*esim.SubLocation{}, nil
	}

	// 获取子区域详细信息
	var models []SubLocationModel
	if err := r.db.WithContext(ctx).Where("id IN ?", subLocationIDs).Order("name ASC").Find(&models).Error; err != nil {
		return nil, err
	}

	// 转换为领域模型
	subLocations := make([]*esim.SubLocation, len(models))
	for i, model := range models {
		subLocations[i] = model.toDomain()
	}

	return subLocations, nil
}

// RegionSubLocationMapRepository 区域-子区域映射存储库
type RegionSubLocationMapRepository struct {
	db *gorm.DB
}

// NewRegionSubLocationMapRepository 创建区域-子区域映射存储库
func NewRegionSubLocationMapRepository(db *gorm.DB) *RegionSubLocationMapRepository {
	return &RegionSubLocationMapRepository{
		db: db,
	}
}

// CreateMapping 创建区域-子区域映射
func (r *RegionSubLocationMapRepository) CreateMapping(ctx context.Context, regionID, subLocationID, providerType string) error {
	mapping := RegionSubLocationMapModel{
		RegionID:      regionID,
		SubLocationID: subLocationID,
		ProviderType:  providerType,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	return r.db.WithContext(ctx).Create(&mapping).Error
}

// DeleteMapping 删除区域-子区域映射
func (r *RegionSubLocationMapRepository) DeleteMapping(ctx context.Context, regionID, subLocationID string) error {
	return r.db.WithContext(ctx).Where("region_id = ? AND sub_location_id = ?", regionID, subLocationID).
		Delete(&RegionSubLocationMapModel{}).Error
}

// GetRegionsBySubLocation 根据子区域获取关联的所有区域
func (r *RegionSubLocationMapRepository) GetRegionsBySubLocation(ctx context.Context, subLocationID string) ([]*esim.Region, error) {
	var regionIDs []string

	// 获取映射关系中该子区域对应的所有区域ID
	if err := r.db.WithContext(ctx).Model(&RegionSubLocationMapModel{}).
		Where("sub_location_id = ?", subLocationID).
		Pluck("region_id", &regionIDs).Error; err != nil {
		return nil, err
	}

	if len(regionIDs) == 0 {
		return []*esim.Region{}, nil
	}

	// 获取区域详细信息
	var models []RegionModel
	if err := r.db.WithContext(ctx).Where("id IN ?", regionIDs).Order("name ASC").Find(&models).Error; err != nil {
		return nil, err
	}

	// 转换为领域模型
	regions := make([]*esim.Region, len(models))
	for i, model := range models {
		regions[i] = model.toDomain()
	}

	return regions, nil
}

// GetSubLocationsByRegion 根据区域获取关联的所有子区域
func (r *RegionSubLocationMapRepository) GetSubLocationsByRegion(ctx context.Context, regionID string) ([]*esim.SubLocation, error) {
	var subLocationIDs []string

	// 获取映射关系中该区域对应的所有子区域ID
	if err := r.db.WithContext(ctx).Model(&RegionSubLocationMapModel{}).
		Where("region_id = ?", regionID).
		Pluck("sub_location_id", &subLocationIDs).Error; err != nil {
		return nil, err
	}

	if len(subLocationIDs) == 0 {
		return []*esim.SubLocation{}, nil
	}

	// 获取子区域详细信息
	var models []SubLocationModel
	if err := r.db.WithContext(ctx).Where("id IN ?", subLocationIDs).Order("name ASC").Find(&models).Error; err != nil {
		return nil, err
	}

	// 转换为领域模型
	subLocations := make([]*esim.SubLocation, len(models))
	for i, model := range models {
		subLocations[i] = model.toDomain()
	}

	return subLocations, nil
}
