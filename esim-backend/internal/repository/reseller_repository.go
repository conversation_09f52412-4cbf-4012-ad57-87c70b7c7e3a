package repository

import (
	"context"

	"vereal/letsesim/internal/domain/reseller"
)

// ResellerRepository 代理商存储库接口
type ResellerRepository interface {
	// Create 创建代理商
	Create(ctx context.Context, reseller *reseller.Reseller) error

	// GetByID 根据ID获取代理商
	GetByID(ctx context.Context, id string) (*reseller.Reseller, error)

	// GetByUserID 根据用户ID获取代理商
	GetByUserID(ctx context.Context, userID string) (*reseller.Reseller, error)

	// GetByAPIKey 根据API密钥获取代理商
	GetByAPIKey(ctx context.Context, apiKey string) (*reseller.Reseller, error)

	// Update 更新代理商
	Update(ctx context.Context, reseller *reseller.Reseller) error

	// Delete 删除代理商
	Delete(ctx context.Context, id string) error

	// List 获取代理商列表
	List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*reseller.Reseller, int64, error)

	// UpdateBalance 更新代理商余额
	UpdateBalance(ctx context.Context, id string, amount int64) error
}
