package repository

import (
	"context"

	"vereal/letsesim/internal/domain/user"
)

// UserRepository 用户存储库接口
type UserRepository interface {
	// Create 创建用户
	Create(ctx context.Context, user *user.User) error

	// GetByID 根据ID获取用户
	GetByID(ctx context.Context, id string) (*user.User, error)

	// GetByEmail 根据邮箱获取用户
	GetByEmail(ctx context.Context, email string) (*user.User, error)

	// Update 更新用户
	Update(ctx context.Context, user *user.User) error

	// Delete 删除用户
	Delete(ctx context.Context, id string) error

	// List 获取用户列表
	List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*user.User, int64, error)

	// CountUsers 获取用户总数
	CountUsers(ctx context.Context) (int64, error)
}
