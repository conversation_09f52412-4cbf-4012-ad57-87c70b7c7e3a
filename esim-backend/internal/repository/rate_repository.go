package repository

import (
	"context"

	"vereal/letsesim/internal/domain/rate"
)

// RateRepository 费率仓库接口
type RateRepository interface {
	// 创建费率
	Create(ctx context.Context, rate *rate.Rate) error

	// 获取费率
	GetByID(ctx context.Context, id string) (*rate.Rate, error)

	// 获取套餐全局费率
	GetGlobalRateByPackage(ctx context.Context, packageID, country string) (*rate.Rate, error)

	// 获取代理商费率
	GetResellerRateByPackage(ctx context.Context, resellerID, packageID, country string) (*rate.Rate, error)

	// 获取企业代理商费率
	GetEnterpriseRateByPackage(ctx context.Context, enterpriseID, packageID, country string) (*rate.Rate, error)

	// 列出全局费率
	ListGlobalRates(ctx context.Context, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error)

	// 列出代理商费率
	ListResellerRates(ctx context.Context, resellerID string, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error)

	// 列出企业代理商费率
	ListEnterpriseRates(ctx context.Context, enterpriseID string, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error)

	// 更新费率
	Update(ctx context.Context, rate *rate.Rate) error

	// 删除费率
	Delete(ctx context.Context, id string) error
}
