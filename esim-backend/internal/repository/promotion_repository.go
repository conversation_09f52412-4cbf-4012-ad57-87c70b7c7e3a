package repository

import (
	"context"

	"vereal/letsesim/internal/domain/promotion"
)

// PromotionRepository 促销存储库接口
type PromotionRepository interface {
	// Create 创建促销
	Create(ctx context.Context, promotion *promotion.Promotion) error

	// GetByID 根据ID获取促销
	GetByID(ctx context.Context, id string) (*promotion.Promotion, error)

	// GetByCode 根据代码获取促销
	GetByCode(ctx context.Context, code string) (*promotion.Promotion, error)

	// Update 更新促销
	Update(ctx context.Context, promotion *promotion.Promotion) error

	// Delete 删除促销
	Delete(ctx context.Context, id string) error

	// List 获取促销列表
	List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*promotion.Promotion, int64, error)

	// GetActivePromotions 获取活动促销
	GetActivePromotions(ctx context.Context, filter map[string]interface{}) ([]*promotion.Promotion, error)
}
