package repository

import (
	"context"

	"vereal/letsesim/internal/domain/credit"
)

// CreditRepository 用户积分存储库接口
type CreditRepository interface {
	// Create 创建用户积分
	Create(ctx context.Context, credit *credit.Credit) error

	// GetByUserID 根据用户ID获取积分
	GetByUserID(ctx context.Context, userID string) (*credit.Credit, error)

	// Update 更新积分
	Update(ctx context.Context, credit *credit.Credit) error

	// UpdateBalance 更新余额
	UpdateBalance(ctx context.Context, userID string, amount float64) error

	// CreateTransaction 创建交易记录
	CreateTransaction(ctx context.Context, transaction *credit.Transaction) error

	// GetTransactionByID 根据ID获取交易记录
	GetTransactionByID(ctx context.Context, id string) (*credit.Transaction, error)

	// UpdateTransaction 更新交易记录
	UpdateTransaction(ctx context.Context, transaction *credit.Transaction) error

	// ListTransactions 获取用户交易记录列表
	ListTransactions(ctx context.Context, userID string, page, pageSize int) ([]*credit.Transaction, int64, error)

	// GetTransactionsByOrderID 根据订单ID获取交易记录
	GetTransactionsByOrderID(ctx context.Context, orderID string) ([]*credit.Transaction, error)
}
