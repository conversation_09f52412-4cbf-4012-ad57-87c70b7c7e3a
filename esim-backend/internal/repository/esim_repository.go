package repository

import (
	"context"
	"time"

	"vereal/letsesim/internal/domain/esim"
)

// ESIMRepository eSIM存储库接口
type ESIMRepository interface {
	// ESIM操作
	CreateESIM(ctx context.Context, esim *esim.ESIM) error
	GetESIMByID(ctx context.Context, id string) (*esim.ESIM, error)
	GetESIMByICCID(ctx context.Context, iccid string) (*esim.ESIM, error)
	GetESIMByTranNo(ctx context.Context, tranNo string) (*esim.ESIM, error)
	UpdateESIM(ctx context.Context, esim *esim.ESIM) error
	DeleteESIM(ctx context.Context, id string) error
	ListESIMs(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.ESIM, int64, error)
	ListESIMsByUserID(ctx context.Context, userID string, status []esim.Status, page, pageSize int) ([]*esim.ESIM, int64, error)

	// ESIM使用情况操作
	CreateESIMUsage(ctx context.Context, usage *esim.ESIMUsage) error
	GetESIMUsageByID(ctx context.Context, id string) (*esim.ESIMUsage, error)
	GetESIMUsageByESIMID(ctx context.Context, esimID string) (*esim.ESIMUsage, error)
	UpdateESIMUsage(ctx context.Context, usage *esim.ESIMUsage) error
	DeleteESIMUsage(ctx context.Context, id string) error

	// 套餐操作
	CreatePackage(ctx context.Context, pkg *esim.Package) error
	GetPackageByID(ctx context.Context, id string) (*esim.Package, error)
	GetPackageByExternalID(ctx context.Context, providerType, externalID string) (*esim.Package, error)
	UpdatePackage(ctx context.Context, pkg *esim.Package) error
	DeletePackage(ctx context.Context, id string) error
	ListPackages(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Package, int64, error)
	ListPackagesByLocation(ctx context.Context, locationCode string, page, pageSize int) ([]*esim.Package, int64, error)

	// 区域操作
	CreateRegion(ctx context.Context, region *esim.Region) error
	GetRegionByID(ctx context.Context, id string) (*esim.Region, error)
	GetRegionByCode(ctx context.Context, providerType, code string) (*esim.Region, error)
	UpdateRegion(ctx context.Context, region *esim.Region) error
	DeleteRegion(ctx context.Context, id string) error
	ListRegions(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Region, int64, error)
	ListRegionsByType(ctx context.Context, regionType esim.RegionType, page, pageSize int) ([]*esim.Region, int64, error)

	// GetByESIMTranNo 根据ESIMTranNo获取eSIM
	GetByESIMTranNo(ctx context.Context, esimTranNo string) (*esim.ESIM, error)

	// GetByExternalOrderNo 根据提供商订单号获取eSIM
	GetByExternalOrderNo(ctx context.Context, externalOrderNo string) (*esim.ESIM, error)

	// GetUserESIMs 获取用户的eSIM列表
	GetUserESIMs(ctx context.Context, userID string, page, pageSize int) ([]*esim.ESIM, int64, error)

	// GetByOrderID 根据订单ID获取eSIM
	GetByOrderID(ctx context.Context, orderID string) ([]*esim.ESIM, error)

	// GetExpiredESIMs 获取已过期的eSIM
	GetExpiredESIMs(ctx context.Context, before time.Time, limit int) ([]*esim.ESIM, error)
}

// PackageRepository 套餐存储库接口
type PackageRepository interface {
	// Create 创建套餐
	Create(ctx context.Context, pkg *esim.Package) error

	// GetByID 根据ID获取套餐
	GetByID(ctx context.Context, id string) (*esim.Package, error)

	// GetByExternalID 根据提供商套餐ID获取套餐
	GetByExternalID(ctx context.Context, providerType, externalID string) (*esim.Package, error)

	// Update 更新套餐
	Update(ctx context.Context, pkg *esim.Package) error

	// ListPackages 获取套餐列表，支持条件过滤
	ListPackages(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Package, int64, error)

	// ListByProvider 获取指定提供商的所有套餐
	ListByProvider(ctx context.Context, providerType string) ([]*esim.Package, error)

	// ListActive 获取活跃套餐列表
	ListActive(ctx context.Context, providerType string, page, pageSize int) ([]*esim.Package, int64, error)
}

// RegionRepository 区域存储库接口
type RegionRepository interface {
	// Create 创建区域
	Create(ctx context.Context, region *esim.Region) error

	// GetByID 根据ID获取区域
	GetByID(ctx context.Context, id string) (*esim.Region, error)

	// GetByCode 根据区域代码获取区域
	GetByCode(ctx context.Context, providerType, code string) (*esim.Region, error)

	// Update 更新区域
	Update(ctx context.Context, region *esim.Region) error

	// ListRegions 获取区域列表，支持条件过滤
	ListRegions(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.Region, int64, error)

	// ListByProvider 获取指定提供商的所有区域
	ListByProvider(ctx context.Context, providerType string) ([]*esim.Region, error)
}

// SubLocationRepository 子区域存储库接口
type SubLocationRepository interface {
	// Create 创建子区域
	Create(ctx context.Context, subLocation *esim.SubLocation) error

	// GetByID 根据ID获取子区域
	GetByID(ctx context.Context, id string) (*esim.SubLocation, error)

	// GetByCode 根据子区域代码获取子区域
	GetByCode(ctx context.Context, providerType, code string) (*esim.SubLocation, error)

	// Update 更新子区域
	Update(ctx context.Context, subLocation *esim.SubLocation) error

	// ListSubLocations 获取子区域列表，支持条件过滤
	ListSubLocations(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*esim.SubLocation, int64, error)

	// GetRegionSubLocations 获取指定区域的所有子区域
	GetRegionSubLocations(ctx context.Context, regionID string) ([]*esim.SubLocation, error)
}

// RegionSubLocationMapRepository 区域-子区域映射存储库接口
type RegionSubLocationMapRepository interface {
	// Create 创建区域-子区域映射
	CreateMapping(ctx context.Context, regionID, subLocationID, providerType string) error

	// DeleteMapping 删除区域-子区域映射
	DeleteMapping(ctx context.Context, regionID, subLocationID string) error

	// GetRegionsBySubLocation 根据子区域获取关联的所有区域
	GetRegionsBySubLocation(ctx context.Context, subLocationID string) ([]*esim.Region, error)

	// GetSubLocationsByRegion 根据区域获取关联的所有子区域
	GetSubLocationsByRegion(ctx context.Context, regionID string) ([]*esim.SubLocation, error)
}

// SyncLogRepository 同步日志存储库接口
type SyncLogRepository interface {
	// Create 创建同步日志
	Create(ctx context.Context, syncLog *SyncLog) error

	// Update 更新同步日志
	Update(ctx context.Context, syncLog *SyncLog) error

	// GetByID 根据ID获取同步日志
	GetByID(ctx context.Context, id string) (*SyncLog, error)

	// GetLatestByEntity 获取指定实体类型的最新同步日志
	GetLatestByEntity(ctx context.Context, entityType, providerType string) (*SyncLog, error)

	// List 获取同步日志列表
	List(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*SyncLog, int64, error)
}

// SyncLog 同步日志模型
type SyncLog struct {
	ID           string    `json:"id"`
	EntityType   string    `json:"entityType"` // package, region, esim
	ProviderType string    `json:"providerType"`
	SyncType     string    `json:"syncType"` // full, incremental
	StartTime    time.Time `json:"startTime"`
	EndTime      time.Time `json:"endTime,omitempty"`
	AddedCount   int       `json:"addedCount"`
	UpdatedCount int       `json:"updatedCount"`
	DeletedCount int       `json:"deletedCount"`
	ErrorCount   int       `json:"errorCount"`
	ErrorDetails string    `json:"errorDetails,omitempty"`
	Status       string    `json:"status"` // running, completed, failed
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}
