package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/order"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/internal/repository/postgres"
	"vereal/letsesim/pkg/esim"
)

// OrderService 订单服务
type OrderService struct {
	orderRepo    repository.OrderRepository
	userService  *UserService
	esimService  *ESIMService
	promoService *PromotionService
	db           *gorm.DB
}

// NewOrderService 创建新的订单服务
func NewOrderService(
	orderRepo repository.OrderRepository,
	userService *UserService,
	esimService *ESIMService,
	promoService *PromotionService,
	db *gorm.DB,
) *OrderService {
	return &OrderService{
		orderRepo:    orderRepo,
		userService:  userService,
		esimService:  esimService,
		promoService: promoService,
		db:           db,
	}
}

// CreateESIMOrder 创建eSIM订单
func (s *OrderService) CreateESIMOrder(
	ctx context.Context,
	userID string,
	providerType string,
	packageID string,
	quantity int,
	promotionCode string,
	metadata map[string]interface{},
) (*order.Order, error) {
	var newOrder *order.Order
	var resultErr error

	err := s.db.Transaction(func(tx *gorm.DB) error {
		orderRepo := postgres.NewOrderRepository(tx)

		// 获取套餐详情
		packageDetails, err := s.esimService.GetPackageDetails(ctx, providerType, packageID)
		if err != nil {
			resultErr = fmt.Errorf("failed to get package details: %w", err)
			return resultErr
		}

		// 计算订单金额
		unitPrice := packageDetails.Price
		totalAmount := unitPrice * int64(quantity)

		// 创建交易ID
		transactionID := uuid.New().String()

		// 创建订单
		newOrder = order.NewOrder(userID, providerType, transactionID, totalAmount, packageDetails.Currency)
		newOrder.ID = uuid.New().String()

		// 添加订单项
		newOrder.AddItem(
			packageID,
			packageDetails.Name,
			quantity,
			unitPrice,
			packageDetails.Currency,
			packageDetails.DataVolume,
			packageDetails.ValidityDays,
			packageDetails.LocationCodes,
		)

		// 应用促销码
		if promotionCode != "" {
			discount, promoID, err := s.promoService.ApplyPromotion(ctx, promotionCode, totalAmount)
			if err != nil {
				resultErr = fmt.Errorf("failed to apply promotion: %w", err)
				return resultErr
			}

			if discount > 0 {
				newOrder.TotalAmount -= discount
				newOrder.PromotionID = promoID
			}
		}

		// 设置元数据
		if metadata != nil {
			newOrder.Metadata = metadata
		}

		// 保存订单
		if err := orderRepo.Create(ctx, newOrder); err != nil {
			resultErr = fmt.Errorf("failed to create order: %w", err)
			return resultErr
		}

		// 创建eSIM
		esimOrder := &esim.ESIMOrder{
			TransactionID:    transactionID,
			PackageID:        packageID,
			Count:            quantity,
			CustomerID:       userID,
			ProviderSpecific: metadata,
		}

		orderResult, err := s.esimService.CreateESIM(ctx, providerType, esimOrder)
		if err != nil {
			// 更新订单状态为失败
			newOrder.UpdateStatus(order.StatusFailed)
			if newOrder.Metadata == nil {
				newOrder.Metadata = map[string]interface{}{}
			}
			newOrder.Metadata["error"] = err.Error()
			orderRepo.Update(ctx, newOrder)

			resultErr = fmt.Errorf("failed to create eSIM: %w", err)
			return resultErr
		}

		// 更新订单状态
		newOrder.ProviderOrderNo = orderResult.OrderNo

		// 如果是异步处理
		if orderResult.IsPending {
			newOrder.UpdateStatus(order.StatusProcessing)
			if newOrder.Metadata == nil {
				newOrder.Metadata = map[string]interface{}{}
			}
			newOrder.Metadata["taskID"] = orderResult.TaskID
		} else {
			// 如果是同步完成
			newOrder.UpdateStatus(order.StatusCompleted)

			// 添加eSIM到订单
			for _, esim := range orderResult.ESIMs {
				newOrder.AddESIM(esim)
			}
		}

		// 更新订单
		if err := orderRepo.Update(ctx, newOrder); err != nil {
			resultErr = fmt.Errorf("failed to update order: %w", err)
			return resultErr
		}

		return nil
	})
	if err != nil {
		return nil, resultErr
	}
	return newOrder, nil
}

// GetOrderByID 根据ID获取订单
func (s *OrderService) GetOrderByID(ctx context.Context, id string) (*order.Order, error) {
	return s.orderRepo.GetByID(ctx, id)
}

// GetOrderByTransactionID 根据交易ID获取订单
func (s *OrderService) GetOrderByTransactionID(ctx context.Context, transactionID string) (*order.Order, error) {
	return s.orderRepo.GetByTransactionID(ctx, transactionID)
}

// GetUserOrders 获取用户订单
func (s *OrderService) GetUserOrders(ctx context.Context, userID string, startDate, endDate time.Time, page, pageSize int) ([]*order.Order, int64, error) {
	return s.orderRepo.GetUserOrders(ctx, userID, startDate, endDate, page, pageSize)
}

// ProcessOrderWebhook 处理订单Webhook
func (s *OrderService) ProcessOrderWebhook(ctx context.Context, providerType string, payload []byte, headers map[string][]string) error {
	// 调用eSIM服务处理Webhook
	if err := s.esimService.HandleWebhook(ctx, providerType, payload, headers); err != nil {
		return err
	}

	// 解析Webhook事件
	event, err := s.parseWebhookEvent(ctx, providerType, payload, headers)
	if err != nil {
		return err
	}

	// 如果是订单状态事件
	if event.EventType == esim.EventTypeOrderStatus {
		// 获取订单号
		orderNo, ok := event.Payload["orderNo"].(string)
		if !ok || orderNo == "" {
			return fmt.Errorf("invalid order number in webhook event")
		}

		// 获取订单
		order, err := s.orderRepo.GetByProviderOrderNo(ctx, orderNo)
		if err != nil {
			return fmt.Errorf("failed to get order by provider order number: %w", err)
		}

		// 如果订单正在处理中
		if order.Status == "PROCESSING" {
			// 获取eSIM详情
			esimQueryParams := esim.ESIMQueryParams{
				OrderNo:  orderNo,
				PageSize: 100,
				PageNum:  1,
			}

			esimList, err := s.esimService.ListESIMs(ctx, providerType, esimQueryParams)
			if err != nil {
				return fmt.Errorf("failed to get eSIM details: %w", err)
			}

			// 添加eSIM到订单
			for _, esim := range esimList.ESIMs {
				order.AddESIM(esim)
			}

			// 更新订单状态
			order.UpdateStatus("COMPLETED")

			// 保存更新
			if err := s.orderRepo.Update(ctx, order); err != nil {
				return fmt.Errorf("failed to update order: %w", err)
			}
		}
	}

	return nil
}

// parseWebhookEvent 解析Webhook事件
func (s *OrderService) parseWebhookEvent(ctx context.Context, providerType string, payload []byte, headers map[string][]string) (*esim.WebhookEvent, error) {
	// 获取提供商
	provider, err := s.esimService.GetProvider(ctx, providerType)
	if err != nil {
		return nil, err
	}

	// 处理Webhook事件
	return provider.ProcessWebhookEvent(ctx, payload, headers)
}

// CancelOrder 取消订单
func (s *OrderService) CancelOrder(ctx context.Context, id string) error {
	// 获取订单
	order, err := s.orderRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get order: %w", err)
	}

	// 检查订单是否可取消
	if !order.IsCancellable() {
		return esim.NewESIMError(
			"ORDER_NOT_CANCELLABLE",
			"Order cannot be cancelled in its current status",
			nil,
			"",
		)
	}

	// 如果订单已有eSIM资源并且提供商支持取消
	if len(order.ESIMs) > 0 {
		// 依次取消所有eSIM
		for _, esimInfo := range order.ESIMs {
			esimID := esim.ESIMIdentifier{
				ICCID:      esimInfo.ICCID,
				ESIMTranNo: esimInfo.ESIMTranNo,
			}

			// 尝试取消eSIM
			err := s.esimService.ManageESIM(ctx, order.ProviderType, esimID, "cancel")
			if err != nil {
				// 记录错误但继续处理其他eSIM
				fmt.Printf("Failed to cancel eSIM %s: %v\n", esimInfo.ICCID, err)
			}
		}
	}

	// 更新订单状态
	order.UpdateStatus("CANCELLED")

	// 如果有使用促销码，需要恢复促销码使用次数
	if order.PromotionID != "" {
		if err := s.promoService.RestorePromotion(ctx, order.PromotionID); err != nil {
			// 记录错误但不影响取消流程
			fmt.Printf("Failed to restore promotion: %v\n", err)
		}
	}

	// 保存更新
	if err := s.orderRepo.Update(ctx, order); err != nil {
		return fmt.Errorf("failed to update order: %w", err)
	}

	return nil
}
