package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"vereal/letsesim/internal/domain/promotion"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

// PromotionService 促销服务
type PromotionService struct {
	promotionRepo repository.PromotionRepository
}

// NewPromotionService 创建新的促销服务
func NewPromotionService(promotionRepo repository.PromotionRepository) *PromotionService {
	return &PromotionService{
		promotionRepo: promotionRepo,
	}
}

// CreatePromotion 创建促销
func (s *PromotionService) CreatePromotion(
	ctx context.Context,
	code string,
	description string,
	promotionType promotion.Type,
	value int64,
	minOrderValue int64,
	maxDiscount int64,
	startDate time.Time,
	endDate time.Time,
	usageLimit int,
	resellerID string,
	metadata map[string]interface{},
) (*promotion.Promotion, error) {
	// 检查代码是否已存在
	existingPromo, err := s.promotionRepo.GetByCode(ctx, code)
	if err != nil && !IsNotFoundError(err) {
		return nil, fmt.Errorf("failed to check promotion code: %w", err)
	}

	if existingPromo != nil {
		return nil, esim.NewESIMError(
			"PROMOTION_CODE_EXISTS",
			"Promotion code already exists",
			nil,
			"",
		)
	}

	// 创建新促销
	newPromo := promotion.NewPromotion(code, description, promotionType, value)
	newPromo.ID = uuid.New().String()
	newPromo.MinOrderValue = minOrderValue
	newPromo.MaxDiscount = maxDiscount
	newPromo.StartDate = startDate
	newPromo.EndDate = endDate
	newPromo.UsageLimit = usageLimit
	newPromo.ResellerID = resellerID

	if metadata != nil {
		newPromo.Metadata = metadata
	}

	// 保存促销
	if err := s.promotionRepo.Create(ctx, newPromo); err != nil {
		return nil, fmt.Errorf("failed to create promotion: %w", err)
	}

	return newPromo, nil
}

// GetPromotionByCode 根据代码获取促销
func (s *PromotionService) GetPromotionByCode(ctx context.Context, code string) (*promotion.Promotion, error) {
	return s.promotionRepo.GetByCode(ctx, code)
}

// GetPromotionByID 根据ID获取促销
func (s *PromotionService) GetPromotionByID(ctx context.Context, id string) (*promotion.Promotion, error) {
	return s.promotionRepo.GetByID(ctx, id)
}

// ApplyPromotion 应用促销码到订单
func (s *PromotionService) ApplyPromotion(ctx context.Context, code string, orderAmount int64) (int64, string, error) {
	// 获取促销
	promo, err := s.promotionRepo.GetByCode(ctx, code)
	if err != nil {
		if IsNotFoundError(err) {
			return 0, "", esim.NewESIMError(
				"PROMOTION_NOT_FOUND",
				"Promotion code not found",
				nil,
				"",
			)
		}
		return 0, "", fmt.Errorf("failed to get promotion: %w", err)
	}

	// 检查促销是否活动
	if !promo.IsActive() {
		return 0, "", esim.NewESIMError(
			"PROMOTION_INACTIVE",
			"Promotion is not active",
			nil,
			"",
		)
	}

	// 计算折扣
	discount := promo.CalculateDiscount(orderAmount)
	if discount <= 0 {
		return 0, "", nil
	}

	// 使用促销
	if !promo.Use() {
		return 0, "", esim.NewESIMError(
			"PROMOTION_USE_FAILED",
			"Failed to use promotion",
			nil,
			"",
		)
	}

	// 更新促销
	if err := s.promotionRepo.Update(ctx, promo); err != nil {
		return 0, "", fmt.Errorf("failed to update promotion: %w", err)
	}

	return discount, promo.ID, nil
}

// RestorePromotion 恢复促销使用次数
func (s *PromotionService) RestorePromotion(ctx context.Context, id string) error {
	// 获取促销
	promo, err := s.promotionRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get promotion: %w", err)
	}

	// 减少使用次数
	if promo.CurrentUsage > 0 {
		promo.CurrentUsage--
	}

	// 如果促销已过期但使用次数未达到限制，恢复状态
	if promo.Status == promotion.StatusExpired && promo.UsageLimit > 0 && promo.CurrentUsage < promo.UsageLimit {
		now := time.Now()
		if now.Before(promo.EndDate) {
			promo.Status = promotion.StatusActive
		}
	}

	promo.UpdatedAt = time.Now()

	// 更新促销
	if err := s.promotionRepo.Update(ctx, promo); err != nil {
		return fmt.Errorf("failed to update promotion: %w", err)
	}

	return nil
}

// ListPromotions 获取促销列表
func (s *PromotionService) ListPromotions(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*promotion.Promotion, int64, error) {
	return s.promotionRepo.List(ctx, filter, page, pageSize)
}

// GetActivePromotions 获取活动促销
func (s *PromotionService) GetActivePromotions(ctx context.Context, filter map[string]interface{}) ([]*promotion.Promotion, error) {
	return s.promotionRepo.GetActivePromotions(ctx, filter)
}

// UpdatePromotion 更新促销
func (s *PromotionService) UpdatePromotion(
	ctx context.Context,
	id string,
	description string,
	value int64,
	minOrderValue int64,
	maxDiscount int64,
	endDate time.Time,
	usageLimit int,
	status string,
) (*promotion.Promotion, error) {
	// 获取促销
	promo, err := s.promotionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get promotion: %w", err)
	}

	// 更新促销信息
	if description != "" {
		promo.Description = description
	}

	if value > 0 {
		promo.Value = value
	}

	promo.MinOrderValue = minOrderValue
	promo.MaxDiscount = maxDiscount

	if !endDate.IsZero() {
		promo.EndDate = endDate
	}

	if usageLimit > 0 {
		promo.UsageLimit = usageLimit
	}

	if status != "" {
		promo.Status = promotion.Status(status)
	}

	promo.UpdatedAt = time.Now()

	// 保存更新
	if err := s.promotionRepo.Update(ctx, promo); err != nil {
		return nil, fmt.Errorf("failed to update promotion: %w", err)
	}

	return promo, nil
}

// DeletePromotion 删除促销
func (s *PromotionService) DeletePromotion(ctx context.Context, id string) error {
	return s.promotionRepo.Delete(ctx, id)
}
