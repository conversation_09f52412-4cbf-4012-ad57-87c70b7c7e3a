package service

import (
	"context"
	"fmt"
	"time"

	"vereal/letsesim/internal/domain/esim"
	"vereal/letsesim/internal/repository"
	pkgesim "vereal/letsesim/pkg/esim"
)

// ESIMService eSIM服务，协调不同提供商和异步操作
type ESIMService struct {
	providerFactory pkgesim.ESIMProviderFactoryInterface
	taskTracker     pkgesim.AsyncTaskTracker
	webhookHandler  pkgesim.WebhookHandler
	packageRepo     repository.PackageRepository // 添加套餐存储库
	esimRepo        repository.ESIMRepository    // 添加eSIM存储库
	regionRepo      repository.RegionRepository  // 添加区域存储库
}

// NewESIMService 创建新的eSIM服务
func NewESIMService(
	providerFactory pkgesim.ESIMProviderFactoryInterface,
	taskTracker pkgesim.AsyncTaskTracker,
	webhookHandler pkgesim.WebhookHandler,
	packageRepo repository.PackageRepository,
	esimRepo repository.ESIMRepository,
	regionRepo repository.RegionRepository,
) *ESIMService {
	return &ESIMService{
		providerFactory: providerFactory,
		taskTracker:     taskTracker,
		webhookHandler:  webhookHandler,
		packageRepo:     packageRepo,
		esimRepo:        esimRepo,
		regionRepo:      regionRepo,
	}
}

// GetProvider 获取指定类型的提供商
func (s *ESIMService) GetProvider(ctx context.Context, providerType string) (pkgesim.ESIMProviderInterface, error) {
	return s.providerFactory.GetProvider(ctx, providerType)
}

// ListProviders 获取所有可用的提供商
func (s *ESIMService) ListProviders(ctx context.Context) []pkgesim.ProviderInfo {
	return s.providerFactory.GetProviders(ctx)
}

// ListPackages 获取指定提供商的套餐列表
func (s *ESIMService) ListPackages(ctx context.Context, providerType string, params pkgesim.PackageQueryParams) ([]pkgesim.Package, *pkgesim.PaginationResult, error) {
	// 优先从本地数据库获取套餐列表
	filter := map[string]interface{}{
		"provider_type": providerType,
		"status":        string(esim.PackageStatusActive),
	}

	// 添加其他查询条件
	if params.LocationCode != "" {
		filter["location_codes LIKE ?"] = "%" + params.LocationCode + "%"
	}

	if params.Type != "" {
		filter["data_type"] = params.Type
	}

	if params.PackageCode != "" {
		filter["external_id"] = params.PackageCode
	}

	// 尝试从本地数据库获取
	localPackages, total, err := s.packageRepo.ListPackages(ctx, filter, params.PageNum, params.PageSize)
	if err != nil || len(localPackages) == 0 {
		// 本地数据不存在或出错，从提供商获取
		provider, err := s.GetProvider(ctx, providerType)
		if err != nil {
			return nil, nil, err
		}

		return provider.ListPackages(ctx, params)
	}

	// 转换为API类型
	result := make([]pkgesim.Package, len(localPackages))
	for i, pkg := range localPackages {
		result[i] = pkgesim.Package{
			ID:            pkg.ExternalID,
			Name:          pkg.Name,
			Description:   pkg.Description,
			Price:         int64(pkg.Price),
			Currency:      pkg.Currency,
			DataVolume:    pkg.DataVolume,
			ValidityDays:  pkg.ValidityDays,
			LocationCodes: pkg.LocationCodes,
			SupportsSMS:   pkg.SupportsSMS,
			DataType:      pkg.DataType,
			NetworkTypes:  pkg.NetworkTypes,
			SupportTopUp:  pkg.SupportTopUp,
		}
	}

	pagination := &pkgesim.PaginationResult{
		Total:    total,
		PageNum:  params.PageNum,
		PageSize: params.PageSize,
	}

	return result, pagination, nil
}

// GetPackageDetails 获取指定提供商的套餐详情
func (s *ESIMService) GetPackageDetails(ctx context.Context, providerType string, packageId string) (*pkgesim.Package, error) {
	// 优先从本地数据库获取套餐详情
	localPackage, err := s.packageRepo.GetByExternalID(ctx, providerType, packageId)
	if err != nil || localPackage == nil {
		// 本地数据不存在或出错，从提供商获取
		provider, err := s.GetProvider(ctx, providerType)
		if err != nil {
			return nil, err
		}

		return provider.GetPackageDetails(ctx, packageId)
	}

	// 转换为API类型
	result := &pkgesim.Package{
		ID:            localPackage.ExternalID,
		Name:          localPackage.Name,
		Description:   localPackage.Description,
		Price:         int64(localPackage.Price),
		Currency:      localPackage.Currency,
		DataVolume:    localPackage.DataVolume,
		ValidityDays:  localPackage.ValidityDays,
		LocationCodes: localPackage.LocationCodes,
		SupportsSMS:   localPackage.SupportsSMS,
		DataType:      localPackage.DataType,
		NetworkTypes:  localPackage.NetworkTypes,
		SupportTopUp:  localPackage.SupportTopUp,
	}

	return result, nil
}

// CreateESIM 创建eSIM
func (s *ESIMService) CreateESIM(ctx context.Context, providerType string, order *pkgesim.ESIMOrder) (*pkgesim.ESIMOrderResult, error) {
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return nil, err
	}

	return provider.CreateESIM(ctx, order)
}

// GetESIMDetails 获取eSIM详情
func (s *ESIMService) GetESIMDetails(ctx context.Context, providerType string, esimID pkgesim.ESIMIdentifier) (*pkgesim.ESIM, error) {
	// 优先从本地数据库获取eSIM详情
	var localESIM *esim.ESIM
	var err error

	if esimID.ICCID != "" {
		localESIM, err = s.esimRepo.GetESIMByICCID(ctx, esimID.ICCID)
	} else if esimID.ESIMTranNo != "" {
		localESIM, err = s.esimRepo.GetByESIMTranNo(ctx, esimID.ESIMTranNo)
	} else if esimID.OrderNo != "" {
		localESIM, err = s.esimRepo.GetByExternalOrderNo(ctx, esimID.OrderNo)
	}

	if err == nil && localESIM != nil {
		// 本地数据存在但需要更新实时数据（使用量、状态等）
		provider, err := s.GetProvider(ctx, providerType)
		if err != nil {
			return nil, err
		}

		// 构建eSIM标识
		identifier := pkgesim.ESIMIdentifier{
			ICCID:      localESIM.ICCID,
			ESIMTranNo: localESIM.ESIMTranNo,
			OrderNo:    localESIM.ExternalOrderNo,
		}

		// 获取最新数据
		latestInfo, err := provider.GetESIMDetails(ctx, identifier)
		if err != nil {
			// 如果提供商API失败，返回本地存储的数据
			return convertLocalESIMToAPI(localESIM), nil
		}

		// 更新本地数据
		s.updateLocalESIM(ctx, localESIM, latestInfo)

		return latestInfo, nil
	}

	// 本地数据不存在，从提供商获取
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetESIMDetails(ctx, esimID)
}

// ListESIMs 获取eSIM列表
func (s *ESIMService) ListESIMs(ctx context.Context, providerType string, params pkgesim.ESIMQueryParams) (*pkgesim.ESIMListResult, error) {
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return nil, err
	}

	return provider.ListESIMs(ctx, params)
}

// TopUpESIM 充值eSIM
func (s *ESIMService) TopUpESIM(ctx context.Context, providerType string, params pkgesim.TopUpParams) (*pkgesim.TopUpResult, error) {
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return nil, err
	}

	return provider.TopUpESIM(ctx, params)
}

// ManageESIM 管理eSIM状态
func (s *ESIMService) ManageESIM(ctx context.Context, providerType string, esimID pkgesim.ESIMIdentifier, action string) error {
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return err
	}

	switch action {
	case "cancel":
		return provider.CancelESIM(ctx, esimID)
	case "suspend":
		return provider.SuspendESIM(ctx, esimID)
	case "resume":
		return provider.ResumeESIM(ctx, esimID)
	case "revoke":
		return provider.RevokeESIM(ctx, esimID)
	default:
		return pkgesim.NewESIMError(
			pkgesim.ErrInvalidParams,
			fmt.Sprintf("Invalid action: %s", action),
			nil,
			"",
		)
	}
}

// SendSMS 发送短信到eSIM
func (s *ESIMService) SendSMS(ctx context.Context, providerType string, params pkgesim.SMSParams) error {
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return err
	}

	return provider.SendSMS(ctx, params)
}

// GetESIMUsage 获取eSIM使用情况
func (s *ESIMService) GetESIMUsage(ctx context.Context, providerType string, esimID pkgesim.ESIMIdentifier) (*pkgesim.ESIMUsage, error) {
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetESIMUsage(ctx, esimID)
}

// GetAccountBalance 获取账户余额
func (s *ESIMService) GetAccountBalance(ctx context.Context, providerType string) (*pkgesim.AccountBalance, error) {
	provider, err := s.GetProvider(ctx, providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetAccountBalance(ctx)
}

// ListSupportedRegions 获取支持的区域列表
func (s *ESIMService) ListSupportedRegions(ctx context.Context, providerType string) ([]pkgesim.Region, error) {
	// 优先从本地数据库获取区域列表
	localRegions, err := s.regionRepo.ListByProvider(ctx, providerType)
	if err != nil || len(localRegions) == 0 {
		// 本地数据不存在或出错，从提供商获取
		provider, err := s.GetProvider(ctx, providerType)
		if err != nil {
			return nil, err
		}

		return provider.ListSupportedRegions(ctx)
	}

	// 转换为API类型
	result := make([]pkgesim.Region, 0, len(localRegions))
	for _, region := range localRegions {
		// 获取子区域
		subLocations, err := s.getSubLocationsByRegion(ctx, region.ID)
		if err != nil {
			// 记录错误但继续处理其他区域
			fmt.Printf("Failed to get sub locations for region %s: %v\n", region.ID, err)
			continue
		}

		// 将RegionType转换为整数类型
		var typeValue int
		switch region.Type {
		case esim.RegionTypeContinent:
			typeValue = 3 // 假设3表示大洲
		case esim.RegionTypeCountry:
			typeValue = 1 // 假设1表示国家
		case esim.RegionTypeRegion:
			typeValue = 2 // 假设2表示地区
		default:
			typeValue = 0 // 未知类型
		}

		apiRegion := pkgesim.Region{
			Code:         region.Code,
			Name:         region.Name,
			Type:         typeValue,
			SubLocations: subLocations,
		}

		result = append(result, apiRegion)
	}

	return result, nil
}

// 获取区域对应的子区域
func (s *ESIMService) getSubLocationsByRegion(ctx context.Context, regionID string) ([]pkgesim.SubLocation, error) {
	// 获取子区域
	subLocationRepo, ok := s.regionRepo.(interface {
		GetRegionSubLocations(context.Context, string) ([]*esim.SubLocation, error)
	})

	if !ok {
		// 如果接口不匹配，使用映射存储库
		mapRepo, ok := s.regionRepo.(interface {
			GetSubLocationsByRegion(context.Context, string) ([]*esim.SubLocation, error)
		})
		if !ok {
			return []pkgesim.SubLocation{}, nil
		}

		subLocations, err := mapRepo.GetSubLocationsByRegion(ctx, regionID)
		if err != nil {
			return nil, err
		}

		result := make([]pkgesim.SubLocation, len(subLocations))
		for i, loc := range subLocations {
			result[i] = pkgesim.SubLocation{
				Code: loc.Code,
				Name: loc.Name,
			}
		}

		return result, nil
	}

	subLocations, err := subLocationRepo.GetRegionSubLocations(ctx, regionID)
	if err != nil {
		return nil, err
	}

	result := make([]pkgesim.SubLocation, len(subLocations))
	for i, loc := range subLocations {
		result[i] = pkgesim.SubLocation{
			Code: loc.Code,
			Name: loc.Name,
		}
	}

	return result, nil
}

// HandleWebhook 处理来自提供商的Webhook
func (s *ESIMService) HandleWebhook(ctx context.Context, providerType string, payload []byte, headers map[string][]string) error {
	return s.webhookHandler.HandleWebhook(ctx, providerType, payload, headers)
}

// WaitForOrderCompletion 等待订单完成
func (s *ESIMService) WaitForOrderCompletion(ctx context.Context, taskID string, timeout time.Duration) (*pkgesim.TaskStatus, error) {
	return s.taskTracker.PollForCompletion(ctx, taskID, timeout)
}

// 将本地eSIM转换为API类型
func convertLocalESIMToAPI(localESIM *esim.ESIM) *pkgesim.ESIM {
	return &pkgesim.ESIM{
		Identifier: pkgesim.ESIMIdentifier{
			ICCID:      localESIM.ICCID,
			ESIMTranNo: localESIM.ESIMTranNo,
			OrderNo:    localESIM.ExternalOrderNo,
		},
		Status: pkgesim.ESIMStatus{
			Code: string(localESIM.Status),
		},
		ActivationCode: localESIM.ActivationCode,
		QRCodeURL:      localESIM.QRCodeURL,
		DataVolume:     0, // 实时数据需要从提供商获取
		UsedData:       0, // 实时数据需要从提供商获取
		ValidityDays:   localESIM.ValidityDays,
		ExpiryTime:     localESIM.ExpiryTime,
		SMS: pkgesim.SMSCapability{
			Supported: localESIM.SupportsSMS,
			MSISDN:    localESIM.MSISDN,
			APIOnly:   localESIM.SMSAPIOnly,
		},
	}
}

// 更新本地eSIM数据
func (s *ESIMService) updateLocalESIM(ctx context.Context, localESIM *esim.ESIM, latestInfo *pkgesim.ESIM) {
	// 更新状态等信息
	localESIM.Status = esim.Status(latestInfo.Status.Code)
	localESIM.ActivationCode = latestInfo.ActivationCode
	localESIM.QRCodeURL = latestInfo.QRCodeURL
	localESIM.ValidityDays = latestInfo.ValidityDays
	localESIM.ExpiryTime = latestInfo.ExpiryTime
	localESIM.SupportsSMS = latestInfo.SMS.Supported
	localESIM.MSISDN = latestInfo.SMS.MSISDN
	localESIM.SMSAPIOnly = latestInfo.SMS.APIOnly
	localESIM.LastSyncAt = time.Now()

	// 更新本地数据库
	if err := s.esimRepo.UpdateESIM(ctx, localESIM); err != nil {
		// 记录错误但继续流程
		fmt.Printf("Failed to update local eSIM data: %v\n", err)
	}
}
