package service

import (
	"context"
	"fmt"

	"vereal/letsesim/internal/domain/credit"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

// CreditService 用户积分服务
type CreditService struct {
	creditRepo repository.CreditRepository
}

// NewCreditService 创建新的用户积分服务
func NewCreditService(creditRepo repository.CreditRepository) *CreditService {
	return &CreditService{
		creditRepo: creditRepo,
	}
}

// GetUserCredit 获取用户积分
func (s *CreditService) GetUserCredit(ctx context.Context, userID string) (*credit.Credit, error) {
	return s.creditRepo.GetByUserID(ctx, userID)
}

// EnsureUserCredit 确保用户有积分账户，如果没有则创建
func (s *CreditService) EnsureUserCredit(ctx context.Context, userID string, currency string) (*credit.Credit, error) {
	userCredit, err := s.creditRepo.GetByUserID(ctx, userID)
	if err != nil {
		if !IsNotFoundError(err) {
			return nil, fmt.Errorf("failed to get user credit: %w", err)
		}

		// 创建新的用户积分
		newCredit := credit.NewCredit(userID, currency)
		if err := s.creditRepo.Create(ctx, newCredit); err != nil {
			return nil, fmt.Errorf("failed to create user credit: %w", err)
		}
		return newCredit, nil
	}
	return userCredit, nil
}

// AddBalance 增加用户余额
func (s *CreditService) AddBalance(ctx context.Context, userID string, amount float64, description string, orderID string) (*credit.Transaction, error) {
	// 使用事务确保数据一致性
	userCredit, err := s.creditRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 更新余额
	userCredit.AddBalance(amount)
	if err := s.creditRepo.Update(ctx, userCredit); err != nil {
		return nil, fmt.Errorf("failed to update balance: %w", err)
	}

	// 创建交易记录
	transaction := credit.NewTransaction(
		userID,
		amount,
		userCredit.Balance,
		credit.TypeDeposit,
		description,
		orderID,
	)

	if err := s.creditRepo.CreateTransaction(ctx, transaction); err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	return transaction, nil
}

// DeductBalance 扣除用户余额
func (s *CreditService) DeductBalance(ctx context.Context, userID string, amount float64, description string, orderID string) (*credit.Transaction, error) {
	// 检查用户余额
	userCredit, err := s.creditRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 检查余额是否充足
	if !userCredit.HasSufficientBalance(amount) {
		return nil, esim.NewESIMError(
			"INSUFFICIENT_BALANCE",
			fmt.Sprintf("Insufficient balance: %.2f", userCredit.Balance),
			nil,
			"",
		)
	}

	// 更新余额
	userCredit.DeductBalance(amount)
	if err := s.creditRepo.Update(ctx, userCredit); err != nil {
		return nil, fmt.Errorf("failed to update balance: %w", err)
	}

	// 创建交易记录
	transaction := credit.NewTransaction(
		userID,
		-amount, // 负数表示扣除
		userCredit.Balance,
		credit.TypeOrderPayment,
		description,
		orderID,
	)

	if err := s.creditRepo.CreateTransaction(ctx, transaction); err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	return transaction, nil
}

// RefundBalance 退款到用户余额
func (s *CreditService) RefundBalance(ctx context.Context, userID string, amount float64, description string, orderID string) (*credit.Transaction, error) {
	return s.AddBalance(ctx, userID, amount, description, orderID)
}

// GetTransactionHistory 获取用户交易历史
func (s *CreditService) GetTransactionHistory(ctx context.Context, userID string, page, pageSize int) ([]*credit.Transaction, int64, error) {
	return s.creditRepo.ListTransactions(ctx, userID, page, pageSize)
}

// GetTransaction 获取交易详情
func (s *CreditService) GetTransaction(ctx context.Context, transactionID string) (*credit.Transaction, error) {
	return s.creditRepo.GetTransactionByID(ctx, transactionID)
}

// GetOrderTransactions 获取订单相关交易
func (s *CreditService) GetOrderTransactions(ctx context.Context, orderID string) ([]*credit.Transaction, error) {
	return s.creditRepo.GetTransactionsByOrderID(ctx, orderID)
}
