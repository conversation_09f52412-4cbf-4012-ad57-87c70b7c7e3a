package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/order"
	"vereal/letsesim/internal/domain/reseller"
	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/internal/repository/postgres"
	"vereal/letsesim/pkg/esim"
)

// ResellerService 代理商服务
type ResellerService struct {
	resellerRepo repository.ResellerRepository
	userService  *UserService
	db           *gorm.DB
}

// NewResellerService 创建新的代理商服务
func NewResellerService(
	resellerRepo repository.ResellerRepository,
	userService *UserService,
	db *gorm.DB,
) *ResellerService {
	return &ResellerService{
		resellerRepo: resellerRepo,
		userService:  userService,
		db:           db,
	}
}

// CreateReseller 创建代理商
func (s *ResellerService) CreateReseller(
	ctx context.Context,
	email string,
	password string,
	companyName string,
	contactPerson string,
	phone string,
	country string,
	address string,
	commissionRate int,
	metadata map[string]interface{},
) (*reseller.Reseller, error) {
	var newReseller *reseller.Reseller
	var resultErr error

	err := s.db.Transaction(func(tx *gorm.DB) error {
		resellerRepo := postgres.NewResellerRepository(tx)
		// 假设 userService 也支持事务注入
		userService := s.userService
		if userService.db != nil {
			userService = NewUserService(postgres.NewUserRepository(tx), postgres.NewCreditRepository(tx), tx)
		}

		// 创建用户账户
		newUser, err := userService.CreateUser(ctx, email, password, contactPerson, phone, user.RoleReseller)
		if err != nil {
			resultErr = fmt.Errorf("failed to create user account: %w", err)
			return resultErr
		}

		// 创建代理商
		newReseller = reseller.NewReseller(newUser.ID, companyName, contactPerson, email, phone, country, commissionRate, reseller.TypeRegular)
		newReseller.ID = uuid.New().String()
		newReseller.Address = address

		// 生成API密钥和密钥
		apiKey := uuid.New().String()
		apiSecret := uuid.New().String()

		// 哈希API密钥（实际应用中可能需要更安全的处理方式）
		newReseller.APIKey = apiKey

		// 哈希API密钥
		hashedSecret, err := bcrypt.GenerateFromPassword([]byte(apiSecret), bcrypt.DefaultCost)
		if err != nil {
			resultErr = fmt.Errorf("failed to hash API secret: %w", err)
			return resultErr
		}
		newReseller.APISecret = string(hashedSecret)

		if metadata != nil {
			newReseller.Metadata = metadata
		}

		// 保存代理商
		if err := resellerRepo.Create(ctx, newReseller); err != nil {
			resultErr = fmt.Errorf("failed to create reseller: %w", err)
			return resultErr
		}

		return nil
	})
	if err != nil {
		return nil, resultErr
	}
	return newReseller, nil
}

// GetResellerByID 根据ID获取代理商
func (s *ResellerService) GetResellerByID(ctx context.Context, id string) (*reseller.Reseller, error) {
	return s.resellerRepo.GetByID(ctx, id)
}

// GetResellerByUserID 根据用户ID获取代理商
func (s *ResellerService) GetResellerByUserID(ctx context.Context, userID string) (*reseller.Reseller, error) {
	return s.resellerRepo.GetByUserID(ctx, userID)
}

// AuthenticateReseller 代理商API认证
func (s *ResellerService) AuthenticateReseller(ctx context.Context, apiKey string, apiSecret string) (*reseller.Reseller, error) {
	// 获取代理商
	resellerObj, err := s.resellerRepo.GetByAPIKey(ctx, apiKey)
	if err != nil {
		if IsNotFoundError(err) {
			return nil, esim.NewESIMError(
				"INVALID_API_KEY",
				"Invalid API key",
				nil,
				"",
			)
		}
		return nil, fmt.Errorf("failed to get reseller: %w", err)
	}

	// 检查代理商状态
	if !resellerObj.IsActive() {
		return nil, esim.NewESIMError(
			"RESELLER_INACTIVE",
			"Reseller account is inactive",
			nil,
			"",
		)
	}

	// 验证密钥
	err = bcrypt.CompareHashAndPassword([]byte(resellerObj.APISecret), []byte(apiSecret))
	if err != nil {
		return nil, esim.NewESIMError(
			"INVALID_API_SECRET",
			"Invalid API secret",
			nil,
			"",
		)
	}

	return resellerObj, nil
}

// UpdateReseller 更新代理商信息
func (s *ResellerService) UpdateReseller(
	ctx context.Context,
	id string,
	companyName string,
	contactPerson string,
	phone string,
	address string,
	commissionRate int,
	callbackURL string,
) (*reseller.Reseller, error) {
	// 获取代理商
	resellerObj, err := s.resellerRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get reseller: %w", err)
	}

	// 更新信息
	if companyName != "" {
		resellerObj.CompanyName = companyName
	}

	if contactPerson != "" {
		resellerObj.ContactPerson = contactPerson
	}

	if phone != "" {
		resellerObj.Phone = phone
	}

	if address != "" {
		resellerObj.Address = address
	}

	if commissionRate > 0 {
		resellerObj.CommissionRate = commissionRate
	}

	if callbackURL != "" {
		resellerObj.CallbackURL = callbackURL
	}

	resellerObj.UpdatedAt = time.Now()

	// 保存更新
	if err := s.resellerRepo.Update(ctx, resellerObj); err != nil {
		return nil, fmt.Errorf("failed to update reseller: %w", err)
	}

	return resellerObj, nil
}

// RegenerateAPIKey 重新生成API密钥
func (s *ResellerService) RegenerateAPIKey(ctx context.Context, id string) (string, string, error) {
	// 获取代理商
	resellerObj, err := s.resellerRepo.GetByID(ctx, id)
	if err != nil {
		return "", "", fmt.Errorf("failed to get reseller: %w", err)
	}

	// 生成新的API密钥和密钥
	apiKey := uuid.New().String()
	apiSecret := uuid.New().String()

	// 更新API密钥
	resellerObj.APIKey = apiKey

	// 哈希API密钥
	hashedSecret, err := bcrypt.GenerateFromPassword([]byte(apiSecret), bcrypt.DefaultCost)
	if err != nil {
		return "", "", fmt.Errorf("failed to hash API secret: %w", err)
	}
	resellerObj.APISecret = string(hashedSecret)

	resellerObj.UpdatedAt = time.Now()

	// 保存更新
	if err := s.resellerRepo.Update(ctx, resellerObj); err != nil {
		return "", "", fmt.Errorf("failed to update reseller: %w", err)
	}

	return apiKey, apiSecret, nil
}

// UpdateResellerStatus 更新代理商状态
func (s *ResellerService) UpdateResellerStatus(ctx context.Context, id string, status reseller.Status) error {
	// 获取代理商
	resellerObj, err := s.resellerRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get reseller: %w", err)
	}

	// 更新状态
	resellerObj.Status = status
	resellerObj.UpdatedAt = time.Now()

	// 保存更新
	if err := s.resellerRepo.Update(ctx, resellerObj); err != nil {
		return fmt.Errorf("failed to update reseller status: %w", err)
	}

	return nil
}

// AddResellerBalance 添加代理商余额
func (s *ResellerService) AddResellerBalance(ctx context.Context, id string, amount int64) error {
	// 获取代理商
	resellerObj, err := s.resellerRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get reseller: %w", err)
	}

	// 添加余额
	resellerObj.AddBalance(amount)

	// 保存更新
	if err := s.resellerRepo.Update(ctx, resellerObj); err != nil {
		return fmt.Errorf("failed to update reseller balance: %w", err)
	}

	return nil
}

// DeductResellerBalance 扣除代理商余额
func (s *ResellerService) DeductResellerBalance(ctx context.Context, id string, amount int64) error {
	// 获取代理商
	resellerObj, err := s.resellerRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get reseller: %w", err)
	}

	// 检查余额
	if !resellerObj.HasSufficientBalance(amount) {
		return esim.NewESIMError(
			"INSUFFICIENT_BALANCE",
			"Reseller has insufficient balance",
			nil,
			"",
		)
	}

	// 扣除余额
	if !resellerObj.DeductBalance(amount) {
		return esim.NewESIMError(
			"BALANCE_DEDUCTION_FAILED",
			"Failed to deduct balance",
			nil,
			"",
		)
	}

	// 保存更新
	if err := s.resellerRepo.Update(ctx, resellerObj); err != nil {
		return fmt.Errorf("failed to update reseller balance: %w", err)
	}

	return nil
}

// ListResellers 获取代理商列表
func (s *ResellerService) ListResellers(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*reseller.Reseller, int64, error) {
	return s.resellerRepo.List(ctx, filter, page, pageSize)
}

// ListUsers 获取代理商下的用户列表
func (s *ResellerService) ListUsers(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*user.User, int64, error) {
	// 添加代理商ID作为过滤条件
	resellerID := filter["resellerId"].(string)

	// 这里假设UserService有List方法可以根据条件查询用户
	// 如果UserService没有这样的方法，需要在UserService中添加
	userFilter := make(map[string]interface{})
	for k, v := range filter {
		userFilter[k] = v
	}

	// 确保过滤条件包含代理商ID
	userFilter["resellerID"] = resellerID

	// 调用UserService获取用户列表
	return s.userService.ListUsers(ctx, userFilter, page, pageSize)
}

// CreateUser 创建代理商下的用户
func (s *ResellerService) CreateUser(
	ctx context.Context,
	resellerID string,
	email string,
	password string,
	name string,
	mobile string,
) (*user.User, error) {
	// 获取代理商
	resellerObj, err := s.GetResellerByID(ctx, resellerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get reseller: %w", err)
	}

	// 检查代理商状态
	if !resellerObj.IsActive() {
		return nil, esim.NewESIMError(
			"RESELLER_INACTIVE",
			"Reseller account is inactive",
			nil,
			"",
		)
	}

	// 使用事务创建用户
	var newUser *user.User
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 创建用户仓储和服务
		userRepo := postgres.NewUserRepository(tx)
		creditRepo := postgres.NewCreditRepository(tx)
		userSvc := NewUserService(userRepo, creditRepo, tx)

		// 创建用户
		var err error
		newUser, err = userSvc.CreateUser(ctx, email, password, name, mobile, user.RoleUser)
		if err != nil {
			return err
		}

		// 更新用户的代理商ID
		// 这里假设User模型有ResellerID字段，直接更新该字段
		newUser.ResellerID = resellerID
		err = userRepo.Update(ctx, newUser)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return newUser, nil
}

// GetUserByID 获取指定ID的用户
func (s *ResellerService) GetUserByID(ctx context.Context, resellerID, userID string) (*user.User, error) {
	// 获取用户
	userObj, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 验证用户是否属于该代理商
	if err := s.VerifyUserBelongsToReseller(ctx, resellerID, userID); err != nil {
		return nil, err
	}

	return userObj, nil
}

// VerifyUserBelongsToReseller 验证用户是否属于指定代理商
func (s *ResellerService) VerifyUserBelongsToReseller(ctx context.Context, resellerID, userID string) error {
	// 获取用户
	userObj, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return err
	}

	// 检查用户是否属于该代理商
	if userObj.ResellerID != resellerID {
		return esim.NewESIMError(
			"USER_NOT_BELONGS_TO_RESELLER",
			"User does not belong to this reseller",
			nil,
			"",
		)
	}

	return nil
}

// GetUserESIMs 获取用户的eSIM列表
func (s *ResellerService) GetUserESIMs(ctx context.Context, userID string) ([]*esim.ESIM, error) {
	// 此处应调用esimService获取用户的eSIM列表
	// 由于esimService尚未实现，返回空列表
	return []*esim.ESIM{}, nil
}

// GetUserOrders 获取用户的订单列表
func (s *ResellerService) GetUserOrders(ctx context.Context, userID string, page, pageSize int) ([]*order.Order, int64, error) {
	// 此处应调用orderService获取用户的订单列表
	// 由于orderService尚未实现，返回空列表
	return []*order.Order{}, 0, nil
}
