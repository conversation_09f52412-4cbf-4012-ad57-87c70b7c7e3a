package service

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"vereal/letsesim/internal/domain/esim"
	"vereal/letsesim/internal/repository"
	pkgesim "vereal/letsesim/pkg/esim"
)

// PackageSyncService 套餐数据同步服务
type PackageSyncService struct {
	packageRepo repository.PackageRepository
	syncLogRepo repository.SyncLogRepository
	esimService *ESIMService
	logger      *zap.Logger
}

// NewPackageSyncService 创建套餐数据同步服务
func NewPackageSyncService(
	packageRepo repository.PackageRepository,
	syncLogRepo repository.SyncLogRepository,
	esimService *ESIMService,
	logger *zap.Logger,
) *PackageSyncService {
	return &PackageSyncService{
		packageRepo: packageRepo,
		syncLogRepo: syncLogRepo,
		esimService: esimService,
		logger:      logger,
	}
}

// SyncPackages 同步套餐数据
func (s *PackageSyncService) SyncPackages(ctx context.Context, providerType string) error {
	// 1. 创建同步日志记录
	syncLog := &repository.SyncLog{
		EntityType:   "package",
		ProviderType: providerType,
		SyncType:     "full",
		StartTime:    time.Now(),
		Status:       "running",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.syncLogRepo.Create(ctx, syncLog); err != nil {
		return fmt.Errorf("failed to create sync log: %w", err)
	}

	defer func() {
		syncLog.EndTime = time.Now()
		syncLog.UpdatedAt = time.Now()
		if err := s.syncLogRepo.Update(ctx, syncLog); err != nil {
			s.logger.Error("Failed to update sync log", zap.Error(err))
		}
	}()

	// 2. 从提供商获取最新套餐列表
	packages, _, err := s.esimService.ListPackages(ctx, providerType, pkgesim.PackageQueryParams{})
	if err != nil {
		syncLog.Status = "failed"
		syncLog.ErrorDetails = err.Error()
		return err
	}

	// 3. 获取本地套餐列表
	localPackages, err := s.packageRepo.ListByProvider(ctx, providerType)
	if err != nil {
		syncLog.Status = "failed"
		syncLog.ErrorDetails = err.Error()
		return err
	}

	// 4. 比较并同步数据
	// 4.1 创建映射，用于快速查找
	localPackageMap := make(map[string]*esim.Package)
	for _, p := range localPackages {
		localPackageMap[p.ExternalID] = p
	}

	// 4.2 处理新增和更新
	for _, pkg := range packages {
		if localPkg, exists := localPackageMap[pkg.ID]; exists {
			// 更新现有套餐
			localPkg.Name = pkg.Name
			localPkg.Description = pkg.Description
			localPkg.Price = float64(pkg.Price)
			localPkg.Currency = pkg.Currency
			localPkg.DataVolume = pkg.DataVolume
			localPkg.ValidityDays = pkg.ValidityDays
			localPkg.LocationCodes = pkg.LocationCodes
			localPkg.SupportsSMS = pkg.SupportsSMS
			localPkg.DataType = pkg.DataType
			localPkg.NetworkTypes = pkg.NetworkTypes
			localPkg.SupportTopUp = pkg.SupportTopUp
			localPkg.LastSyncAt = time.Now()

			if err := s.packageRepo.Update(ctx, localPkg); err != nil {
				syncLog.ErrorCount++
				s.logger.Error("Failed to update package",
					zap.String("id", localPkg.ID),
					zap.String("externalID", localPkg.ExternalID),
					zap.Error(err),
				)
				continue
			}

			syncLog.UpdatedCount++
			delete(localPackageMap, pkg.ID) // 从映射中移除，剩余的将是需要标记为不活跃的
		} else {
			// 创建新套餐
			newPackage := esim.NewPackage(
				providerType,
				pkg.ID,
				pkg.Name,
				float64(pkg.Price),
				pkg.Currency,
				pkg.DataVolume,
				pkg.ValidityDays,
			)
			newPackage.LocationCodes = pkg.LocationCodes
			newPackage.SupportsSMS = pkg.SupportsSMS
			newPackage.DataType = pkg.DataType
			newPackage.NetworkTypes = pkg.NetworkTypes
			newPackage.SupportTopUp = pkg.SupportTopUp
			newPackage.Description = pkg.Description

			if err := s.packageRepo.Create(ctx, newPackage); err != nil {
				syncLog.ErrorCount++
				s.logger.Error("Failed to create package",
					zap.String("externalID", pkg.ID),
					zap.Error(err),
				)
				continue
			}

			syncLog.AddedCount++
		}
	}

	// 4.3 处理已下架套餐（仅标记为不活跃，不物理删除）
	for _, p := range localPackageMap {
		p.Status = esim.PackageStatusDiscontinued
		p.LastSyncAt = time.Now()

		if err := s.packageRepo.Update(ctx, p); err != nil {
			syncLog.ErrorCount++
			s.logger.Error("Failed to mark package as discontinued",
				zap.String("id", p.ID),
				zap.String("externalID", p.ExternalID),
				zap.Error(err),
			)
			continue
		}

		syncLog.DeletedCount++
	}

	syncLog.Status = "completed"
	return nil
}

// RegionSyncService 区域数据同步服务
type RegionSyncService struct {
	regionRepo      repository.RegionRepository
	subLocationRepo repository.SubLocationRepository
	mapRepo         repository.RegionSubLocationMapRepository
	syncLogRepo     repository.SyncLogRepository
	esimService     *ESIMService
	logger          *zap.Logger
}

// NewRegionSyncService 创建区域数据同步服务
func NewRegionSyncService(
	regionRepo repository.RegionRepository,
	subLocationRepo repository.SubLocationRepository,
	mapRepo repository.RegionSubLocationMapRepository,
	syncLogRepo repository.SyncLogRepository,
	esimService *ESIMService,
	logger *zap.Logger,
) *RegionSyncService {
	return &RegionSyncService{
		regionRepo:      regionRepo,
		subLocationRepo: subLocationRepo,
		mapRepo:         mapRepo,
		syncLogRepo:     syncLogRepo,
		esimService:     esimService,
		logger:          logger,
	}
}

// SyncRegions 同步区域数据
func (s *RegionSyncService) SyncRegions(ctx context.Context, providerType string) error {
	// 1. 创建同步日志记录
	syncLog := &repository.SyncLog{
		EntityType:   "region",
		ProviderType: providerType,
		SyncType:     "full",
		StartTime:    time.Now(),
		Status:       "running",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.syncLogRepo.Create(ctx, syncLog); err != nil {
		return fmt.Errorf("failed to create sync log: %w", err)
	}

	defer func() {
		syncLog.EndTime = time.Now()
		syncLog.UpdatedAt = time.Now()
		if err := s.syncLogRepo.Update(ctx, syncLog); err != nil {
			s.logger.Error("Failed to update sync log", zap.Error(err))
		}
	}()

	// 2. 从提供商获取最新区域列表
	regions, err := s.esimService.ListSupportedRegions(ctx, providerType)
	if err != nil {
		syncLog.Status = "failed"
		syncLog.ErrorDetails = err.Error()
		return err
	}

	// 3. 获取本地区域列表
	localRegions, err := s.regionRepo.ListByProvider(ctx, providerType)
	if err != nil {
		syncLog.Status = "failed"
		syncLog.ErrorDetails = err.Error()
		return err
	}

	// 4. 创建映射，用于快速查找
	localRegionMap := make(map[string]*esim.Region)
	for _, r := range localRegions {
		localRegionMap[r.Code] = r
	}

	// 5. 处理区域和子区域
	for _, region := range regions {
		// 5.1 更新或创建区域
		var localRegion *esim.Region
		if existing, exists := localRegionMap[region.Code]; exists {
			// 更新现有区域
			localRegion = existing
			localRegion.Name = region.Name
			localRegion.Type = getRegionTypeFromInt(region.Type)
			localRegion.LastSyncAt = time.Now()

			if err := s.regionRepo.Update(ctx, localRegion); err != nil {
				syncLog.ErrorCount++
				s.logger.Error("Failed to update region",
					zap.String("code", region.Code),
					zap.Error(err),
				)
				continue
			}
			syncLog.UpdatedCount++
		} else {
			// 创建新区域
			localRegion = &esim.Region{
				ProviderType: providerType,
				Code:         region.Code,
				Name:         region.Name,
				Type:         getRegionTypeFromInt(region.Type),
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
				LastSyncAt:   time.Now(),
			}

			if err := s.regionRepo.Create(ctx, localRegion); err != nil {
				syncLog.ErrorCount++
				s.logger.Error("Failed to create region",
					zap.String("code", region.Code),
					zap.Error(err),
				)
				continue
			}
			syncLog.AddedCount++
		}

		// 5.2 处理子区域
		for _, subLoc := range region.SubLocations {
			// 获取或创建子区域
			subLocation, err := s.subLocationRepo.GetByCode(ctx, providerType, subLoc.Code)
			if err != nil {
				if !IsNotFoundError(err) {
					syncLog.ErrorCount++
					s.logger.Error("Failed to check sublocation",
						zap.String("code", subLoc.Code),
						zap.Error(err),
					)
					continue
				}

				// 创建新的子区域
				subLocation = &esim.SubLocation{
					ProviderType: providerType,
					Code:         subLoc.Code,
					Name:         subLoc.Name,
					CreatedAt:    time.Now(),
					UpdatedAt:    time.Now(),
				}

				if err := s.subLocationRepo.Create(ctx, subLocation); err != nil {
					syncLog.ErrorCount++
					s.logger.Error("Failed to create sublocation",
						zap.String("code", subLoc.Code),
						zap.Error(err),
					)
					continue
				}
			} else {
				// 更新子区域
				subLocation.Name = subLoc.Name
				subLocation.UpdatedAt = time.Now()

				if err := s.subLocationRepo.Update(ctx, subLocation); err != nil {
					syncLog.ErrorCount++
					s.logger.Error("Failed to update sublocation",
						zap.String("code", subLoc.Code),
						zap.Error(err),
					)
					continue
				}
			}

			// 创建区域-子区域映射
			err = s.mapRepo.CreateMapping(ctx, localRegion.ID, subLocation.ID, providerType)
			if err != nil {
				syncLog.ErrorCount++
				s.logger.Error("Failed to create mapping",
					zap.String("regionCode", localRegion.Code),
					zap.String("subLocationCode", subLocation.Code),
					zap.Error(err),
				)
			}
		}
	}

	syncLog.Status = "completed"
	return nil
}

// getRegionTypeFromInt 将整数类型的RegionType转换为字符串类型
func getRegionTypeFromInt(typeInt int) esim.RegionType {
	switch typeInt {
	case 1:
		return esim.RegionTypeCountry
	case 2:
		return esim.RegionTypeContinent
	case 3:
		return esim.RegionTypeRegion
	default:
		return esim.RegionTypeCountry
	}
}
