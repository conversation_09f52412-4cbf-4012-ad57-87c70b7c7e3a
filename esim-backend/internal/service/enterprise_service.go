package service

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"

	"vereal/letsesim/internal/domain/enterprise"
	esimDomain "vereal/letsesim/internal/domain/esim"
	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

// EnterpriseESIMRepository 企业eSIM存储库接口，用于适配不同的ESIMRepository实现
type EnterpriseESIMRepository interface {
	// 只包含企业服务需要的方法
	GetByID(ctx context.Context, id string) (*esimDomain.ESIM, error)
	GetUserESIMs(ctx context.Context, userID string, page, pageSize int) ([]*esimDomain.ESIM, int64, error)
}

// EnterpriseService 企业代理商服务
type EnterpriseService struct {
	enterpriseRepo repository.EnterpriseRepository
	userRepo       repository.UserRepository
	esimRepo       EnterpriseESIMRepository
	logger         *zap.Logger
}

// NewEnterpriseService 创建新的企业代理商服务
func NewEnterpriseService(
	enterpriseRepo repository.EnterpriseRepository,
	userRepo repository.UserRepository,
	esimRepo EnterpriseESIMRepository,
	logger *zap.Logger,
) *EnterpriseService {
	return &EnterpriseService{
		enterpriseRepo: enterpriseRepo,
		userRepo:       userRepo,
		esimRepo:       esimRepo,
		logger:         logger,
	}
}

// CreateDepartment 创建部门
func (s *EnterpriseService) CreateDepartment(
	ctx context.Context,
	enterpriseID string,
	name string,
	description string,
) (*enterprise.Department, error) {
	// 创建部门
	department := enterprise.NewDepartment(enterpriseID, name, description)

	// 保存部门
	err := s.enterpriseRepo.CreateDepartment(ctx, department)
	if err != nil {
		return nil, err
	}

	return department, nil
}

// GetDepartmentByID 根据ID获取部门
func (s *EnterpriseService) GetDepartmentByID(
	ctx context.Context,
	id string,
) (*enterprise.Department, error) {
	return s.enterpriseRepo.GetDepartmentByID(ctx, id)
}

// UpdateDepartment 更新部门
func (s *EnterpriseService) UpdateDepartment(
	ctx context.Context,
	id string,
	name string,
	description string,
) (*enterprise.Department, error) {
	// 获取现有部门
	dept, err := s.GetDepartmentByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	dept.Name = name
	dept.Description = description
	dept.UpdatedAt = time.Now()

	// 保存更新
	err = s.enterpriseRepo.UpdateDepartment(ctx, dept)
	if err != nil {
		return nil, err
	}

	return dept, nil
}

// DeleteDepartment 删除部门
func (s *EnterpriseService) DeleteDepartment(
	ctx context.Context,
	id string,
) error {
	return s.enterpriseRepo.DeleteDepartment(ctx, id)
}

// ListDepartments 获取部门列表
func (s *EnterpriseService) ListDepartments(
	ctx context.Context,
	enterpriseID string,
	page int,
	pageSize int,
) ([]*enterprise.Department, int64, error) {
	return s.enterpriseRepo.ListDepartments(ctx, enterpriseID, page, pageSize)
}

// CreateEmployee 创建员工
func (s *EnterpriseService) CreateEmployee(
	ctx context.Context,
	userID string,
	enterpriseID string,
	departmentID string,
	position string,
	employeeCode string,
) (*enterprise.Employee, error) {
	// 创建员工
	employee := enterprise.NewEmployee(userID, enterpriseID, departmentID, position)
	employee.EmployeeCode = employeeCode

	// 保存员工
	err := s.enterpriseRepo.CreateEmployee(ctx, employee)
	if err != nil {
		return nil, err
	}

	return employee, nil
}

// GetEmployeeByID 根据ID获取员工
func (s *EnterpriseService) GetEmployeeByID(
	ctx context.Context,
	id string,
) (*enterprise.Employee, error) {
	return s.enterpriseRepo.GetEmployeeByID(ctx, id)
}

// GetEmployeeByUserID 根据用户ID获取员工
func (s *EnterpriseService) GetEmployeeByUserID(
	ctx context.Context,
	userID string,
) (*enterprise.Employee, error) {
	return s.enterpriseRepo.GetEmployeeByUserID(ctx, userID)
}

// UpdateEmployee 更新员工
func (s *EnterpriseService) UpdateEmployee(
	ctx context.Context,
	id string,
	departmentID string,
	position string,
	employeeCode string,
) (*enterprise.Employee, error) {
	// 获取现有员工
	emp, err := s.GetEmployeeByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if departmentID != "" {
		emp.DepartmentID = departmentID
	}
	if position != "" {
		emp.Position = position
	}
	if employeeCode != "" {
		emp.EmployeeCode = employeeCode
	}
	emp.UpdatedAt = time.Now()

	// 保存更新
	err = s.enterpriseRepo.UpdateEmployee(ctx, emp)
	if err != nil {
		return nil, err
	}

	return emp, nil
}

// DeleteEmployee 删除员工
func (s *EnterpriseService) DeleteEmployee(
	ctx context.Context,
	id string,
) error {
	return s.enterpriseRepo.DeleteEmployee(ctx, id)
}

// ListEmployees 获取员工列表
func (s *EnterpriseService) ListEmployees(
	ctx context.Context,
	filter map[string]interface{},
	page int,
	pageSize int,
) ([]*enterprise.Employee, int64, error) {
	return s.enterpriseRepo.ListEmployees(ctx, filter, page, pageSize)
}

// CreateEmployeeWithUser 创建员工及关联用户
func (s *EnterpriseService) CreateEmployeeWithUser(
	ctx context.Context,
	enterpriseID string,
	email string,
	name string,
	departmentID string,
	position string,
	mobile string,
	password string,
) (*enterprise.Employee, *user.User, error) {
	// 1. 创建用户
	newUser := &user.User{
		Email:          email,
		Name:           name,
		Mobile:         mobile,
		Role:           user.RoleUser,
		Status:         user.UserStatusActive,
		HashedPassword: password, // 注意：实际应用中应该加密
	}

	if err := s.userRepo.Create(ctx, newUser); err != nil {
		return nil, nil, err
	}

	// 2. 创建员工
	employee, err := s.CreateEmployee(ctx, newUser.ID, enterpriseID, departmentID, position, "")
	if err != nil {
		// 如果创建员工失败，应该考虑是否回滚用户创建
		// 这里可以添加回滚逻辑，或者记录错误并继续
		s.logger.Error("创建员工失败但用户已创建", zap.Error(err), zap.String("userID", newUser.ID))
		return nil, newUser, err
	}

	return employee, newUser, nil
}

// AssignESIMs 批量分配eSIM给员工
func (s *EnterpriseService) AssignESIMs(
	ctx context.Context,
	employeeIDs []string,
	esimIDs []string,
) (int, error) {
	// 实现分配eSIM的逻辑
	// 这里需要根据esimRepo的实际接口实现具体逻辑
	successCount := 0

	// 示例实现 - 需要根据实际的esimRepo接口调整
	for _, esimID := range esimIDs {
		for _, employeeID := range employeeIDs {
			// 实际实现时应该调用适当的仓储方法分配eSIM
			// 例如：err := s.esimRepo.AssignToEmployee(ctx, esimID, employeeID)
			// 目前仅记录日志表示尝试分配
			s.logger.Info("尝试分配eSIM",
				zap.String("esimID", esimID),
				zap.String("employeeID", employeeID))

			// 如果方法成功，则增加成功计数
			successCount++
		}
	}

	return successCount, nil
}

// ReclaimESIMs 批量回收eSIM
func (s *EnterpriseService) ReclaimESIMs(
	ctx context.Context,
	esimIDs []string,
) (int, error) {
	// 实现回收eSIM的逻辑
	// 这里需要根据esimRepo的实际接口实现具体逻辑
	successCount := 0

	// 示例实现 - 需要根据实际的esimRepo接口调整
	for _, esimID := range esimIDs {
		// 实际实现时应该调用适当的仓储方法回收eSIM
		// 例如：err := s.esimRepo.ReclaimESIM(ctx, esimID)
		// 目前仅记录日志表示尝试回收
		s.logger.Info("尝试回收eSIM", zap.String("esimID", esimID))

		// 如果方法成功，则增加成功计数
		successCount++
	}

	return successCount, nil
}

// GetEmployeeESIMs 获取员工的eSIM列表
func (s *EnterpriseService) GetEmployeeESIMs(
	ctx context.Context,
	employeeID string,
) ([]*esim.ESIM, error) {
	// 首先获取与员工关联的用户ID
	employee, err := s.enterpriseRepo.GetEmployeeByID(ctx, employeeID)
	if err != nil {
		return nil, err
	}

	// 然后查询该用户的eSIM列表
	domainEsims, _, err := s.esimRepo.GetUserESIMs(ctx, employee.UserID, 1, 100)
	if err != nil {
		return nil, err
	}

	// 将域模型转换为pkg/esim.ESIM类型
	esims := make([]*esim.ESIM, len(domainEsims))
	for i, domainEsim := range domainEsims {
		esims[i] = &esim.ESIM{
			Identifier: esim.ESIMIdentifier{
				ICCID:      domainEsim.ICCID,
				ESIMTranNo: domainEsim.ESIMTranNo,
			},
			Status: esim.ESIMStatus{
				Code: string(domainEsim.Status),
			},
			ActivationCode: domainEsim.ActivationCode,
			QRCodeURL:      domainEsim.QRCodeURL,
			DataVolume:     0, // 这里需要从esim使用情况中获取
			UsedData:       0, // 这里需要从esim使用情况中获取
			ExpiryTime:     domainEsim.ExpiryTime,
		}
	}

	return esims, nil
}

// 企业配额管理

// CreateEmployeeQuota 创建员工配额
func (s *EnterpriseService) CreateEmployeeQuota(
	ctx context.Context,
	employeeID string,
	quotaType enterprise.QuotaType,
	quotaValue int64,
	refreshCycle string,
) (*enterprise.EmployeeQuota, error) {
	// 创建配额
	quota := enterprise.NewEmployeeQuota(employeeID, quotaType, quotaValue, refreshCycle)

	// 保存配额
	err := s.enterpriseRepo.CreateEmployeeQuota(ctx, quota)
	if err != nil {
		return nil, err
	}

	return quota, nil
}

// GetEmployeeQuotaByID 获取员工配额
func (s *EnterpriseService) GetEmployeeQuotaByID(
	ctx context.Context,
	id string,
) (*enterprise.EmployeeQuota, error) {
	return s.enterpriseRepo.GetEmployeeQuotaByID(ctx, id)
}

// GetActiveQuotaByEmployeeID 获取员工活跃配额
func (s *EnterpriseService) GetActiveQuotaByEmployeeID(
	ctx context.Context,
	employeeID string,
	quotaType enterprise.QuotaType,
) (*enterprise.EmployeeQuota, error) {
	return s.enterpriseRepo.GetActiveQuotaByEmployeeID(ctx, employeeID, quotaType)
}

// UpdateEmployeeQuota 更新员工配额
func (s *EnterpriseService) UpdateEmployeeQuota(
	ctx context.Context,
	id string,
	quotaValue int64,
	isActive bool,
) (*enterprise.EmployeeQuota, error) {
	// 获取现有配额
	quota, err := s.GetEmployeeQuotaByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if quotaValue > 0 {
		quota.QuotaValue = quotaValue
	}
	quota.IsActive = isActive
	quota.UpdatedAt = time.Now()

	// 保存更新
	err = s.enterpriseRepo.UpdateEmployeeQuota(ctx, quota)
	if err != nil {
		return nil, err
	}

	return quota, nil
}

// UseEmployeeQuota 使用员工配额
func (s *EnterpriseService) UseEmployeeQuota(
	ctx context.Context,
	employeeID string,
	quotaType enterprise.QuotaType,
	value int64,
) (bool, error) {
	// 获取员工活跃配额
	quota, err := s.GetActiveQuotaByEmployeeID(ctx, employeeID, quotaType)
	if err != nil {
		return false, err
	}

	// 使用配额
	if !quota.UseQuota(value) {
		return false, errors.New("配额不足")
	}

	// 更新配额
	err = s.enterpriseRepo.UpdateEmployeeQuota(ctx, quota)
	if err != nil {
		return false, err
	}

	return true, nil
}

// ListEmployeeQuotas 获取员工配额列表
func (s *EnterpriseService) ListEmployeeQuotas(
	ctx context.Context,
	employeeID string,
	page int,
	pageSize int,
) ([]*enterprise.EmployeeQuota, int64, error) {
	return s.enterpriseRepo.ListEmployeeQuotas(ctx, employeeID, page, pageSize)
}
