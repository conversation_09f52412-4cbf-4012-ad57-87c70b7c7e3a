package service

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"golang.org/x/crypto/bcrypt"

	"gorm.io/gorm"

	"vereal/letsesim/internal/domain/credit"
	"vereal/letsesim/internal/domain/user"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/internal/repository/postgres"
	"vereal/letsesim/pkg/esim"
)

// UserService 用户服务
type UserService struct {
	userRepo   repository.UserRepository
	creditRepo repository.CreditRepository
	db         *gorm.DB
}

// NewUserService 创建新的用户服务
func NewUserService(userRepo repository.UserRepository, creditRepo repository.CreditRepository, db *gorm.DB) *UserService {
	return &UserService{
		userRepo:   userRepo,
		creditRepo: creditRepo,
		db:         db,
	}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(ctx context.Context,
	email, password, name, mobile string,
	role user.Role) (*user.User, error) {
	var newUser *user.User
	var newCredit *credit.Credit
	var resultErr error

	err := s.db.Transaction(func(tx *gorm.DB) error {
		userRepo := postgres.NewUserRepository(tx)
		creditRepo := postgres.NewCreditRepository(tx)

		// 检查邮箱是否已存在
		existingUser, err := userRepo.GetByEmail(ctx, email)
		if err != nil && !IsNotFoundError(err) {
			resultErr = fmt.Errorf("failed to check email existence: %w", err)
			return resultErr
		}
		if existingUser != nil {
			resultErr = esim.NewESIMError(
				esim.ErrInvalidParams, // 使用无效参数错误类型，映射到400状态码
				"User with this email already exists",
				nil,
				"",
			)
			return resultErr
		}

		// 创建新用户
		newUser = user.NewUser(email, name, role, "")
		newUser.Mobile = mobile

		// 对密码进行哈希处理
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			resultErr = fmt.Errorf("failed to hash password: %w", err)
			return resultErr
		}
		newUser.HashedPassword = string(hashedPassword)

		// 保存用户
		if err := userRepo.Create(ctx, newUser); err != nil {
			resultErr = fmt.Errorf("failed to create user: %w", err)
			return resultErr
		}

		// 为新用户创建积分账户
		newCredit = credit.NewCredit(newUser.ID, "USD")
		if err := creditRepo.Create(ctx, newCredit); err != nil {
			// 记录错误但不影响用户创建
			fmt.Printf("Failed to create credit account for user: %v\n", err)
		}
		return nil
	})
	if err != nil {
		return nil, resultErr
	}
	return newUser, nil
}

// Authenticate 用户认证
func (s *UserService) Authenticate(ctx context.Context, email, password string) (*user.User, error) {
	// 获取用户
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		if IsNotFoundError(err) {
			return nil, esim.NewESIMError(
				"INVALID_CREDENTIALS",
				"Invalid email or password",
				nil,
				"",
			)
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, esim.NewESIMError(
			"USER_INACTIVE",
			"User account is inactive",
			nil,
			"",
		)
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.HashedPassword), []byte(password))
	if err != nil {
		return nil, esim.NewESIMError(
			"INVALID_CREDENTIALS",
			"Invalid email or password",
			nil,
			"",
		)
	}

	// 更新最后登录时间
	user.LastLoginAt = time.Now()
	if err := s.userRepo.Update(ctx, user); err != nil {
		// 仅记录错误，不影响认证结果
		fmt.Printf("Failed to update last login time: %v\n", err)
	}

	return user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(ctx context.Context, id string) (*user.User, error) {
	return s.userRepo.GetByID(ctx, id)
}

// GetUserByEmail 根据邮箱获取用户
func (s *UserService) GetUserByEmail(ctx context.Context, email string) (*user.User, error) {
	return s.userRepo.GetByEmail(ctx, email)
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(ctx context.Context, id, name, Mobile string) (*user.User, error) {
	// 获取用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 更新用户信息
	user.Name = name
	user.Mobile = Mobile
	user.UpdatedAt = time.Now()

	// 保存更新
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(ctx context.Context, id, currentPassword, newPassword string) error {
	// 获取用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 验证当前密码
	err = bcrypt.CompareHashAndPassword([]byte(user.HashedPassword), []byte(currentPassword))
	if err != nil {
		return esim.NewESIMError(
			"INVALID_CURRENT_PASSWORD",
			"Current password is incorrect",
			nil,
			"",
		)
	}

	// 生成新密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// 更新密码
	user.HashedPassword = string(hashedPassword)
	user.UpdatedAt = time.Now()

	// 保存更新
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// ListUsers 获取用户列表
func (s *UserService) ListUsers(ctx context.Context, filter map[string]interface{}, page, pageSize int) ([]*user.User, int64, error) {
	return s.userRepo.List(ctx, filter, page, pageSize)
}

// DisableUser 禁用用户
func (s *UserService) DisableUser(ctx context.Context, id string) error {
	// 获取用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 更新状态
	user.Status = "INACTIVE"
	user.UpdatedAt = time.Now()

	// 保存更新
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to disable user: %w", err)
	}

	return nil
}

// EnableUser 启用用户
func (s *UserService) EnableUser(ctx context.Context, id string) error {
	// 获取用户
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 更新状态
	user.Status = "ACTIVE"
	user.UpdatedAt = time.Now()

	// 保存更新
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to enable user: %w", err)
	}

	return nil
}

// GetUserCredit 获取用户积分信息
func (s *UserService) GetUserCredit(ctx context.Context, userID string) (*credit.Credit, error) {
	return s.creditRepo.GetByUserID(ctx, userID)
}

// GetUserTransactionHistory 获取用户交易历史
func (s *UserService) GetUserTransactionHistory(ctx context.Context, userID string, page, pageSize int) ([]*credit.Transaction, int64, error) {
	return s.creditRepo.ListTransactions(ctx, userID, page, pageSize)
}

// AddUserBalance 为用户增加余额
func (s *UserService) AddUserBalance(ctx context.Context, userID string, amount float64, description string, orderID string) (*credit.Transaction, error) {
	// 获取用户积分
	userCredit, err := s.creditRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 更新余额
	userCredit.AddBalance(amount)
	if err := s.creditRepo.Update(ctx, userCredit); err != nil {
		return nil, fmt.Errorf("failed to update balance: %w", err)
	}

	// 创建交易记录
	transaction := credit.NewTransaction(
		userID,
		amount,
		userCredit.Balance,
		credit.TypeDeposit,
		description,
		orderID,
	)

	if err := s.creditRepo.CreateTransaction(ctx, transaction); err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	return transaction, nil
}

// DeductUserBalance 从用户余额中扣除
func (s *UserService) DeductUserBalance(ctx context.Context, userID string, amount float64, description string, orderID string) (*credit.Transaction, error) {
	// 检查用户余额
	userCredit, err := s.creditRepo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 检查余额是否充足
	if userCredit.Balance < amount {
		return nil, esim.NewESIMError(
			"INSUFFICIENT_BALANCE",
			fmt.Sprintf("Insufficient balance: %.2f", userCredit.Balance),
			nil,
			"",
		)
	}

	// 更新余额
	userCredit.DeductBalance(amount)
	if err := s.creditRepo.Update(ctx, userCredit); err != nil {
		return nil, fmt.Errorf("failed to update balance: %w", err)
	}

	// 创建交易记录
	transaction := credit.NewTransaction(
		userID,
		-amount, // 负数表示扣除
		userCredit.Balance,
		credit.TypeOrderPayment,
		description,
		orderID,
	)

	if err := s.creditRepo.CreateTransaction(ctx, transaction); err != nil {
		return nil, fmt.Errorf("failed to create transaction record: %w", err)
	}

	return transaction, nil
}

// RefundUserBalance 退款到用户余额
func (s *UserService) RefundUserBalance(ctx context.Context, userID string, amount float64, description string, orderID string) (*credit.Transaction, error) {
	return s.AddUserBalance(ctx, userID, amount, description, orderID)
}

// IsNotFoundError 检查是否是资源不存在错误
func IsNotFoundError(err error) bool {
	if err == nil {
		return false
	}

	// 可以根据实际的错误类型检查
	// 这里简化处理，假设错误信息中包含"not found"
	return esim.IsNotFound(err)
}

// VerifyResetToken 验证密码重置令牌
func (s *UserService) VerifyResetToken(ctx context.Context, token string) (string, bool, error) {
	// 这里应该实现从存储中获取令牌信息的逻辑
	// 由于我们需要Redis客户端，但UserService中没有，这里简化实现
	// 实际实现应该使用适当的存储机制检查令牌有效性

	// 模拟令牌验证逻辑
	if token == "" {
		return "", false, nil
	}

	// 假设token格式为"reset_{email}"，从中提取邮箱
	// 实际实现应该使用适当的存储和加密机制
	if len(token) > 6 && token[:6] == "reset_" {
		email := token[6:]
		// 检查用户是否存在
		_, err := s.GetUserByEmail(ctx, email)
		if err != nil {
			if IsNotFoundError(err) {
				return "", false, nil
			}
			return "", false, fmt.Errorf("验证令牌失败: %w", err)
		}
		return email, true, nil
	}

	return "", false, nil
}

// CompletePasswordReset 完成密码重置
func (s *UserService) CompletePasswordReset(ctx context.Context, token string, newPassword string) error {
	// 验证令牌
	email, valid, err := s.VerifyResetToken(ctx, token)
	if err != nil {
		return err
	}
	if !valid {
		return echo.NewHTTPError(http.StatusUnauthorized, "重置令牌无效或已过期")
	}

	// 查找用户
	user, err := s.GetUserByEmail(ctx, email)
	if err != nil {
		return err
	}

	// 更新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	user.HashedPassword = string(hashedPassword)
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("更新用户密码失败: %w", err)
	}

	return nil
}

// RequestPasswordReset 请求密码重置
func (s *UserService) RequestPasswordReset(ctx context.Context, email string) error {
	// 查找用户
	user, err := s.GetUserByEmail(ctx, email)
	if err != nil {
		if IsNotFoundError(err) {
			// 为了安全，我们不会透露邮箱是否已注册
			return nil
		}
		return fmt.Errorf("查找用户失败: %w", err)
	}

	// 生成重置令牌
	// 简化实现：实际应用中应使用安全的随机令牌
	_ = fmt.Sprintf("reset_%s", user.Email) // 忽略未使用变量警告

	// 这里应该保存令牌并发送邮件
	// 由于我们没有邮件发送服务和Redis客户端，这里只是模拟实现

	return nil
}

// HasUsers 检查是否存在用户
func (s *UserService) HasUsers(ctx context.Context) (bool, error) {
	count, err := s.userRepo.CountUsers(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to count users: %w", err)
	}
	return count > 0, nil
}

// generateRandomPassword 生成随机密码
func generateRandomPassword(length int) (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	password := make([]byte, length)

	for i := range password {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		password[i] = charset[n.Int64()]
	}

	return string(password), nil
}

// CreateInitialAdmin 创建初始管理员账号
func (s *UserService) CreateInitialAdmin(ctx context.Context, email string) (string, error) {
	// 检查是否已有用户
	hasUsers, err := s.HasUsers(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to check if users exist: %w", err)
	}

	if hasUsers {
		return "", fmt.Errorf("initial admin cannot be created: users already exist in the system")
	}

	// 生成随机密码 (12位长度)
	password, err := generateRandomPassword(12)
	if err != nil {
		return "", fmt.Errorf("failed to generate random password: %w", err)
	}

	// 创建管理员用户
	_, err = s.CreateUser(ctx, email, password, "Administrator", "", user.RoleAdmin)
	if err != nil {
		return "", fmt.Errorf("failed to create initial admin user: %w", err)
	}

	return password, nil
}
