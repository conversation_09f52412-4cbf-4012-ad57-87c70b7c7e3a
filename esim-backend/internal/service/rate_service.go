package service

import (
	"context"
	"fmt"

	"vereal/letsesim/internal/domain/rate"
	"vereal/letsesim/internal/repository"
	"vereal/letsesim/pkg/esim"
)

// RateService 费率服务
type RateService struct {
	rateRepo repository.RateRepository
}

// NewRateService 创建新的费率服务
func NewRateService(rateRepo repository.RateRepository) *RateService {
	return &RateService{
		rateRepo: rateRepo,
	}
}

// 费率请求结构
type RateRequest struct {
	PackageID string `json:"packageId"`
	Country   string `json:"country"`
	Rate      int64  `json:"rate"`
	Currency  string `json:"currency"`
}

// GetGlobalRateByPackage 获取套餐全局费率
func (s *RateService) GetGlobalRateByPackage(ctx context.Context, packageID, country string) (*rate.Rate, error) {
	return s.rateRepo.GetGlobalRateByPackage(ctx, packageID, country)
}

// GetResellerRateByPackage 获取代理商费率
func (s *RateService) GetResellerRateByPackage(ctx context.Context, resellerID, packageID, country string) (*rate.Rate, error) {
	// 尝试获取代理商特定费率
	resellerRate, err := s.rateRepo.GetResellerRateByPackage(ctx, resellerID, packageID, country)
	if err == nil {
		return resellerRate, nil
	}

	// 如果未找到代理商特定费率，回退到全局费率
	if esim.IsNotFound(err) {
		return s.GetGlobalRateByPackage(ctx, packageID, country)
	}

	return nil, err
}

// GetEnterpriseRateByPackage 获取企业代理商费率
func (s *RateService) GetEnterpriseRateByPackage(ctx context.Context, enterpriseID, packageID, country string) (*rate.Rate, error) {
	// 尝试获取企业代理商特定费率
	enterpriseRate, err := s.rateRepo.GetEnterpriseRateByPackage(ctx, enterpriseID, packageID, country)
	if err == nil {
		return enterpriseRate, nil
	}

	// 如果未找到企业代理商特定费率，回退到全局费率
	if esim.IsNotFound(err) {
		return s.GetGlobalRateByPackage(ctx, packageID, country)
	}

	return nil, err
}

// GetRateForPackage 获取套餐适用的费率，根据角色自动选择适当的费率
func (s *RateService) GetRateForPackage(ctx context.Context, ownerType, ownerID, packageID, country string) (*rate.Rate, error) {
	switch ownerType {
	case rate.OwnerTypeReseller:
		return s.GetResellerRateByPackage(ctx, ownerID, packageID, country)
	case rate.OwnerTypeEnterprise:
		return s.GetEnterpriseRateByPackage(ctx, ownerID, packageID, country)
	case rate.OwnerTypeGlobal:
		return s.GetGlobalRateByPackage(ctx, packageID, country)
	default:
		return nil, esim.NewESIMError(
			esim.ErrInvalidParams,
			fmt.Sprintf("Invalid owner type: %s", ownerType),
			nil,
			"",
		)
	}
}

// CreateGlobalRate 创建全局费率
func (s *RateService) CreateGlobalRate(ctx context.Context, request RateRequest) (*rate.Rate, error) {
	// 创建费率领域模型
	newRate := rate.NewGlobalRate(request.PackageID, request.Country, request.Rate, request.Currency)

	// 存储到数据库
	err := s.rateRepo.Create(ctx, newRate)
	if err != nil {
		return nil, err
	}

	return newRate, nil
}

// CreateResellerRate 创建代理商费率
func (s *RateService) CreateResellerRate(ctx context.Context, resellerID string, request RateRequest) (*rate.Rate, error) {
	// 创建费率领域模型
	newRate := rate.NewResellerRate(request.PackageID, request.Country, request.Rate, request.Currency, resellerID)

	// 存储到数据库
	err := s.rateRepo.Create(ctx, newRate)
	if err != nil {
		return nil, err
	}

	return newRate, nil
}

// CreateEnterpriseRate 创建企业代理商费率
func (s *RateService) CreateEnterpriseRate(ctx context.Context, enterpriseID string, request RateRequest) (*rate.Rate, error) {
	// 创建费率领域模型
	newRate := rate.NewEnterpriseRate(request.PackageID, request.Country, request.Rate, request.Currency, enterpriseID)

	// 存储到数据库
	err := s.rateRepo.Create(ctx, newRate)
	if err != nil {
		return nil, err
	}

	return newRate, nil
}

// ListGlobalRates 列出全局费率
func (s *RateService) ListGlobalRates(ctx context.Context, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error) {
	return s.rateRepo.ListGlobalRates(ctx, filters, page, pageSize)
}

// ListResellerRates 列出代理商费率
func (s *RateService) ListResellerRates(ctx context.Context, resellerID string, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error) {
	return s.rateRepo.ListResellerRates(ctx, resellerID, filters, page, pageSize)
}

// ListEnterpriseRates 列出企业代理商费率
func (s *RateService) ListEnterpriseRates(ctx context.Context, enterpriseID string, filters map[string]interface{}, page, pageSize int) ([]*rate.Rate, int64, error) {
	return s.rateRepo.ListEnterpriseRates(ctx, enterpriseID, filters, page, pageSize)
}

// UpdateRate 更新费率
func (s *RateService) UpdateRate(ctx context.Context, id string, request RateRequest) (*rate.Rate, error) {
	// 获取现有费率
	existingRate, err := s.rateRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 更新费率信息
	existingRate.Rate = request.Rate
	if request.Currency != "" {
		existingRate.Currency = request.Currency
	}

	// 存储更新后的费率
	err = s.rateRepo.Update(ctx, existingRate)
	if err != nil {
		return nil, err
	}

	return existingRate, nil
}

// DeleteRate 删除费率
func (s *RateService) DeleteRate(ctx context.Context, id string) error {
	return s.rateRepo.Delete(ctx, id)
}
