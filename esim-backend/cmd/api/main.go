package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"vereal/letsesim/config"
	"vereal/letsesim/internal/di"
	"vereal/letsesim/migrations"

	"go.uber.org/zap"
)

func main() {
	// 解析命令行参数
	configPath := flag.String("config", "./config", "config path")
	migrate := flag.Bool("migrate", false, "run migrations")
	autoMigrate := flag.Bool("auto-migrate", false, "run migrations when start server")
	initAdmin := flag.Bool("init-admin", false, "create initial admin user")
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// 初始化应用
	app, err := di.InitializeApp(cfg)
	if err != nil {
		fmt.Printf("Failed to initialize app: %v\n", err)
		os.Exit(1)
	}

	// 运行数据库迁移
	if *migrate {
		if err := migrations.Migrate(app.DB); err != nil {
			app.Logger.Fatal("Failed to run migrations", zap.Error(err))
		}
		app.Logger.Info("Migrations completed successfully")
		return
	}

	// 创建初始管理员用户
	if *initAdmin {
		args := flag.Args()
		if len(args) == 0 {
			fmt.Printf("Error: email address is required when using --init-admin\n")
			fmt.Printf("Usage: %s --init-admin <email>\n", os.Args[0])
			os.Exit(1)
		}

		adminEmail := args[0]
		ctx := context.Background()
		password, err := app.UserService.CreateInitialAdmin(ctx, adminEmail)
		if err != nil {
			fmt.Printf("Failed to create initial admin: %v\n", err)
			os.Exit(1)
		}

		fmt.Printf("✅ Initial admin user created successfully.\n")
		fmt.Printf("   Email:    %s\n", adminEmail)
		fmt.Printf("   Password: %s\n", password)
		fmt.Printf("Please change this password after your first login.\n")
		return
	}

	// 如果启用了自动迁移，在启动服务前执行迁移
	if *autoMigrate {
		app.Logger.Info("Running automatic database migrations")
		if err := migrations.Migrate(app.DB); err != nil {
			app.Logger.Fatal("Failed to run automatic migrations", zap.Error(err))
		}
		app.Logger.Info("Automatic migrations completed successfully")
	}

	// 启动HTTP服务器
	serverAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	app.Logger.Info("Starting server", zap.String("address", serverAddr))

	// 使用goroutine启动服务器，以便优雅退出
	go func() {
		if err := app.Echo.Start(serverAddr); err != nil {
			app.Logger.Info("Shutting down the server")
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 优雅退出
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Server.ShutdownTimeout)
	defer cancel()

	app.Logger.Info("Shutting down server...")
	if err := app.Echo.Shutdown(ctx); err != nil {
		app.Logger.Error("Server shutdown error", zap.Error(err))
	}

	// 关闭数据库连接
	sqlDB, err := app.DB.DB()
	if err == nil {
		sqlDB.Close()
	}

	// 关闭Redis连接
	app.RedisClient.Close()

	app.Logger.Info("Server exited.")
}
