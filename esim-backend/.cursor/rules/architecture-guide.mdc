---
description: 
globs: 
alwaysApply: false
---
# Clean Architecture Implementation Guide

AuraESIM follows the Clean Architecture pattern with clear separation of concerns:

## Layers
1. **Domain Layer** - Contains business entities and rules
   - [internal/domain](mdc:internal/domain): Core business models and logic
   
2. **Repository Layer** - Data access abstractions
   - [internal/repository](mdc:internal/repository): Database operations and persistence
   
3. **Service Layer** - Application business rules
   - [internal/service](mdc:internal/service): Orchestrates domain objects to fulfill use cases
   
4. **API Layer** - External interfaces
   - [internal/api](mdc:internal/api): HTTP handlers, middleware, and routing

## Dependency Flow
Dependencies flow inward, with inner layers having no knowledge of outer layers:
- Domain layer has no external dependencies
- Repository layer depends on Domain
- Service layer depends on Domain and Repository
- API layer depends on Service

## Configuration and Infrastructure
- [config](mdc:esim-backend/config): Application configuration
- [di](mdc:esim-backend/di): Dependency injection
- [migrations](mdc:esim-backend/migrations): Database schema management

## Integration Points
The application integrates with multiple eSIM providers:
- [pkg/esim](mdc:pkg/esim): Provider integrations with a factory pattern
