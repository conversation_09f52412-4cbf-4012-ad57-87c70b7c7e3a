---
description: 
globs: 
alwaysApply: false
---
# Development Workflow Guide

## Getting Started
1. Clone the repository
2. Set up environment: `docker-compose up -d` (PostgreSQL and Redis)
3. Run migrations: `go run cmd/api/main.go -migrate`
4. Start the server: `go run cmd/api/main.go`

## Common Tasks

### Adding a New Domain Model
1. Create model in [internal/domain](mdc:internal/domain)
2. Define repository interface in [internal/repository](mdc:internal/repository)
3. Implement repository
4. Create service in [internal/service](mdc:internal/service)
5. Add API handlers in [internal/api/handlers](mdc:internal/api/handlers)
6. Update API routes in [internal/api/router.go](mdc:internal/api/router.go)
7. Add database migration in [migrations](mdc:esim-backend/migrations)

### Authentication
JWT-based authentication is implemented in middleware:
- [internal/api/middleware](mdc:internal/api/middleware)

### Database Migrations
- New migrations should be placed in [migrations](mdc:esim-backend/migrations)
- Run with `-migrate` flag: `go run cmd/api/main.go -migrate`

### Configuration
- Configuration is managed in [config](mdc:esim-backend/config)
- Environment-specific configs should follow the pattern `config.{env}.yaml`

### Testing
- Run tests: `go test ./...`
- Integration tests require a running database and Redis instance
