---
description: 
globs: 
alwaysApply: false
---
# Domain Models Guide

AuraESIM implements the following core domain models:

## User Model
- Represents all types of users in the system
- Supports different roles: Admin, User, Reseller, Enterprise
- File: [internal/domain/user/model.go](mdc:internal/domain/user/model.go)

## eSIM Model
- Represents eSIM profiles available to customers
- Includes connectivity details, data packages, and status
- File: [internal/domain/esim/model.go](mdc:internal/domain/esim/model.go)

## Order Model
- Captures purchase transactions for eSIMs
- Tracks payment status, item details, and related user
- File: [internal/domain/order/model.go](mdc:internal/domain/order/model.go)

## Key Domain Relationships
- Users can have multiple eSIMs
- Users create Orders
- Resellers have custom rates
- Enterprise users belong to departments
- eSIMs can be assigned to employees

## Domain Rules
- Users have specific permissions based on their role
- eSIMs go through a lifecycle: Created → Allocated → Activated → Expired
- Orders can be tracked for payment status
- Promotions can be applied to orders
- Credits can be used for purchases
