---
description: 
globs: 
alwaysApply: false
---
# AuraESIM Project Overview

AuraESIM is a platform for managing eSIM services with a clean architecture approach. The platform serves various user roles including individual users, resellers, enterprise customers, and administrators.

## Key Features
- User authentication and authorization
- eSIM purchase, activation, and management
- Order management and payment processing
- Credit system for purchases
- Promotion code functionality
- Reseller management with custom rate configuration
- Enterprise department and employee management
- Admin dashboard for system oversight

## Technology Stack
- Backend: Go with Echo framework
- Database: PostgreSQL
- Caching: Redis
- Authentication: JWT

## Main Components
- API Server: [cmd/api/main.go](mdc:esim-backend/cmd/api/main.go)
- Domain Models: [internal/domain](mdc:internal/domain)
- Services Layer: [internal/service](mdc:internal/service)
- API Handlers: [internal/api/handlers](mdc:internal/api/handlers)
- Repository Layer: [internal/repository](mdc:internal/repository)
