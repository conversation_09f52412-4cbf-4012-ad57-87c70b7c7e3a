---
description: 
globs: 
alwaysApply: false
---
# Code Structure Guide

## Project Organization

```
AuraESIM/
├── cmd/                 # Application entry points
│   └── api/             # API server main
├── config/              # Configuration files and loading
├── di/                  # Dependency injection setup
├── docs/                # Documentation
├── internal/            # Private application code
│   ├── api/             # API layer
│   │   ├── handlers/    # HTTP request handlers
│   │   └── middleware/  # HTTP middleware
│   ├── domain/          # Core business models
│   │   ├── esim/        # eSIM domain models
│   │   ├── order/       # Order domain models
│   │   └── user/        # User domain models
│   ├── repository/      # Data access layer
│   └── service/         # Business logic services
├── migrations/          # Database migrations
└── pkg/                 # Public libraries
    └── esim/            # eSIM provider interfaces
```

## Key Files
- [cmd/api/main.go](mdc:esim-backend/cmd/api/main.go): Application entrypoint
- [internal/api/router.go](mdc:internal/api/router.go): API route definitions
- [internal/domain/user/model.go](mdc:internal/domain/user/model.go): User model example
- [internal/service/user_service.go](mdc:internal/service/user_service.go): User service example

## Naming Conventions
- **Packages**: Lowercase, single word
- **Files**: Snake case (e.g., `user_service.go`)
- **Interfaces**: Method name with 'er' suffix (e.g., `UserRepository`)
- **Structs**: CamelCase, descriptive (e.g., `UserService`)

## Testing Strategy
Tests are organized in the same package as the code being tested with a `_test.go` suffix.
