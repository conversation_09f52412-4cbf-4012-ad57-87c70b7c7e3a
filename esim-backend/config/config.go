package config

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
	"gorm.io/gorm/logger"
)

// Config 应用配置
type Config struct {
	App       AppConfig
	Server    ServerConfig
	Database  DatabaseConfig
	Redis     RedisConfig
	Auth      AuthConfig
	Providers ProvidersConfig
	Log       LogConfig
	Test      *TestConfig
}

// AppConfig 应用基本配置
type AppConfig struct {
	Name        string
	Environment string
	Debug       bool
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host            string
	Port            int
	ShutdownTimeout time.Duration
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver                      string
	Host                        string
	Port                        int
	Username                    string
	Password                    string
	Database                    string
	SSLMode                     string
	MaxOpenConns                int
	MaxIdleConns                int
	ConnMaxLifetime             time.Duration
	ConnMaxIdleTime             time.Duration
	LogLevel                    string
	SlowThreshold               int
	IgnoreRecordNotFoundError   bool
	Colorful                    bool
	TablePrefix                 string
	DisableForeignKeyConstraint bool
	SkipDefaultTransaction      bool
	PrepareStmt                 bool
	AllowGlobalUpdate           bool
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string
	Port         int
	Password     string
	DB           int
	PoolSize     int
	MinIdleConns int
	MaxRetries   int
	DialTimeout  time.Duration
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	PoolTimeout  time.Duration
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWTSecret     string
	JWTExpiration time.Duration
}

// ProvidersConfig eSIM提供商配置
type ProvidersConfig struct {
	ESIMAccess ESIMAccessConfig
	MayaMobile MayaMobileConfig
}

// ESIMAccessConfig eSIMAccess提供商配置
type ESIMAccessConfig struct {
	Enabled    bool
	BaseURL    string
	APIKey     string
	APISecret  string
	WebhookURL string
}

// MayaMobileConfig MayaMobile提供商配置
type MayaMobileConfig struct {
	Enabled    bool
	BaseURL    string
	APIKey     string
	APISecret  string
	WebhookURL string
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string // 日志级别
	Format     string // 日志格式
	Output     string // 日志输出位置
	TimeFormat string // 时间格式
	MaxSize    int    // 单个日志文件最大尺寸，单位MB
	MaxBackups int    // 最大保留的旧日志文件数量
	MaxAge     int    // 日志文件保留天数
	Compress   bool   // 是否压缩旧日志文件
	LocalTime  bool   // 是否使用本地时间命名轮转的日志文件
}

// TestConfig 测试配置
type TestConfig struct {
	// 是否使用模拟服务而非真实API
	UseMock bool
	// 模拟数据目录
	MockDataDir string
	// 当使用真实API时，是否自动记录响应
	RecordResponses bool
	// 记录模式: all(所有响应), new(只记录不存在的响应), none(不记录)
	RecordMode string
	// 各提供商测试配置
	Providers *TestProvidersConfig
}

// TestProvidersConfig 测试提供商配置
type TestProvidersConfig struct {
	ESIMAccess *TestProviderConfig
	MayaMobile *TestProviderConfig
}

// TestProviderConfig 单个测试提供商配置
type TestProviderConfig struct {
	// 模拟模式: always(总是使用模拟), auto(优先使用已有模拟), never(总是使用真实API)
	MockMode string
}

// LoadConfig 加载配置
func LoadConfig(configPaths ...string) (*Config, error) {
	v := viper.New()

	// 设置默认值
	setDefaults(v)

	// 配置Viper
	v.SetEnvPrefix("ESIM")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// 添加配置路径
	for _, path := range configPaths {
		v.AddConfigPath(path)
	}

	// 设置配置文件名
	v.SetConfigName("config")
	v.SetConfigType("yaml")

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		// 配置文件不存在时不报错
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// 解析配置
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置
func setDefaults(v *viper.Viper) {
	// 应用配置
	v.SetDefault("app.name", "eSIM Backend")
	v.SetDefault("app.environment", "development")
	v.SetDefault("app.debug", true)

	// 服务器配置
	v.SetDefault("server.host", "0.0.0.0")
	v.SetDefault("server.port", 18080)
	v.SetDefault("server.shutdownTimeout", 5*time.Second)

	// 数据库配置
	v.SetDefault("database.driver", "postgres")
	v.SetDefault("database.host", "localhost")
	v.SetDefault("database.port", 5432)
	v.SetDefault("database.username", "postgres")
	v.SetDefault("database.password", "11111111")
	v.SetDefault("database.database", "letsesim")
	v.SetDefault("database.sslMode", "disable")
	v.SetDefault("database.maxOpenConns", 20)
	v.SetDefault("database.maxIdleConns", 10)
	v.SetDefault("database.connMaxLifetime", 1*time.Hour)
	v.SetDefault("database.connMaxIdleTime", 30*time.Minute)
	v.SetDefault("database.logLevel", "info")
	v.SetDefault("database.slowThreshold", 200)
	v.SetDefault("database.ignoreRecordNotFoundError", true)
	v.SetDefault("database.colorful", true)
	v.SetDefault("database.tablePrefix", "")
	v.SetDefault("database.disableForeignKeyConstraint", false)
	v.SetDefault("database.skipDefaultTransaction", false)
	v.SetDefault("database.prepareStmt", true)
	v.SetDefault("database.allowGlobalUpdate", false)

	// Redis配置
	v.SetDefault("redis.host", "localhost")
	v.SetDefault("redis.port", 6379)
	v.SetDefault("redis.password", "")
	v.SetDefault("redis.db", 0)
	v.SetDefault("redis.poolSize", 10)
	v.SetDefault("redis.minIdleConns", 2)
	v.SetDefault("redis.maxRetries", 3)
	v.SetDefault("redis.dialTimeout", 5*time.Second)
	v.SetDefault("redis.readTimeout", 3*time.Second)
	v.SetDefault("redis.writeTimeout", 3*time.Second)
	v.SetDefault("redis.poolTimeout", 4*time.Second)

	// 认证配置
	v.SetDefault("auth.jwtSecret", "your-secret-key")
	v.SetDefault("auth.jwtExpiration", 720*time.Hour)

	// 提供商配置
	// eSIMAccess
	v.SetDefault("providers.esimAccess.enabled", true)
	v.SetDefault("providers.esimAccess.baseURL", "https://api.esimaccess.com")
	v.SetDefault("providers.esimAccess.apiKey", "")
	v.SetDefault("providers.esimAccess.apiSecret", "")
	v.SetDefault("providers.esimAccess.webhookURL", "")

	// MayaMobile
	v.SetDefault("providers.mayaMobile.enabled", true)
	v.SetDefault("providers.mayaMobile.baseURL", "https://api.maya.net/connectivity/v1")
	v.SetDefault("providers.mayaMobile.apiKey", "")
	v.SetDefault("providers.mayaMobile.apiSecret", "")
	v.SetDefault("providers.mayaMobile.webhookURL", "")

	// 测试配置
	v.SetDefault("test.useMock", true)
	v.SetDefault("test.mockDataDir", "tests/fixtures/mock_data")
	v.SetDefault("test.recordResponses", false)
	v.SetDefault("test.recordMode", "new")
	v.SetDefault("test.providers.esimAccess.mockMode", "auto")
	v.SetDefault("test.providers.mayaMobile.mockMode", "auto")

	// 日志配置
	v.SetDefault("log.level", "info")
	v.SetDefault("log.format", "json")
	v.SetDefault("log.output", "stdout")
	v.SetDefault("log.timeFormat", "2006-01-02T15:04:05Z07:00")
	v.SetDefault("log.maxSize", 100)    // 默认单个日志文件最大100MB
	v.SetDefault("log.maxBackups", 5)   // 默认保留5个旧文件
	v.SetDefault("log.maxAge", 30)      // 默认保留30天
	v.SetDefault("log.compress", true)  // 默认压缩旧文件
	v.SetDefault("log.localTime", true) // 默认使用本地时间
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.Username, c.Password, c.Database, c.SSLMode,
	)
}

// GetRedisAddr 获取Redis连接地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// LogWriter 返回日志输出接口实现
func (c *DatabaseConfig) LogWriter() logger.Writer {
	return log.New(os.Stdout, "\r\n", log.LstdFlags)
}
