# eSIM平台配置文件示例
# 所有配置均可通过环境变量覆盖，环境变量格式为：ESIM_SECTION_KEY
# 例如：app.name 对应环境变量 ESIM_APP_NAME

# 应用基本配置
app:
  # 应用名称，显示在日志和监控中
  name: "Let's eSIM Backend"
  # 运行环境，可选值：development, staging, production
  environment: "development"
  # 是否启用调试模式，生产环境应设置为false
  debug: true

# 服务器配置
server:
  # 服务器监听地址，0.0.0.0表示监听所有网络接口
  host: "0.0.0.0"
  # 服务器监听端口
  port: 18080
  # 优雅关闭超时时间，超过此时间强制关闭
  shutdownTimeout: "5s"

# 数据库配置
database:
  # 数据库驱动，目前仅支持postgres
  driver: "postgres"
  # 数据库主机地址
  host: "localhost"
  # 数据库端口
  port: 5432
  # 数据库用户名
  username: "postgres"
  # 数据库密码
  password: "1234432156"
  # 数据库名称
  database: "letsesim"
  # SSL模式，可选值：disable, require, verify-ca, verify-full
  sslMode: "disable"
  # 最大打开连接数
  maxOpenConns: 20
  # 最大空闲连接数
  maxIdleConns: 10
  # 连接最大生存时间
  connMaxLifetime: "1h"

# Redis配置
redis:
  # Redis主机地址
  host: "localhost"
  # Redis端口
  port: 6379
  # Redis密码，无密码留空
  password: ""
  # Redis数据库索引
  db: 0
  # 连接池大小
  poolSize: 10

# 认证配置
auth:
  # JWT签名密钥，生产环境必须修改为强密钥
  jwtSecret: "TiUgQLneNl8E1hA3E3b1B4Jx5AiE2Jv4f6n5Oh9u0aB88"
  # JWT令牌有效期
  jwtExpiration: "720h"

# eSIM提供商配置
providers:
  # eSIMAccess提供商配置
  esimAccess:
    # 是否启用此提供商
    enabled: true
    # API基础URL
    baseURL: "https://api.esimaccess.com"
    # API密钥
    apiKey: ""
    # API密钥密文
    apiSecret: ""
    # Webhook回调URL，接收提供商通知
    webhookURL: ""
  
  # MayaMobile提供商配置
  mayaMobile:
    # 是否启用此提供商
    enabled: true
    # API基础URL
    baseURL: "https://api.maya.net/connectivity/v1"
    # API密钥
    apiKey: ""
    # API密钥密文
    apiSecret: ""
    # Webhook回调URL，接收提供商通知
    webhookURL: ""

# 日志配置
log:
  # 日志级别，可选值：debug, info, warn, error, dpanic, panic, fatal
  level: "info"
  # 日志格式，可选值：json, console
  format: "json"
  # 日志输出位置，可选值：stdout, stderr, 或文件路径（如 logs/app.log）
  output: "stdout"
  # 时间格式
  timeFormat: "2006-01-02T15:04:05Z07:00"
  # 当输出为文件时，单个日志文件的最大尺寸，单位MB
  maxSize: 100
  # 当输出为文件时，最大保留的旧日志文件数量
  maxBackups: 5
  # 当输出为文件时，日志文件最大保留天数
  maxAge: 30
  # 当输出为文件时，是否压缩旧日志文件
  compress: true
  # 当输出为文件时，是否使用本地时间命名轮转的日志文件
  localTime: true 