// 数据套餐相关类型定义

export interface Package {
  id: string
  name: string
  description: string
  price: number
  currency: string
  dataVolume: number // 数据量(字节)
  validityDays: number // 有效期(天)
  locationCodes: string[] // 支持的国家/地区代码
  supportsSMS: boolean // 是否支持短信
  dataType: string // 固定流量或无限流量
  networkTypes: string[] // 网络类型(4G/5G等)
  supportTopUp: boolean // 是否支持充值
  provider?: string // 提供商
  status?: PackageStatus // 套餐状态
  lastSyncAt?: string // 最后同步时间
}

export interface Provider {
  id: string
  name: string
  type: string
  status: ProviderStatus
  packageCount?: number
  lastSyncAt?: string
}

export interface PackageQueryParams {
  page?: number
  pageSize?: number
  provider?: string
  locationCode?: string
  type?: string
  packageCode?: string
  region?: string
  country?: string
  minPrice?: number
  maxPrice?: number
  minDataVolume?: number
  maxDataVolume?: number
  minValidityDays?: number
  maxValidityDays?: number
  dataType?: string
  networkType?: string
  supportsSMS?: boolean
  supportTopUp?: boolean
  status?: PackageStatus
}

export interface PackageListResponse {
  packages: Package[]
  pagination: {
    total: number
    page: number
    pageSize: number
  }
}

export interface ProviderListResponse {
  providers: Provider[]
  pagination: {
    total: number
    page: number
    pageSize: number
  }
}

export enum PackageStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

export enum ProviderStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

export enum DataType {
  LIMITED = 'LIMITED',
  UNLIMITED = 'UNLIMITED'
}

export enum NetworkType {
  LTE = 'LTE',
  FOUR_G = '4G',
  FIVE_G = '5G'
}

// 套餐筛选器类型
export interface PackageFilters {
  provider?: string
  region?: string
  dataType?: DataType
  networkType?: NetworkType
  priceRange?: [number, number]
  dataVolumeRange?: [number, number]
  validityRange?: [number, number]
  supportsSMS?: boolean
  supportTopUp?: boolean
}

// 套餐统计信息
export interface PackageStats {
  totalPackages: number
  availablePackages: number
  unavailablePackages: number
  providerCount: number
  averagePrice: number
  lastSyncTime?: string
}

// 同步状态
export interface SyncStatus {
  isSync: boolean
  lastSyncTime?: string
  syncProgress?: number
  syncError?: string
}

// 套餐详情扩展信息
export interface PackageDetails extends Package {
  regions?: Region[]
  providerInfo?: Provider
  syncStatus?: SyncStatus
  usageStats?: {
    totalOrders: number
    activeESIMs: number
    revenue: number
  }
}

// 地区信息
export interface Region {
  code: string
  name: string
  continent: string
  flag?: string
}
