import type { Locale } from '@/i18n/config';

// 语言消息类型（从zh.json推断）
export type Messages = typeof import('../../messages/zh.json');

// 国际化组件Props类型
export interface I18nComponentProps {
  locale?: Locale;
  messages?: Messages;
}

// 语言切换器组件Props
export interface LanguageSwitcherProps {
  currentLocale: Locale;
  onLanguageChange?: (locale: Locale) => void;
  className?: string;
}

// 翻译键类型（支持嵌套键路径）
export type TranslationKey =
  | 'common.loading'
  | 'common.submit'
  | 'common.cancel'
  | 'auth.login'
  | 'auth.logout'
  | 'navigation.dashboard'
  | 'navigation.overview'
  | 'navigation.packages'
  | 'dashboard.welcome'
  | 'dashboard.overview'
  | 'user.users'
  | 'user.userManagement'
  | 'package.packages'
  | 'package.packageManagement'
  | 'package.packageList'
  | 'package.packageDetails'
  | 'package.providers'
  | 'package.providerManagement'
  | 'esim.esims'
  | 'esim.esimManagement'
  | 'order.orders'
  | 'order.orderManagement'
  | 'reseller.resellers'
  | 'reseller.resellerManagement'
  | 'enterprise.enterprises'
  | 'enterprise.enterpriseManagement'
  | 'promotion.promotions'
  | 'promotion.promotionManagement'
  | 'rate.rates'
  | 'rate.rateManagement'
  | 'credit.credits'
  | 'credit.creditManagement'
  | 'report.reports'
  | 'report.reportCenter'
  | 'settings.settings'
  | 'settings.systemSettings'
  | 'profile.profile'
  | 'profile.editProfile'
  | 'table.noData'
  | 'table.noResults'
  | 'form.required'
  | 'form.invalidEmail'
  | 'notification.success'
  | 'notification.error';

// 国际化路由参数
export interface LocaleParams {
  locale: Locale;
}

// 页面Props（包含国际化信息）
export interface PageProps {
  params: LocaleParams;
  searchParams: { [key: string]: string | string[] | undefined };
}

// 布局Props（包含国际化信息）
export interface LayoutProps {
  children: React.ReactNode;
  params: LocaleParams;
} 