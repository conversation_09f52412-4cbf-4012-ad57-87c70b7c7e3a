import { locales, defaultLocale, type Locale } from '@/i18n/config';

/**
 * 检查给定的语言代码是否为支持的语言
 */
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

/**
 * 获取有效的语言代码，如果无效则返回默认语言
 */
export function getValidLocale(locale: string | undefined): Locale {
  if (!locale || !isValidLocale(locale)) {
    return defaultLocale;
  }
  return locale;
}

/**
 * 从浏览器Accept-Language头解析首选语言
 */
export function parseAcceptLanguage(acceptLanguage: string | null): Locale {
  if (!acceptLanguage) {
    return defaultLocale;
  }

  // 解析Accept-Language头，例如: "zh-CN,zh;q=0.9,en;q=0.8"
  const languages = acceptLanguage
    .split(',')
    .map(lang => {
      const [locale, q] = lang.trim().split(';q=');
      return {
        locale: locale.toLowerCase(),
        quality: q ? parseFloat(q) : 1.0
      };
    })
    .sort((a, b) => b.quality - a.quality);

  // 查找匹配的语言
  for (const { locale } of languages) {
    // 完全匹配
    if (isValidLocale(locale)) {
      return locale;
    }
    
    // 语言代码匹配（例如 zh-CN -> zh）
    const langCode = locale.split('-')[0];
    if (isValidLocale(langCode)) {
      return langCode;
    }
  }

  return defaultLocale;
}

/**
 * 生成本地化的URL路径
 */
export function getLocalizedPath(path: string, locale: Locale): string {
  // 移除开头的斜杠
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // 如果路径为空，返回语言根路径
  if (!cleanPath) {
    return `/${locale}`;
  }
  
  // 返回带语言前缀的路径
  return `/${locale}/${cleanPath}`;
}

/**
 * 从URL路径中提取语言代码
 */
export function extractLocaleFromPath(path: string): {
  locale: Locale;
  pathWithoutLocale: string;
} {
  const segments = path.split('/').filter(Boolean);
  
  if (segments.length === 0) {
    return {
      locale: defaultLocale,
      pathWithoutLocale: '/'
    };
  }
  
  const firstSegment = segments[0];
  
  if (isValidLocale(firstSegment)) {
    return {
      locale: firstSegment,
      pathWithoutLocale: '/' + segments.slice(1).join('/')
    };
  }
  
  return {
    locale: defaultLocale,
    pathWithoutLocale: path
  };
}

/**
 * 获取当前语言的文字方向 (LTR/RTL)
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function getTextDirection(_locale: Locale): 'ltr' | 'rtl' {
  // 如果后续支持阿拉伯语等RTL语言，可以在这里添加
  return 'ltr';
}

/**
 * 格式化本地化的日期
 */
export function formatLocalizedDate(
  date: Date | string,
  locale: Locale,
  options: Intl.DateTimeFormatOptions = {}
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const localeMap: Record<Locale, string> = {
    zh: 'zh-CN',
    en: 'en-US',
    ja: 'ja-JP'
  };
  
  return new Intl.DateTimeFormat(localeMap[locale], {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options
  }).format(dateObj);
}

/**
 * 格式化本地化的数字
 */
export function formatLocalizedNumber(
  number: number,
  locale: Locale,
  options: Intl.NumberFormatOptions = {}
): string {
  const localeMap: Record<Locale, string> = {
    zh: 'zh-CN',
    en: 'en-US',
    ja: 'ja-JP'
  };
  
  return new Intl.NumberFormat(localeMap[locale], options).format(number);
}

/**
 * 格式化本地化的货币
 */
export function formatLocalizedCurrency(
  amount: number,
  locale: Locale,
  currency: string = 'USD'
): string {
  return formatLocalizedNumber(amount, locale, {
    style: 'currency',
    currency
  });
} 