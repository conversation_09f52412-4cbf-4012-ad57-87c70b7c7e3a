import { ApiError } from './api'
import { toast } from 'sonner'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN'
}

// 错误处理结果
export interface ErrorHandleResult {
  type: ErrorType
  message: string
  shouldRetry: boolean
  shouldLogout: boolean
  shouldRedirect?: string
}

// 通用错误处理器
export class GlobalErrorHandler {
  private static instance: GlobalErrorHandler
  
  private constructor() {}
  
  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler()
    }
    return GlobalErrorHandler.instance
  }
  
  // 处理API错误
  handleApiError(error: unknown): ErrorHandleResult {
    // 网络错误
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        type: ErrorType.NETWORK,
        message: '网络连接失败，请检查网络设置',
        shouldRetry: true,
        shouldLogout: false
      }
    }
    
    // API错误
    if (error instanceof ApiError) {
      return this.handleHttpError(error.status, error.message)
    }
    
    // 其他错误
    return {
      type: ErrorType.UNKNOWN,
      message: '发生未知错误，请稍后重试',
      shouldRetry: false,
      shouldLogout: false
    }
  }
  
  // 处理HTTP状态码错误
  private handleHttpError(status: number, message: string): ErrorHandleResult {
    switch (status) {
      case 401:
        return {
          type: ErrorType.AUTHENTICATION,
          message: '登录已过期，请重新登录',
          shouldRetry: false,
          shouldLogout: true,
          shouldRedirect: '/login'
        }
      
      case 403:
        return {
          type: ErrorType.AUTHORIZATION,
          message: '权限不足，无法访问此资源',
          shouldRetry: false,
          shouldLogout: false,
          shouldRedirect: '/dashboard'
        }
      
      case 404:
        return {
          type: ErrorType.NOT_FOUND,
          message: '请求的资源不存在',
          shouldRetry: false,
          shouldLogout: false
        }
      
      case 422:
        return {
          type: ErrorType.VALIDATION,
          message: message || '请求参数验证失败',
          shouldRetry: false,
          shouldLogout: false
        }
      
      case 429:
        return {
          type: ErrorType.SERVER,
          message: '请求过于频繁，请稍后重试',
          shouldRetry: true,
          shouldLogout: false
        }
      
      case 500:
      case 502:
      case 503:
      case 504:
        return {
          type: ErrorType.SERVER,
          message: '服务器错误，请稍后重试',
          shouldRetry: true,
          shouldLogout: false
        }
      
      default:
        return {
          type: ErrorType.UNKNOWN,
          message: message || '发生未知错误',
          shouldRetry: false,
          shouldLogout: false
        }
    }
  }
  
  // 显示错误消息
  showError(result: ErrorHandleResult, customMessage?: string) {
    const message = customMessage || result.message
    
    switch (result.type) {
      case ErrorType.AUTHENTICATION:
        toast.error(message, {
          description: '即将跳转到登录页面',
          duration: 3000
        })
        break
      
      case ErrorType.AUTHORIZATION:
        toast.error(message, {
          description: '请联系管理员获取权限',
          duration: 5000
        })
        break
      
      case ErrorType.VALIDATION:
        toast.warning(message, {
          description: '请检查输入信息',
          duration: 4000
        })
        break
      
      case ErrorType.NETWORK:
        toast.error(message, {
          description: '请检查网络连接',
          duration: 5000
        })
        break
      
      default:
        toast.error(message, {
          duration: 4000
        })
    }
  }
}

// 导出单例实例
export const errorHandler = GlobalErrorHandler.getInstance()

// 便捷的错误处理函数
export function handleError(error: unknown, customMessage?: string): ErrorHandleResult {
  const result = errorHandler.handleApiError(error)
  errorHandler.showError(result, customMessage)
  return result
}

// 用于业务层的错误处理装饰器
export function withErrorHandling<T extends (...args: unknown[]) => Promise<unknown>>(
  fn: T,
  customHandler?: (error: unknown) => void
): T {
  return (async (...args: unknown[]) => {
    try {
      return await fn(...args)
    } catch (error) {
      if (customHandler) {
        customHandler(error)
      } else {
        handleError(error)
      }
      throw error
    }
  }) as T
}
