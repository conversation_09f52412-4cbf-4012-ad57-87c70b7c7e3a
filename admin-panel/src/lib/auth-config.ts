import { UserRole, User } from '@/types/auth'

// 页面权限配置类型
export interface PageAuthConfig {
  // 是否需要登录
  requireAuth: boolean
  // 允许的角色列表，空数组表示所有已登录用户都可访问
  allowedRoles?: UserRole[]
  // 无权限时的重定向路径
  redirectTo?: string
  // 是否在加载时显示loading
  showLoading?: boolean
  // 自定义权限检查函数
  customCheck?: (user: User) => boolean
}

// 默认配置
export const DEFAULT_AUTH_CONFIG: PageAuthConfig = {
  requireAuth: true,
  allowedRoles: [],
  redirectTo: '/dashboard',
  showLoading: true
}

// 页面权限配置映射
export const PAGE_AUTH_CONFIG: Record<string, PageAuthConfig> = {
  // 公开页面 - 无需登录
  '/login': {
    requireAuth: false,
    showLoading: false
  },
  
  '/': {
    requireAuth: false,
    showLoading: true
  },
  
  // 需要登录但无角色限制的页面
  '/dashboard': {
    requireAuth: true,
    allowedRoles: [], // 空数组表示所有已登录用户都可访问
    showLoading: true
  },
  
  '/profile': {
    requireAuth: true,
    allowedRoles: [],
    showLoading: true
  },
  
  '/notifications': {
    requireAuth: true,
    allowedRoles: [],
    showLoading: true
  },

  // 业务功能页面 - 需要特定角色
  '/users': {
    requireAuth: true,
    allowedRoles: ['ADMIN', 'RESELLER', 'ENTERPRISE'],
    redirectTo: '/dashboard',
    showLoading: true
  },

  '/esims': {
    requireAuth: true,
    allowedRoles: ['ADMIN', 'RESELLER', 'ENTERPRISE'],
    redirectTo: '/dashboard',
    showLoading: true
  },

  '/orders': {
    requireAuth: true,
    allowedRoles: ['ADMIN', 'RESELLER', 'ENTERPRISE'],
    redirectTo: '/dashboard',
    showLoading: true
  },

  '/promotions': {
    requireAuth: true,
    allowedRoles: ['ADMIN', 'RESELLER'],
    redirectTo: '/dashboard',
    showLoading: true
  },

  '/finance': {
    requireAuth: true,
    allowedRoles: ['ADMIN', 'RESELLER', 'ENTERPRISE'],
    redirectTo: '/dashboard',
    showLoading: true
  },

  // 系统管理页面 - 仅管理员
  '/system/rates': {
    requireAuth: true,
    allowedRoles: ['ADMIN'],
    redirectTo: '/dashboard',
    showLoading: true
  },
  
  '/system/resellers': {
    requireAuth: true,
    allowedRoles: ['ADMIN'],
    redirectTo: '/dashboard',
    showLoading: true
  },
  
  '/system/enterprises': {
    requireAuth: true,
    allowedRoles: ['ADMIN'],
    redirectTo: '/dashboard',
    showLoading: true
  },
  
  '/system/analytics': {
    requireAuth: true,
    allowedRoles: ['ADMIN'],
    redirectTo: '/dashboard',
    showLoading: true
  },
  
  '/system/settings': {
    requireAuth: true,
    allowedRoles: ['ADMIN'],
    redirectTo: '/dashboard',
    showLoading: true
  }
}

// 获取页面权限配置
export function getPageAuthConfig(pathname: string): PageAuthConfig {
  // 移除语言前缀 (如 /en, /zh, /ja)
  const cleanPath = pathname.replace(/^\/[a-z]{2}(?=\/|$)/, '') || '/'
  
  // 查找精确匹配
  if (PAGE_AUTH_CONFIG[cleanPath]) {
    return { ...DEFAULT_AUTH_CONFIG, ...PAGE_AUTH_CONFIG[cleanPath] }
  }
  
  // 查找父路径匹配（用于动态路由）
  const pathSegments = cleanPath.split('/').filter(Boolean)
  for (let i = pathSegments.length; i > 0; i--) {
    const parentPath = '/' + pathSegments.slice(0, i).join('/')
    if (PAGE_AUTH_CONFIG[parentPath]) {
      return { ...DEFAULT_AUTH_CONFIG, ...PAGE_AUTH_CONFIG[parentPath] }
    }
  }
  
  // 返回默认配置（需要登录）
  return DEFAULT_AUTH_CONFIG
}

// 检查用户是否有权限访问页面
export function checkPagePermission(
  config: PageAuthConfig,
  user: User | null,
  isAuthenticated: boolean
): {
  hasPermission: boolean
  shouldRedirect: boolean
  redirectTo?: string
  reason?: string
} {
  // 如果页面不需要登录
  if (!config.requireAuth) {
    return { hasPermission: true, shouldRedirect: false }
  }
  
  // 如果需要登录但用户未登录
  if (!isAuthenticated) {
    return {
      hasPermission: false,
      shouldRedirect: true,
      redirectTo: '/login',
      reason: 'NOT_AUTHENTICATED'
    }
  }
  
  // 如果没有角色限制，已登录用户都可访问
  if (!config.allowedRoles || config.allowedRoles.length === 0) {
    return { hasPermission: true, shouldRedirect: false }
  }
  
  // 检查用户角色
  if (!user || !user.role) {
    return {
      hasPermission: false,
      shouldRedirect: true,
      redirectTo: config.redirectTo || '/dashboard',
      reason: 'NO_ROLE'
    }
  }
  
  if (!config.allowedRoles.includes(user.role)) {
    return {
      hasPermission: false,
      shouldRedirect: true,
      redirectTo: config.redirectTo || '/dashboard',
      reason: 'INSUFFICIENT_ROLE'
    }
  }
  
  // 自定义权限检查
  if (config.customCheck && !config.customCheck(user)) {
    return {
      hasPermission: false,
      shouldRedirect: true,
      redirectTo: config.redirectTo || '/dashboard',
      reason: 'CUSTOM_CHECK_FAILED'
    }
  }
  
  return { hasPermission: true, shouldRedirect: false }
}


