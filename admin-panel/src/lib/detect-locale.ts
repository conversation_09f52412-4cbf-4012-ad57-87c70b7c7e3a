import { locales, type Locale } from '@/i18n/config'

/**
 * 检测浏览器语言并返回支持的语言代码
 * 默认按浏览器语言，没有支持的语言就默认英文
 */
export function detectBrowserLocale(): Locale {
  if (typeof window === 'undefined') {
    return 'en' // 服务端默认英文
  }

  // 获取浏览器语言列表
  const browserLanguages = navigator.languages || [navigator.language]
  
  // 尝试匹配完整语言代码 (如 'zh-CN')
  for (const browserLang of browserLanguages) {
    const langCode = browserLang.toLowerCase()
    
    // 直接匹配
    if (locales.includes(langCode as Locale)) {
      return langCode as Locale
    }
    
    // 匹配语言代码的前缀 (如 'zh-CN' -> 'zh')
    const langPrefix = langCode.split('-')[0]
    if (locales.includes(langPrefix as Locale)) {
      return langPrefix as Locale
    }
  }
  
  // 如果没有匹配的语言，默认返回英文
  return 'en'
}

/**
 * 获取当前应该使用的语言
 * 优先级：URL参数 > localStorage > 浏览器语言 > 默认英文
 */
export function getCurrentLocale(): Locale {
  if (typeof window === 'undefined') {
    return 'en'
  }

  // 1. 检查 localStorage 中的语言设置
  const savedLocale = localStorage.getItem('locale')
  if (savedLocale && locales.includes(savedLocale as Locale)) {
    return savedLocale as Locale
  }

  // 2. 检测浏览器语言
  const browserLocale = detectBrowserLocale()
  
  // 3. 保存到 localStorage
  localStorage.setItem('locale', browserLocale)
  
  return browserLocale
}

/**
 * 保存语言选择到 localStorage
 */
export function saveLocale(locale: Locale) {
  if (typeof window !== 'undefined') {
    localStorage.setItem('locale', locale)
  }
} 