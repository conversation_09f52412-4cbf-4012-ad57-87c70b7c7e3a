'use client'

import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { PageContainer } from '@/components/layout/PageContainer'
import { LanguageSwitcher } from '@/components/layout/language-switcher'
import { formatLocalizedNumber, formatLocalizedDate, formatLocalizedCurrency } from '@/lib/i18n-utils'
import { type Locale } from '@/i18n/config'
import { Users, CreditCard, Activity, TrendingUp } from 'lucide-react'

export function InternationalizedDashboard() {
  const t = useTranslations()
  const locale = useLocale() as Locale

  // 示例数据
  const stats = {
    totalUsers: 12450,
    totalOrders: 3280,
    totalRevenue: 125680.50,
    activeESIMs: 8920
  }

  const currentDate = new Date()

  return (
    <PageContainer
      title={t('dashboard.welcome')}
      description={`${t('dashboard.overview')} - ${formatLocalizedDate(currentDate, locale)}`}
      headerActions={<LanguageSwitcher />}
    >
      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {t('dashboard.totalUsers')}
            </CardTitle>
            <Users className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-3xl font-bold">
              {formatLocalizedNumber(stats.totalUsers, locale)}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              +20.1% {t('dashboard.thisMonth')}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {t('dashboard.totalOrders')}
            </CardTitle>
            <CreditCard className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-3xl font-bold">
              {formatLocalizedNumber(stats.totalOrders, locale)}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              +15.3% {t('dashboard.thisWeek')}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {t('dashboard.totalRevenue')}
            </CardTitle>
            <TrendingUp className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-3xl font-bold">
              {formatLocalizedCurrency(stats.totalRevenue, locale)}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              +25.7% {t('dashboard.thisMonth')}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {t('dashboard.activeESIMs')}
            </CardTitle>
            <Activity className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-3xl font-bold">
              {formatLocalizedNumber(stats.activeESIMs, locale)}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              +12.8% {t('dashboard.thisWeek')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 功能演示卡片 */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="shadow-sm">
          <CardHeader className="pb-6">
            <CardTitle className="text-xl font-semibold">{t('navigation.esims')}</CardTitle>
            <CardDescription className="text-base">
              {t('esim.esimManagement')}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium">{t('esim.activated')}:</span>
                <span className="text-lg font-semibold text-green-600">
                  {formatLocalizedNumber(5680, locale)}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium">{t('esim.pending')}:</span>
                <span className="text-lg font-semibold text-yellow-600">
                  {formatLocalizedNumber(230, locale)}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium">{t('esim.expired')}:</span>
                <span className="text-lg font-semibold text-red-600">
                  {formatLocalizedNumber(145, locale)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-6">
            <CardTitle className="text-xl font-semibold">{t('navigation.orders')}</CardTitle>
            <CardDescription className="text-base">
              {t('order.orderManagement')}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium">{t('order.completed')}:</span>
                <span className="text-lg font-semibold text-green-600">
                  {formatLocalizedNumber(2890, locale)}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium">{t('order.processing')}:</span>
                <span className="text-lg font-semibold text-blue-600">
                  {formatLocalizedNumber(125, locale)}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm font-medium">{t('order.cancelled')}:</span>
                <span className="text-lg font-semibold text-red-600">
                  {formatLocalizedNumber(65, locale)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 多语言特性说明 */}
      <Card className="shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-xl font-semibold">🌍 国际化功能演示</CardTitle>
          <CardDescription className="text-base">
            当前语言: {locale} - 所有文本、数字、日期和货币格式都已本地化
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wider">数字格式</h4>
              <p className="text-lg font-medium">
                {formatLocalizedNumber(1234567.89, locale)}
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wider">日期格式</h4>
              <p className="text-lg font-medium">
                {formatLocalizedDate(currentDate, locale, { 
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wider">货币格式</h4>
              <p className="text-lg font-medium">
                {formatLocalizedCurrency(98765.43, locale)}
              </p>
            </div>
          </div>

          <div className="mt-8 p-6 bg-muted/50 rounded-lg">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-3">📊 {t('dashboard.statistics')}</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex justify-between">
                    <span>{t('dashboard.todayStats')}:</span>
                    <span className="font-semibold">
                      {formatLocalizedNumber(1248, locale)} {t('user.users')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('dashboard.monthlyGrowth')}:</span>
                    <span className="font-semibold text-green-600">+23.5%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </PageContainer>
  )
} 