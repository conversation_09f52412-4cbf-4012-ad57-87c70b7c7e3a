'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Package as PackageIcon,
  Globe,
  Smartphone,
  Wifi,
  MessageSquare,
  Calendar,
  DollarSign,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  RefreshCw
} from 'lucide-react'
import type { Package } from '@/types/package'

interface PackageListProps {
  packages: Package[]
  loading?: boolean
  onRefresh?: () => void
  onEdit?: (pkg: Package) => void
  onDelete?: (pkg: Package) => void
  className?: string
}

export function PackageList({ 
  packages, 
  loading = false, 
  onRefresh, 
  onEdit, 
  onDelete,
  className 
}: PackageListProps) {
  const t = useTranslations('package')
  const tCommon = useTranslations('common')
  const router = useRouter()
  
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid')

  // 格式化数据量
  const formatDataVolume = (bytes: number): string => {
    if (bytes === 0) return t('unlimited')
    const gb = bytes / (1024 * 1024 * 1024)
    if (gb >= 1) {
      return `${gb.toFixed(1)}GB`
    }
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(0)}MB`
  }

  // 格式化价格
  const formatPrice = (price: number, currency: string): string => {
    return `${currency} ${(price / 100).toFixed(2)}`
  }

  // 查看详情
  const handleViewDetails = (pkg: Package) => {
    router.push(`/packages/${pkg.provider || 'default'}/${pkg.id}`)
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            {tCommon('loading')}...
          </div>
        </CardContent>
      </Card>
    )
  }

  if (packages.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center py-8 text-muted-foreground">
            <PackageIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">{tCommon('noData')}</p>
            <p className="text-sm">没有找到符合条件的数据套餐</p>
            {onRefresh && (
              <Button onClick={onRefresh} className="mt-4">
                <RefreshCw className="h-4 w-4 mr-2" />
                {tCommon('refresh')}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t('packageList')}</CardTitle>
            <CardDescription>
              {tCommon('total')} {packages.length} {t('packages')}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              网格
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              表格
            </Button>
            {onRefresh && (
              <Button onClick={onRefresh} size="sm" variant="outline">
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {viewMode === 'grid' ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {packages.map((pkg) => (
              <Card key={pkg.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-base line-clamp-1">{pkg.name}</CardTitle>
                      <CardDescription className="line-clamp-2 mt-1">
                        {pkg.description}
                      </CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(pkg)}>
                          <Eye className="h-4 w-4 mr-2" />
                          {t('viewDetails')}
                        </DropdownMenuItem>
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(pkg)}>
                            <Edit className="h-4 w-4 mr-2" />
                            {t('editPackage')}
                          </DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem 
                            onClick={() => onDelete(pkg)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            {t('deletePackage')}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant={pkg.supportsSMS ? "default" : "secondary"}>
                      {pkg.supportsSMS ? <MessageSquare className="h-3 w-3 mr-1" /> : <Wifi className="h-3 w-3 mr-1" />}
                      {pkg.supportsSMS ? 'SMS' : 'Data'}
                    </Badge>
                    <Badge variant="outline">
                      {pkg.dataType === 'UNLIMITED' ? t('unlimited') : t('limited')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{t('price')}</span>
                    <span className="font-semibold">{formatPrice(pkg.price, pkg.currency)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{t('dataVolume')}</span>
                    <span className="font-medium">{formatDataVolume(pkg.dataVolume)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{t('validityDays')}</span>
                    <span className="font-medium">{pkg.validityDays} {tCommon('days')}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{t('provider')}</span>
                    <span className="text-sm">{pkg.provider}</span>
                  </div>
                  <div className="pt-2">
                    <Button 
                      className="w-full" 
                      size="sm"
                      onClick={() => handleViewDetails(pkg)}
                    >
                      {t('viewDetails')}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('packageName')}</TableHead>
                <TableHead>{t('provider')}</TableHead>
                <TableHead>{t('price')}</TableHead>
                <TableHead>{t('dataVolume')}</TableHead>
                <TableHead>{t('validityDays')}</TableHead>
                <TableHead>{t('networkTypes')}</TableHead>
                <TableHead className="text-right">{tCommon('actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {packages.map((pkg) => (
                <TableRow key={pkg.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{pkg.name}</div>
                      <div className="text-sm text-muted-foreground line-clamp-1">
                        {pkg.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{pkg.provider}</TableCell>
                  <TableCell>{formatPrice(pkg.price, pkg.currency)}</TableCell>
                  <TableCell>{formatDataVolume(pkg.dataVolume)}</TableCell>
                  <TableCell>{pkg.validityDays} {tCommon('days')}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      {pkg.networkTypes.map((type) => (
                        <Badge key={type} variant="outline" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(pkg)}>
                          <Eye className="h-4 w-4 mr-2" />
                          {t('viewDetails')}
                        </DropdownMenuItem>
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(pkg)}>
                            <Edit className="h-4 w-4 mr-2" />
                            {t('editPackage')}
                          </DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem 
                            onClick={() => onDelete(pkg)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            {t('deletePackage')}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
