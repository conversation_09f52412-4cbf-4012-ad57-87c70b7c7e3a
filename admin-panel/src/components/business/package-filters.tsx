'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Slider } from '@/components/ui/slider'
import { 
  Search,
  Filter,
  X
} from 'lucide-react'
import type { PackageFilters } from '@/types/package'

interface PackageFiltersProps {
  filters: PackageFilters
  onFiltersChange: (filters: PackageFilters) => void
  onSearch: (query: string) => void
  onClear: () => void
  className?: string
}

export function PackageFiltersComponent({ 
  filters, 
  onFiltersChange, 
  onSearch, 
  onClear,
  className 
}: PackageFiltersProps) {
  const t = useTranslations('package')
  const tCommon = useTranslations('common')
  
  const [searchQuery, setSearchQuery] = useState('')
  const [priceRange, setPriceRange] = useState<[number, number]>(filters.priceRange || [0, 1000])
  const [dataVolumeRange, setDataVolumeRange] = useState<[number, number]>(filters.dataVolumeRange || [0, 100])

  // 处理搜索
  const handleSearch = () => {
    onSearch(searchQuery)
  }

  // 处理筛选器变化
  const handleFilterChange = (key: keyof PackageFilters, value: any) => {
    const newFilters = { ...filters, [key]: value }
    onFiltersChange(newFilters)
  }

  // 处理价格范围变化
  const handlePriceRangeChange = (value: number[]) => {
    const range: [number, number] = [value[0], value[1]]
    setPriceRange(range)
    handleFilterChange('priceRange', range)
  }

  // 处理数据量范围变化
  const handleDataVolumeRangeChange = (value: number[]) => {
    const range: [number, number] = [value[0], value[1]]
    setDataVolumeRange(range)
    handleFilterChange('dataVolumeRange', range)
  }

  // 清除所有筛选
  const handleClearAll = () => {
    setSearchQuery('')
    setPriceRange([0, 1000])
    setDataVolumeRange([0, 100])
    onClear()
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          {t('searchPackages')}和筛选
        </CardTitle>
        <CardDescription>
          使用多种条件筛选和搜索数据套餐
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 搜索框 */}
        <div className="space-y-2">
          <Label htmlFor="search">{tCommon('search')}</Label>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder={`${t('searchPackages')}...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              {tCommon('search')}
            </Button>
          </div>
        </div>

        {/* 基本筛选 */}
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label>{t('filterByProvider')}</Label>
            <Select
              value={filters.provider || 'all'}
              onValueChange={(value) => handleFilterChange('provider', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('filterByProvider')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{tCommon('all')}</SelectItem>
                <SelectItem value="ESIMAccess">ESIMAccess</SelectItem>
                <SelectItem value="MayaMobile">MayaMobile</SelectItem>
                <SelectItem value="GlobaleSIM">GlobaleSIM</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t('filterByRegion')}</Label>
            <Select
              value={filters.region || 'all'}
              onValueChange={(value) => handleFilterChange('region', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('filterByRegion')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{tCommon('all')}</SelectItem>
                <SelectItem value="EU">欧洲</SelectItem>
                <SelectItem value="AS">亚洲</SelectItem>
                <SelectItem value="NA">北美洲</SelectItem>
                <SelectItem value="GLOBAL">全球</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t('filterByDataType')}</Label>
            <Select
              value={filters.dataType || 'all'}
              onValueChange={(value) => handleFilterChange('dataType', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('filterByDataType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{tCommon('all')}</SelectItem>
                <SelectItem value="LIMITED">{t('limited')}</SelectItem>
                <SelectItem value="UNLIMITED">{t('unlimited')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>{t('filterByNetworkType')}</Label>
            <Select
              value={filters.networkType || 'all'}
              onValueChange={(value) => handleFilterChange('networkType', value === 'all' ? undefined : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('filterByNetworkType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{tCommon('all')}</SelectItem>
                <SelectItem value="4G">{t('4g')}</SelectItem>
                <SelectItem value="5G">{t('5g')}</SelectItem>
                <SelectItem value="LTE">{t('lte')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 价格范围 */}
        <div className="space-y-3">
          <Label>{t('priceRange')}: ¥{priceRange[0]} - ¥{priceRange[1]}</Label>
          <Slider
            value={priceRange}
            onValueChange={handlePriceRangeChange}
            max={1000}
            min={0}
            step={10}
            className="w-full"
          />
        </div>

        {/* 数据量范围 */}
        <div className="space-y-3">
          <Label>{t('dataVolumeRange')}: {dataVolumeRange[0]}GB - {dataVolumeRange[1]}GB</Label>
          <Slider
            value={dataVolumeRange}
            onValueChange={handleDataVolumeRangeChange}
            max={100}
            min={0}
            step={1}
            className="w-full"
          />
        </div>

        {/* 功能选项 */}
        <div className="space-y-3">
          <Label>功能支持</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="sms-support"
                checked={filters.supportsSMS || false}
                onCheckedChange={(checked) => handleFilterChange('supportsSMS', checked || undefined)}
              />
              <Label htmlFor="sms-support" className="text-sm font-normal">
                {t('supportsSMS')}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="topup-support"
                checked={filters.supportTopUp || false}
                onCheckedChange={(checked) => handleFilterChange('supportTopUp', checked || undefined)}
              />
              <Label htmlFor="topup-support" className="text-sm font-normal">
                {t('supportTopUp')}
              </Label>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2 pt-4">
          <Button onClick={handleClearAll} variant="outline" className="flex-1">
            <X className="h-4 w-4 mr-2" />
            {tCommon('clear')}
          </Button>
          <Button onClick={handleSearch} className="flex-1">
            <Search className="h-4 w-4 mr-2" />
            应用筛选
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
