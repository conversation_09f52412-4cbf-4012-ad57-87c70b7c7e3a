'use client'

import { useEffect } from 'react'
import { useTranslations } from 'next-intl'
import useAuthStore from '@/store/auth'
import { Loading } from '@/components/common'

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const t = useTranslations('common')
  
  // 分别调用选择器，避免创建新对象导致的无限循环
  const initializeAuth = useAuthStore(state => state.initializeAuth)
  const isLoading = useAuthStore(state => state.isLoading)
  const _initialized = useAuthStore(state => state._initialized)

  useEffect(() => {
    // 应用启动时初始化认证状态
    initializeAuth()
  }, [initializeAuth])

  // 在认证状态初始化完成之前显示加载状态
  if (!_initialized || isLoading) {
    return (
      <Loading 
        text={t('initializing')} 
        size="lg" 
        fullScreen 
      />
    )
  }

  return <>{children}</>
}

export default AuthProvider