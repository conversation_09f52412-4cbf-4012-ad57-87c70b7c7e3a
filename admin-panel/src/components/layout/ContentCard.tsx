"use client"

import { ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface ContentCardProps {
  children: ReactNode
  title?: string
  description?: string
  className?: string
  headerActions?: ReactNode
  contentClassName?: string
}

export function ContentCard({ 
  children, 
  title, 
  description, 
  className,
  headerActions,
  contentClassName
}: ContentCardProps) {
  return (
    <Card className={cn("shadow-sm", className)}>
      {(title || description || headerActions) && (
        <CardHeader className="pb-6">
          <div className="flex items-start justify-between gap-4">
            <div className="space-y-2">
              {title && (
                <CardTitle className="text-xl font-semibold tracking-tight">
                  {title}
                </CardTitle>
              )}
              {description && (
                <CardDescription className="text-base text-muted-foreground">
                  {description}
                </CardDescription>
              )}
            </div>
            {headerActions && (
              <div className="flex items-center gap-2">
                {headerActions}
              </div>
            )}
          </div>
        </CardHeader>
      )}
      <CardContent className={cn("pt-0", contentClassName)}>
        {children}
      </CardContent>
    </Card>
  )
} 