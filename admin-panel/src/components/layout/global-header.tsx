"use client"

import { ModeToggle } from '@/components/layout/mode-toggle'
import { LanguageSwitcher } from '@/components/layout/language-switcher'

interface GlobalHeaderProps {
  position?: 'fixed' | 'absolute' | 'relative' | 'sticky'
  className?: string
  showBranding?: boolean
}

export function GlobalHeader({ 
  position = 'fixed', 
  className = '',
  showBranding = false
}: GlobalHeaderProps) {
  const positionClasses = {
    fixed: 'fixed top-4 right-4',
    absolute: 'absolute top-4 right-4',
    relative: 'relative',
    sticky: 'sticky top-4'
  }

  return (
    <div className={`${positionClasses[position]} flex items-center gap-2 z-50 ${className}`}>
      {showBranding && (
        <div className="mr-4 text-foreground font-bold">
          System
        </div>
      )}
      <LanguageSwitcher />
      <ModeToggle />
    </div>
  )
}

// 导出不同场景的预设组件
export function FloatingHeader() {
  return <GlobalHeader position="fixed" />
}

export function LoginHeader() {
  return <GlobalHeader position="absolute" />
}

export function DashboardHeader() {
  return <GlobalHeader position="relative" showBranding />
} 