"use client"

import { useTranslations } from 'next-intl'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import {
  LayoutDashboard,
  Users,
  Smartphone,
  ShoppingCart,
  Gift,
  CreditCard,
  Settings,
  Building2,
  UserCheck,
  DollarSign,
  BarChart3,
  Bell,
  User,
  LogOut,
  ChevronUp
} from 'lucide-react'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from '@/components/ui/sidebar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useAuth } from '@/hooks/useAuth'

// 导航菜单配置
const navigationItems = [
  {
    title: 'dashboard',
    icon: LayoutDashboard,
    href: '/dashboard',
    roles: ['ADMIN', 'RESELLER', 'ENTERPRISE']
  },
  {
    title: 'users',
    icon: Users,
    href: '/users',
    roles: ['ADMIN', 'RESELLER', 'ENTERPRISE']
  },
  {
    title: 'esims',
    icon: Smartphone,
    href: '/esims',
    roles: ['ADMIN', 'RESELLER', 'ENTERPRISE']
  },
  {
    title: 'orders',
    icon: ShoppingCart,
    href: '/orders',
    roles: ['ADMIN', 'RESELLER', 'ENTERPRISE']
  },
  {
    title: 'promotions',
    icon: Gift,
    href: '/promotions',
    roles: ['ADMIN', 'RESELLER', 'ENTERPRISE']
  },
  {
    title: 'finance',
    icon: CreditCard,
    href: '/finance',
    roles: ['ADMIN', 'RESELLER', 'ENTERPRISE']
  }
]

const systemItems = [
  {
    title: 'rates',
    icon: DollarSign,
    href: '/system/rates',
    roles: ['ADMIN']
  },
  {
    title: 'resellers',
    icon: UserCheck,
    href: '/system/resellers',
    roles: ['ADMIN']
  },
  {
    title: 'enterprises',
    icon: Building2,
    href: '/system/enterprises',
    roles: ['ADMIN']
  },
  {
    title: 'analytics',
    icon: BarChart3,
    href: '/system/analytics',
    roles: ['ADMIN']
  },
  {
    title: 'settings',
    icon: Settings,
    href: '/system/settings',
    roles: ['ADMIN']
  }
]

export function AppSidebar() {
  const t = useTranslations('navigation')
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const { isMobile } = useSidebar()

  // 根据用户角色过滤菜单项
  const filterMenuByRole = (items: typeof navigationItems) => {
    if (!user?.role) return []
    return items.filter(item => item.roles.includes(user.role))
  }

  const filteredNavigation = filterMenuByRole(navigationItems)
  const filteredSystemItems = filterMenuByRole(systemItems)

  return (
    <Sidebar variant="inset">
      <SidebarHeader className="px-6 py-6">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild className="h-14 px-4">
              <Link href="/dashboard">
                <div className="flex aspect-square size-10 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Smartphone className="size-6" />
                </div>
                <div className="grid flex-1 text-left leading-tight">
                  <span className="truncate text-base font-bold">AuraESIM</span>
                  <span className="truncate text-sm text-sidebar-foreground/70">Admin Panel</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent className="px-3">
        {/* 主要功能 */}
        <SidebarGroup className="py-4">
          <SidebarGroupLabel className="px-3 py-3 text-xs font-semibold uppercase tracking-wider text-sidebar-foreground/60">
            {t('main')}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {filteredNavigation.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={pathname === item.href}
                    className="h-11 px-3 text-sm"
                  >
                    <Link href={item.href}>
                      <item.icon className="size-5" />
                      <span>{t(item.title)}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* 系统管理 - 仅管理员可见 */}
        {filteredSystemItems.length > 0 && (
          <SidebarGroup className="py-4">
            <SidebarGroupLabel className="px-3 py-3 text-xs font-semibold uppercase tracking-wider text-sidebar-foreground/60">
              {t('system')}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {filteredSystemItems.map((item) => (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton 
                      asChild 
                      isActive={pathname === item.href}
                      className="h-11 px-3 text-sm"
                    >
                      <Link href={item.href}>
                        <item.icon className="size-5" />
                        <span>{t(item.title)}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>

      <SidebarFooter className="px-6 py-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="h-14 px-4 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-10 w-10 rounded-xl">
                    <AvatarImage src={user?.avatar} alt={user?.name} />
                    <AvatarFallback className="rounded-xl bg-sidebar-primary text-sidebar-primary-foreground font-semibold">
                      {user?.name?.charAt(0) || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left leading-tight">
                    <span className="truncate text-sm font-semibold">{user?.name}</span>
                    <span className="truncate text-xs text-sidebar-foreground/70">{user?.email}</span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side={isMobile ? "bottom" : "right"}
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem asChild className="h-10">
                  <Link href="/profile">
                    <User className="size-4" />
                    {t('profile')}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="h-10">
                  <Link href="/notifications">
                    <Bell className="size-4" />
                    {t('notifications')}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={logout} className="h-10">
                  <LogOut className="size-4" />
                  {t('logout')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}
