"use client"

import { usePathname } from 'next/navigation'
import { FloatingHeader } from '@/components/layout/global-header'

interface LayoutWrapperProps {
  children: React.ReactNode
}

export function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname()
  
  // 不需要浮动头部的页面（这些页面有自己的头部）
  const noFloatingHeader = [
    '/dashboard',
    '/login'
  ]
  
  // 检查当前路径是否包含不需要浮动头部的页面
  const needsFloatingHeader = !noFloatingHeader.some(path => 
    pathname.includes(path)
  )

  return (
    <>
      {needsFloatingHeader && <FloatingHeader />}
      {children}
    </>
  )
} 