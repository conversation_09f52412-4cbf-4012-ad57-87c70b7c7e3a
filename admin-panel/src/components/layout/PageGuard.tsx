'use client'

import { ReactNode } from 'react'
import { usePageAuth } from '@/hooks/usePageAuth'

interface PageGuardProps {
  children: ReactNode
  fallback?: ReactNode
}

export function PageGuard({ 
  children, 
  fallback = <div className="flex items-center justify-center min-h-screen">Loading...</div> 
}: PageGuardProps) {
  const { hasPermission, isLoading, authConfig } = usePageAuth()
  
  // 显示加载状态
  if (isLoading && authConfig.showLoading) {
    return <>{fallback}</>
  }
  
  // 如果没有权限，返回null（重定向已在hook中处理）
  if (!hasPermission) {
    return null
  }
  
  return <>{children}</>
}

export default PageGuard
