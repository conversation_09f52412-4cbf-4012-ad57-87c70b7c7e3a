"use client"

import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface PageContainerProps {
  children: ReactNode
  title?: string
  description?: string
  className?: string
  headerActions?: ReactNode
}

export function PageContainer({ 
  children, 
  title, 
  description, 
  className,
  headerActions 
}: PageContainerProps) {
  return (
    <div className={cn("space-y-8", className)}>
      {(title || description || headerActions) && (
        <div className="flex items-start justify-between gap-4">
          <div className="space-y-2">
            {title && (
              <h1 className="text-3xl font-bold tracking-tight text-foreground">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-lg text-muted-foreground max-w-2xl">
                {description}
              </p>
            )}
          </div>
          {headerActions && (
            <div className="flex items-center gap-3">
              {headerActions}
            </div>
          )}
        </div>
      )}
      <div className="space-y-6">
        {children}
      </div>
    </div>
  )
} 