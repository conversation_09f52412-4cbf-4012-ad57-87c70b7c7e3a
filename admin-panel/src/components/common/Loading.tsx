import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LoadingProps {
  text?: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
  fullScreen?: boolean
}

export function Loading({ 
  text, 
  size = 'md', 
  className,
  fullScreen = false 
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6', 
    lg: 'h-8 w-8'
  }

  const containerClasses = fullScreen 
    ? 'min-h-screen flex items-center justify-center bg-background'
    : 'flex items-center justify-center'

  return (
    <div className={cn(containerClasses, className)}>
      <div className="text-center">
        <Loader2 className={cn(sizeClasses[size], 'animate-spin mx-auto mb-4')} />
        {text && (
          <p className="text-muted-foreground">
            {text}
          </p>
        )}
      </div>
    </div>
  )
} 