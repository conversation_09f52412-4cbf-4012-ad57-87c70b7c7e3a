import { defineRouting } from 'next-intl/routing';
import { createNavigation } from 'next-intl/navigation';

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['zh', 'en', 'ja'],

  // Used when no locale matches - 默认语言
  defaultLocale: 'en',

  // Enable automatic locale detection based on Accept-Language header
  localeDetection: true,

  // The locale prefix for the pathname
  localePrefix: 'always',

  // Prefix for pathnames that don't match the defaultLocale
  pathnames: {
    '/': '/',
    '/dashboard': {
      zh: '/dashboard',
      en: '/dashboard',
      ja: '/dashboard'
    },
    '/login': {
      zh: '/login',
      en: '/login', 
      ja: '/login'
    },
    '/unauthorized': {
      zh: '/unauthorized',
      en: '/unauthorized',
      ja: '/unauthorized'
    }
  }
});

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { Link, redirect, usePathname, useRouter } =
  createNavigation(routing); 