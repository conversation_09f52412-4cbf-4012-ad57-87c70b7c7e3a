import { api } from '@/lib/api'
import type {
  Package,
  Provider,
  PackageQueryParams,
  PackageListResponse,
  PackageDetails,
  PackageStats,
  SyncStatus
} from '@/types/package'

export class PackageService {
  // 获取提供商列表
  static async getProviders(): Promise<Provider[]> {
    const response = await api.get<Provider[]>('/api/v1/providers')
    return response || []
  }

  // 获取指定提供商的套餐列表
  static async getPackages(
    provider: string,
    params?: PackageQueryParams
  ): Promise<PackageListResponse> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value))
        }
      })
    }

    const response = await api.get<any>(
      `/api/v1/providers/${provider}/packages?${queryParams.toString()}`
    )

    return {
      packages: response.packages || [],
      pagination: response.pagination || { total: 0, page: 1, pageSize: 20 }
    }
  }

  // 获取套餐详情
  static async getPackageDetails(
    provider: string,
    packageId: string
  ): Promise<PackageDetails> {
    const response = await api.get<PackageDetails>(
      `/api/v1/providers/${provider}/packages/${packageId}`
    )
    return response
  }

  // 获取所有提供商的套餐列表（聚合）
  static async getAllPackages(params?: PackageQueryParams): Promise<PackageListResponse> {
    const providers = await this.getProviders()
    const allPackages: Package[] = []
    let totalCount = 0

    // 并行获取所有提供商的套餐
    const packagePromises = providers.map(async (provider) => {
      try {
        const result = await this.getPackages(provider.type, {
          ...params,
          page: 1,
          pageSize: 1000 // 获取大量数据用于聚合
        })
        return result.packages.map(pkg => ({
          ...pkg,
          provider: provider.name
        }))
      } catch (error) {
        console.error(`Failed to fetch packages from provider ${provider.name}:`, error)
        return []
      }
    })

    const packageResults = await Promise.all(packagePromises)
    packageResults.forEach(packages => {
      allPackages.push(...packages)
    })

    // 应用客户端筛选
    let filteredPackages = allPackages

    if (params?.provider) {
      filteredPackages = filteredPackages.filter(pkg => 
        pkg.provider?.toLowerCase().includes(params.provider!.toLowerCase())
      )
    }

    if (params?.locationCode) {
      filteredPackages = filteredPackages.filter(pkg =>
        pkg.locationCodes.some(code => 
          code.toLowerCase().includes(params.locationCode!.toLowerCase())
        )
      )
    }

    if (params?.dataType) {
      filteredPackages = filteredPackages.filter(pkg => pkg.dataType === params.dataType)
    }

    if (params?.minPrice !== undefined) {
      filteredPackages = filteredPackages.filter(pkg => pkg.price >= params.minPrice!)
    }

    if (params?.maxPrice !== undefined) {
      filteredPackages = filteredPackages.filter(pkg => pkg.price <= params.maxPrice!)
    }

    if (params?.minDataVolume !== undefined) {
      filteredPackages = filteredPackages.filter(pkg => pkg.dataVolume >= params.minDataVolume!)
    }

    if (params?.maxDataVolume !== undefined) {
      filteredPackages = filteredPackages.filter(pkg => pkg.dataVolume <= params.maxDataVolume!)
    }

    if (params?.supportsSMS !== undefined) {
      filteredPackages = filteredPackages.filter(pkg => pkg.supportsSMS === params.supportsSMS)
    }

    if (params?.supportTopUp !== undefined) {
      filteredPackages = filteredPackages.filter(pkg => pkg.supportTopUp === params.supportTopUp)
    }

    // 分页处理
    const page = params?.page || 1
    const pageSize = params?.pageSize || 20
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize

    totalCount = filteredPackages.length
    const paginatedPackages = filteredPackages.slice(startIndex, endIndex)

    return {
      packages: paginatedPackages,
      pagination: {
        total: totalCount,
        page,
        pageSize
      }
    }
  }

  // 获取套餐统计信息
  static async getPackageStats(): Promise<PackageStats> {
    try {
      const providers = await this.getProviders()
      const allPackages = await this.getAllPackages({ pageSize: 10000 })
      
      const availablePackages = allPackages.packages.filter(pkg => 
        pkg.status === 'ACTIVE' || !pkg.status
      ).length
      
      const totalPrice = allPackages.packages.reduce((sum, pkg) => sum + pkg.price, 0)
      const averagePrice = allPackages.packages.length > 0 ? totalPrice / allPackages.packages.length : 0

      return {
        totalPackages: allPackages.pagination.total,
        availablePackages,
        unavailablePackages: allPackages.pagination.total - availablePackages,
        providerCount: providers.length,
        averagePrice: Math.round(averagePrice * 100) / 100
      }
    } catch (error) {
      console.error('Failed to get package stats:', error)
      return {
        totalPackages: 0,
        availablePackages: 0,
        unavailablePackages: 0,
        providerCount: 0,
        averagePrice: 0
      }
    }
  }

  // 同步套餐数据
  static async syncPackages(provider?: string): Promise<SyncStatus> {
    try {
      const endpoint = provider
        ? `/api/v1/admin/providers/${provider}/sync`
        : '/api/v1/admin/packages/sync'

      await api.post(endpoint)

      return {
        isSync: true,
        lastSyncTime: new Date().toISOString(),
        syncProgress: 100
      }
    } catch (error) {
      console.error('Failed to sync packages:', error)
      return {
        isSync: false,
        syncError: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // 搜索套餐
  static async searchPackages(
    query: string,
    params?: Omit<PackageQueryParams, 'page' | 'pageSize'>
  ): Promise<PackageListResponse> {
    const searchParams: PackageQueryParams = {
      ...params,
      page: 1,
      pageSize: 50
    }

    // 如果查询字符串看起来像地区代码，添加到locationCode参数
    if (query.length <= 3 && /^[A-Z]+$/i.test(query)) {
      searchParams.locationCode = query.toUpperCase()
    }

    const allPackages = await this.getAllPackages(searchParams)
    
    // 在名称和描述中搜索
    const filteredPackages = allPackages.packages.filter(pkg =>
      pkg.name.toLowerCase().includes(query.toLowerCase()) ||
      pkg.description.toLowerCase().includes(query.toLowerCase()) ||
      pkg.locationCodes.some(code => 
        code.toLowerCase().includes(query.toLowerCase())
      ) ||
      (pkg.provider && pkg.provider.toLowerCase().includes(query.toLowerCase()))
    )

    return {
      packages: filteredPackages,
      pagination: {
        total: filteredPackages.length,
        page: 1,
        pageSize: filteredPackages.length
      }
    }
  }
}
