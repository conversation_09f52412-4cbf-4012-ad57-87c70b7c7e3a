import { useEffect, useMemo } from 'react'
import { usePathname } from 'next/navigation'
import { useRouter } from '@/i18n/routing'
import { useAuth } from './useAuth'
import { getPageAuthConfig, checkPagePermission } from '@/lib/auth-config'

export function usePageAuth() {
  const pathname = usePathname()
  const router = useRouter()
  const { user, isAuthenticated, isLoading, _initialized } = useAuth()
  
  // 获取当前页面的权限配置
  const authConfig = useMemo(() => {
    return getPageAuthConfig(pathname)
  }, [pathname])
  
  // 检查权限
  const permissionResult = useMemo(() => {
    if (!_initialized) {
      return {
        hasPermission: false,
        shouldRedirect: false,
        isLoading: true
      }
    }
    
    const result = checkPagePermission(authConfig, user, isAuthenticated)
    return {
      ...result,
      isLoading: false
    }
  }, [authConfig, user, isAuthenticated, _initialized])
  
  // 处理重定向
  useEffect(() => {
    if (permissionResult.shouldRedirect && permissionResult.redirectTo && !isLoading) {
      router.push(permissionResult.redirectTo as '/login' | '/dashboard')
    }
  }, [permissionResult.shouldRedirect, permissionResult.redirectTo, isLoading, router])
  
  return {
    ...permissionResult,
    authConfig,
    isLoading: isLoading || permissionResult.isLoading
  }
}
