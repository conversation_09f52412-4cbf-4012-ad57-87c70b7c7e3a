'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import PageGuard from '@/components/layout/PageGuard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  Package as PackageIcon,
  Globe,
  Smartphone,
  Wifi,
  MessageSquare,
  Calendar,
  DollarSign,
  Signal,
  RefreshCw
} from 'lucide-react'
import { PackageService } from '@/services/packageService'
import type { PackageDetails } from '@/types/package'

export default function PackageDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const t = useTranslations('package')
  const tCommon = useTranslations('common')
  
  const [packageDetails, setPackageDetails] = useState<PackageDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const provider = params.provider as string
  const packageId = params.id as string

  // 加载套餐详情
  const loadPackageDetails = async () => {
    try {
      setLoading(true)
      setError(null)
      const details = await PackageService.getPackageDetails(provider, packageId)
      setPackageDetails(details)
    } catch (err) {
      console.error('Failed to load package details:', err)
      setError(err instanceof Error ? err.message : 'Failed to load package details')
    } finally {
      setLoading(false)
    }
  }

  // 格式化数据量
  const formatDataVolume = (bytes: number): string => {
    if (bytes === 0) return t('unlimited')
    const gb = bytes / (1024 * 1024 * 1024)
    if (gb >= 1) {
      return `${gb.toFixed(1)}GB`
    }
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(0)}MB`
  }

  // 格式化价格
  const formatPrice = (price: number, currency: string): string => {
    return `${currency} ${(price / 100).toFixed(2)}`
  }

  useEffect(() => {
    if (provider && packageId) {
      loadPackageDetails()
    }
  }, [provider, packageId])

  if (loading) {
    return (
      <PageGuard>
        <div className="flex items-center justify-center min-h-[400px]">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          {tCommon('loading')}...
        </div>
      </PageGuard>
    )
  }

  if (error) {
    return (
      <PageGuard>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {tCommon('back')}
            </Button>
          </div>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-red-600">
                <p>{error}</p>
                <Button onClick={loadPackageDetails} className="mt-4">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {tCommon('refresh')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageGuard>
    )
  }

  if (!packageDetails) {
    return (
      <PageGuard>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {tCommon('back')}
            </Button>
          </div>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-muted-foreground">
                <p>{t('packageDetails')} {tCommon('noData')}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageGuard>
    )
  }

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 页面标题和导航 */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {tCommon('back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('packageDetails')}</h1>
            <p className="text-muted-foreground">
              {packageDetails.name}
            </p>
          </div>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* 主要信息 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PackageIcon className="h-5 w-5" />
                  {t('packageDetails')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold">{packageDetails.name}</h3>
                  <p className="text-muted-foreground mt-1">{packageDetails.description}</p>
                </div>
                
                <Separator />
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('packageId')}</span>
                    <span className="text-sm text-muted-foreground">{packageDetails.id}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('provider')}</span>
                    <span className="text-sm text-muted-foreground">{packageDetails.provider || provider}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('dataType')}</span>
                    <Badge variant="outline">
                      {packageDetails.dataType === 'UNLIMITED' ? t('unlimited') : t('limited')}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('status')}</span>
                    <Badge variant={packageDetails.status === 'ACTIVE' ? 'default' : 'secondary'}>
                      {packageDetails.status === 'ACTIVE' ? t('active') : t('inactive')}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 覆盖地区 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  {t('locationCodes')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {packageDetails.locationCodes.map((code) => (
                    <Badge key={code} variant="outline">
                      {code}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 网络类型 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Signal className="h-5 w-5" />
                  {t('networkTypes')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {packageDetails.networkTypes.map((type) => (
                    <Badge key={type} variant="outline">
                      {type}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏信息 */}
          <div className="space-y-6">
            {/* 价格和规格 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  {t('price')}和规格
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">
                    {formatPrice(packageDetails.price, packageDetails.currency)}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{t('price')}</p>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center gap-2">
                      <Wifi className="h-4 w-4" />
                      {t('dataVolume')}
                    </span>
                    <span className="text-sm font-semibold">
                      {formatDataVolume(packageDetails.dataVolume)}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {t('validityDays')}
                    </span>
                    <span className="text-sm font-semibold">
                      {packageDetails.validityDays} {tCommon('days')}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      {t('supportsSMS')}
                    </span>
                    <Badge variant={packageDetails.supportsSMS ? "default" : "secondary"}>
                      {packageDetails.supportsSMS ? tCommon('enabled') : tCommon('disabled')}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium flex items-center gap-2">
                      <Smartphone className="h-4 w-4" />
                      {t('supportTopUp')}
                    </span>
                    <Badge variant={packageDetails.supportTopUp ? "default" : "secondary"}>
                      {packageDetails.supportTopUp ? tCommon('enabled') : tCommon('disabled')}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <Card>
              <CardHeader>
                <CardTitle>{tCommon('actions')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full">
                  创建eSIM订单
                </Button>
                <Button variant="outline" className="w-full">
                  {t('editPackage')}
                </Button>
                <Button variant="outline" className="w-full" onClick={loadPackageDetails}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {tCommon('refresh')}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageGuard>
  )
}
