'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import PageGuard from '@/components/layout/PageGuard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Search,
  Filter,
  RefreshCw,
  Package as PackageIcon,
  Globe,
  Smartphone,
  Wifi,
  MessageSquare,
  Plus
} from 'lucide-react'
import { PackageService } from '@/services/packageService'
import type { Package, Provider, PackageQueryParams, PackageStats } from '@/types/package'

export default function PackagesPage() {
  const t = useTranslations('package')
  const tCommon = useTranslations('common')
  const router = useRouter()
  
  const [packages, setPackages] = useState<Package[]>([])
  const [providers, setProviders] = useState<Provider[]>([])
  const [stats, setStats] = useState<PackageStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProvider, setSelectedProvider] = useState<string>('all')
  const [selectedDataType, setSelectedDataType] = useState<string>('all')
  const [selectedNetworkType, setSelectedNetworkType] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [activeTab, setActiveTab] = useState('all')

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true)

      // 模拟数据，避免API调用错误
      setProviders([
        { id: '1', name: 'ESIMAccess', type: 'esimaccess', status: 'ACTIVE' as any, packageCount: 150 },
        { id: '2', name: 'MayaMobile', type: 'mayamobile', status: 'ACTIVE' as any, packageCount: 200 },
        { id: '3', name: 'GlobaleSIM', type: 'globalesim', status: 'INACTIVE' as any, packageCount: 80 }
      ])

      setStats({
        totalPackages: 430,
        availablePackages: 350,
        unavailablePackages: 80,
        providerCount: 3,
        averagePrice: 25.50
      })

      // 加载套餐数据
      await loadPackages()
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  // 加载套餐列表
  const loadPackages = async () => {
    try {
      // 模拟套餐数据
      const mockPackages: Package[] = [
        {
          id: 'pkg_001',
          name: '欧洲30天5GB套餐',
          description: '适用于欧洲地区的30天有效期5GB流量套餐',
          price: 9900,
          currency: 'CNY',
          dataVolume: 5 * 1024 * 1024 * 1024,
          validityDays: 30,
          locationCodes: ['EU', 'DE', 'FR', 'IT', 'ES'],
          supportsSMS: true,
          dataType: 'LIMITED',
          networkTypes: ['4G', '5G'],
          supportTopUp: true,
          provider: 'ESIMAccess'
        },
        {
          id: 'pkg_002',
          name: '亚洲15天3GB套餐',
          description: '覆盖亚洲主要国家的15天3GB流量套餐',
          price: 6800,
          currency: 'CNY',
          dataVolume: 3 * 1024 * 1024 * 1024,
          validityDays: 15,
          locationCodes: ['AS', 'JP', 'KR', 'SG', 'TH'],
          supportsSMS: false,
          dataType: 'LIMITED',
          networkTypes: ['4G'],
          supportTopUp: false,
          provider: 'MayaMobile'
        },
        {
          id: 'pkg_003',
          name: '全球无限流量套餐',
          description: '全球通用的无限流量套餐，7天有效期',
          price: 15800,
          currency: 'CNY',
          dataVolume: 0,
          validityDays: 7,
          locationCodes: ['GLOBAL'],
          supportsSMS: true,
          dataType: 'UNLIMITED',
          networkTypes: ['4G', '5G', 'LTE'],
          supportTopUp: true,
          provider: 'GlobaleSIM'
        }
      ]

      setPackages(mockPackages)
      setTotalPages(1)
    } catch (error) {
      console.error('Failed to load packages:', error)
      setPackages([])
    }
  }

  // 处理搜索
  const handleSearch = async () => {
    setCurrentPage(1)
    await loadPackages()
  }

  // 处理筛选变化
  const handleFilterChange = async () => {
    setCurrentPage(1)
    await loadPackages()
  }

  // 清除筛选
  const clearFilters = () => {
    setSearchQuery('')
    setSelectedProvider('all')
    setSelectedDataType('all')
    setSelectedNetworkType('all')
    setCurrentPage(1)
  }

  // 同步套餐数据
  const handleSync = async () => {
    try {
      setLoading(true)
      // 模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 1000))
      await loadData()
    } catch (error) {
      console.error('Failed to sync packages:', error)
    } finally {
      setLoading(false)
    }
  }

  // 格式化数据量
  const formatDataVolume = (bytes: number): string => {
    if (bytes === 0) return t('unlimited')
    const gb = bytes / (1024 * 1024 * 1024)
    if (gb >= 1) {
      return `${gb.toFixed(1)}GB`
    }
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(0)}MB`
  }

  // 格式化价格
  const formatPrice = (price: number, currency: string): string => {
    return `${currency} ${(price / 100).toFixed(2)}`
  }

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadPackages()
  }, [currentPage, activeTab])

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('packageManagement')}</h1>
            <p className="text-muted-foreground">
              {t('packageList')}和{t('providerManagement')}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={handleSync} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {t('syncPackages')}
            </Button>
          </div>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('totalPackages')}</CardTitle>
                <PackageIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalPackages}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('availablePackages')}</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.availablePackages}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('providers')}</CardTitle>
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.providerCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{tCommon('averagePrice')}</CardTitle>
                <Plus className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥{stats.averagePrice}</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 搜索和筛选 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('searchPackages')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={`${t('searchPackages')}...`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button onClick={handleSearch} disabled={loading}>
                <Search className="h-4 w-4 mr-2" />
                {tCommon('search')}
              </Button>
            </div>
            
            <div className="grid gap-4 md:grid-cols-4">
              <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filterByProvider')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{tCommon('all')}</SelectItem>
                  {providers.map((provider) => (
                    <SelectItem key={provider.id} value={provider.name}>
                      {provider.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedDataType} onValueChange={setSelectedDataType}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filterByDataType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{tCommon('all')}</SelectItem>
                  <SelectItem value="LIMITED">{t('limited')}</SelectItem>
                  <SelectItem value="UNLIMITED">{t('unlimited')}</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedNetworkType} onValueChange={setSelectedNetworkType}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filterByNetworkType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{tCommon('all')}</SelectItem>
                  <SelectItem value="4G">{t('4g')}</SelectItem>
                  <SelectItem value="5G">{t('5g')}</SelectItem>
                  <SelectItem value="LTE">{t('lte')}</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" onClick={clearFilters}>
                <Filter className="h-4 w-4 mr-2" />
                {tCommon('clear')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 套餐列表 */}
        <Card>
          <CardHeader>
            <CardTitle>{t('packageList')}</CardTitle>
            <CardDescription>
              {tCommon('total')} {packages.length} {t('packages')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="all">{tCommon('all')}</TabsTrigger>
                {providers.slice(0, 4).map((provider) => (
                  <TabsTrigger key={provider.id} value={provider.type}>
                    {provider.name}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              <TabsContent value={activeTab} className="mt-6">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                    {tCommon('loading')}...
                  </div>
                ) : packages.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {tCommon('noData')}
                  </div>
                ) : (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {packages.map((pkg) => (
                      <Card key={pkg.id} className="hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <CardTitle className="text-base line-clamp-1">{pkg.name}</CardTitle>
                              <CardDescription className="line-clamp-2 mt-1">
                                {pkg.description}
                              </CardDescription>
                            </div>
                            <Badge variant={pkg.supportsSMS ? "default" : "secondary"}>
                              {pkg.supportsSMS ? <MessageSquare className="h-3 w-3 mr-1" /> : <Wifi className="h-3 w-3 mr-1" />}
                              {pkg.supportsSMS ? 'SMS' : 'Data'}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">{t('price')}</span>
                            <span className="font-semibold">{formatPrice(pkg.price, pkg.currency)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">{t('dataVolume')}</span>
                            <span className="font-medium">{formatDataVolume(pkg.dataVolume)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">{t('validityDays')}</span>
                            <span className="font-medium">{pkg.validityDays} {tCommon('days')}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">{t('networkTypes')}</span>
                            <div className="flex gap-1">
                              {pkg.networkTypes.map((type) => (
                                <Badge key={type} variant="outline" className="text-xs">
                                  {type}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="pt-2">
                            <Button
                              className="w-full"
                              size="sm"
                              onClick={() => router.push(`/packages/${pkg.provider || 'default'}/${pkg.id}`)}
                            >
                              {t('viewDetails')}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </PageGuard>
  )
}
