import { ReactNode } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { AuthProvider } from '@/components/layout/AuthProvider'

interface OrdersLayoutProps {
  children: ReactNode
}

export default function OrdersLayout({ children }: OrdersLayoutProps) {
  return (
    <AuthProvider>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </AuthProvider>
  )
}
