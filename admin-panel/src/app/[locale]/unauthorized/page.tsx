'use client'

import { useRouter } from 'next/navigation'
import { Alert<PERSON>riangle, ArrowLeft } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function UnauthorizedPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900">
              <AlertTriangle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <CardTitle className="mt-4">访问被拒绝</CardTitle>
            <CardDescription>
              抱歉，您没有权限访问此页面。请联系管理员获取相应权限。
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button 
              onClick={() => router.back()}
              variant="outline"
              className="mr-2"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回上一页
            </Button>
            <Button 
              onClick={() => router.push('/dashboard')}
            >
              回到首页
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 