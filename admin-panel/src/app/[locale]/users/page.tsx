'use client'

import PageGuard from '@/components/layout/PageGuard'
import { useTranslations } from 'next-intl'

export default function UsersPage() {
  const t = useTranslations('user')

  return (
    <PageGuard>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('userManagement')}</h1>
          <p className="text-muted-foreground">
            管理系统中的所有用户账户
          </p>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            用户管理功能正在开发中...
          </p>
        </div>
      </div>
    </PageGuard>
  )
}
