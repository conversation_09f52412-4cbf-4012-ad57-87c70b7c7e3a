import { ReactNode } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { AuthProvider } from '@/components/layout/AuthProvider'

interface SystemLayoutProps {
  children: ReactNode
}

export default function SystemLayout({ children }: SystemLayoutProps) {
  return (
    <AuthProvider>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </AuthProvider>
  )
}
