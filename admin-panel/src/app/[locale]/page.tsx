'use client'

import { useEffect } from 'react'
import { useTranslations } from 'next-intl'
import useAuthStore from '@/store/auth'
import { useRouter } from '@/i18n/routing'
import { Loading } from '@/components/common'

export default function HomePage() {
  const t = useTranslations('common')
  
  // 分别调用选择器，避免创建新对象导致的无限循环
  const isAuthenticated = useAuthStore(state => state.isAuthenticated)
  const isLoading = useAuthStore(state => state.isLoading)
  const _initialized = useAuthStore(state => state._initialized)
  const router = useRouter()

  useEffect(() => {
    // 只有在认证状态已初始化且不在加载中时才进行重定向
    if (_initialized && !isLoading) {
      if (isAuthenticated) {
        router.push('/dashboard')
      } else {
        router.push('/login')
      }
    }
  }, [_initialized, isAuthenticated, isLoading, router])

  return (
    <Loading 
      text={t('loading')} 
      size="lg" 
      fullScreen 
    />
  )
}
