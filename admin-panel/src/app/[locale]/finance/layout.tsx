import { ReactNode } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { AuthProvider } from '@/components/layout/AuthProvider'

interface FinanceLayoutProps {
  children: ReactNode
}

export default function FinanceLayout({ children }: FinanceLayoutProps) {
  return (
    <AuthProvider>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </AuthProvider>
  )
}
