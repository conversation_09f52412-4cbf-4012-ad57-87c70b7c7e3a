'use client'

import PageGuard from '@/components/layout/PageGuard'
import { useTranslations } from 'next-intl'

export default function ESIMsPage() {
  const t = useTranslations('esim')

  return (
    <PageGuard>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('esimManagement')}</h1>
          <p className="text-muted-foreground">
            管理eSIM库存、激活状态和分配
          </p>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <p className="text-center text-muted-foreground">
            eSIM管理功能正在开发中...
          </p>
        </div>
      </div>
    </PageGuard>
  )
}
