'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslations } from 'next-intl'
import { Eye, EyeOff, Loader2, User } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { ModeToggle } from '@/components/layout/mode-toggle'
import { LanguageSwitcher } from '@/components/layout/language-switcher'

import { useAuth } from '@/hooks/useAuth'
import { loginSchema, LoginFormData } from '@/lib/validations/auth'

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const { login, isLoading, error, setError } = useAuth()
  const router = useRouter()
  const t = useTranslations('auth')

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  // 错误类型到国际化 key 的映射
  const getErrorMessage = (errorType: string | null): string | null => {
    if (!errorType) return null
    
    const errorMap: Record<string, string> = {
      'INVALID_CREDENTIALS': t('invalidCredentials'),
      'ACCOUNT_DISABLED': t('accountDisabled'),
      'TOO_MANY_ATTEMPTS': t('tooManyAttempts'),
      'SERVER_ERROR': t('serverError'),
      'NETWORK_ERROR': t('networkError'),
      'UNKNOWN_ERROR': t('unknownError'),
    }
    
    return errorMap[errorType] || t('unknownError')
  }

  const onSubmit = async (data: LoginFormData) => {
    try {
      await login(data)
      router.push('/dashboard')
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  // 临时测试功能：模拟不同的错误类型
  const simulateError = (errorType: string) => {
    setError(errorType)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950 py-12 px-4 sm:px-6 lg:px-8 relative">
      {/* 右上角的主题切换和语言切换 */}
      <div className="absolute top-4 right-4 flex items-center gap-2 z-10">
        <LanguageSwitcher />
        <ModeToggle />
      </div>
      
      <div className="max-w-md w-full space-y-8">
        {/* 简单的用户图标 */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <User className="h-6 w-6 text-gray-600 dark:text-gray-400" />
          </div>
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            {t('loginTitle')}
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {t('loginSubtitle')}
          </p>
        </div>

        {/* 测试错误按钮组 - 仅在开发环境显示 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <p className="text-xs text-yellow-800 dark:text-yellow-200 mb-2">DEV-MODE: test login error</p>
            <div className="grid grid-cols-2 gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => simulateError('INVALID_CREDENTIALS')}
                className="text-xs"
              >
                Invalid credentials
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => simulateError('ACCOUNT_DISABLED')}
                className="text-xs"
              >
                Account disabled
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => simulateError('TOO_MANY_ATTEMPTS')}
                className="text-xs"
              >
                Too many attempts
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => simulateError('SERVER_ERROR')}
                className="text-xs"
              >
                Server error
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => simulateError('NETWORK_ERROR')}
                className="text-xs"
              >
                Network error
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setError(null)}
                className="text-xs"
              >
                Clear error
              </Button>
            </div>
          </div>
        )}

        <Card className="border-0 shadow-sm">
          <CardContent className="pt-6">
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertDescription>{getErrorMessage(error)}</AlertDescription>
              </Alert>
            )}

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t('email')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          className="h-11"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t('password')}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showPassword ? 'text' : 'password'}
                            placeholder="••••••••"
                            className="h-11 pr-10"
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-400" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button 
                  type="submit" 
                  className="w-full h-11 bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100" 
                  disabled={isLoading}
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t('login')}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            v1.0.0
          </p>
        </div>
      </div>
    </div>
  )
} 