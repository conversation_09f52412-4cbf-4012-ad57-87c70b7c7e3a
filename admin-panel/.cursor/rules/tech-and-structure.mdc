---
description: 
globs: 
alwaysApply: false
---
# 技术栈与目录结构

- 框架：Next.js 15（App Router）
- UI：shadcn/ui + Tailwind CSS
- 状态管理：Zustand
- 数据获取：TanStack Query
- 表单校验：React Hook Form + Zod
- 图表：Recharts
- Runtime：Bun
- 类型：TypeScript
- 代码规范：ESLint、Prettier、TS strict


推荐目录结构：
admin-panel/
├── src/
│   ├── app/                 # App Router 页面
│   ├── components/          # 组件库
│   │   ├── ui/             # shadcn/ui 基础组件
│   │   ├── layout/         # 布局组件
│   │   ├── business/       # 业务组件
│   │   └── charts/         # 图表组件
│   ├── lib/                # 工具函数
│   ├── hooks/              # 自定义 Hooks
│   ├── store/              # 状态管理
│   ├── types/              # TypeScript 类型定义
│   └── styles/             # 样式文件
├── public/                 # 静态资源
└── docs/                   # 文档

详细规范见 [admin-panel-prd.md](mdc:../docs/admin-panel-prd.md)

