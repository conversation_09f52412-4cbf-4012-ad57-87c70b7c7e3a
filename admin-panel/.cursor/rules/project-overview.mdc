---
description: 
globs: 
alwaysApply: false
---
# AuraESIM 管理后台项目总览

AuraESIM 管理后台（admin-panel）是为代理商、企业代理商和平台管理员提供的 Web 响应式 eSIM 业务管理系统。系统采用 Next.js 15（App Router）、shadcn/ui、Tailwind CSS、Zustand、TanStack Query、React Hook Form、Zod 等现代前端技术栈，注重高效、易用、安全和可维护性。

- 支持三类主要角色：Reseller（代理商）、Enterprise（企业代理商）、Admin（平台管理员），各自拥有不同的管理权限和功能模块访问能力。
- 所有页面和 API 均需基于角色进行权限校验（RBAC）。
- 采用经典后台布局：Header（顶部导航）、Sidebar（侧边栏）、Main Content（主内容区）、Footer（可选）。
- 完全支持国际化，默认支持英文，中文，日文。
- 组件、页面、API 封装需有文档和示例，推荐分阶段、可追踪的开发计划。

详细设计与规范见 [admin-panel-prd.md](mdc:../docs/admin-panel-prd.md)

