---
description:
globs:
alwaysApply: false
---
# 多语言（国际化）开发规范

1. **国际化支持**：所有界面、组件、表单、提示、校验等必须完整支持国际化。
2. **默认语言**：项目默认支持中文（zh）、英文（en）、日文（ja），三种语言资源需同步维护，任何新功能/新页面/新组件，必须同步追加三语文案。
3. **资源存放**：所有语言资源按最佳实践存放于 `admin-panel/messages/` 目录（不在 src 内），结构分模块嵌套，便于扩展和维护。
4. **实现方式**：所有组件、页面、业务逻辑均应通过 [next-intl](mdc:https://next-intl-docs.vercel.app) 实现国际化，严禁硬编码文案。
5. **语言切换**：主界面需提供明显的语言切换器，推荐使用 [src/components/layout/language-switcher.tsx](mdc:admin-panel/src/components/layout/language-switcher.tsx)。
6. **路由规则**：所有页面必须放在 `src/app/[locale]/` 目录下，避免在 `src/app/` 根目录创建页面，确保国际化路由正常工作。
7. **扩展要求**：如需新增语言，需：
   - 新增 `messages/xx.json` 资源文件
   - 在 `src/i18n/config.ts` 中追加语言代码、名称、国旗
   - 在 `src/i18n/routing.ts` 的 locales 数组中追加
   - 在 pathnames 对象中添加新语言的路径映射
8. **错误处理**：所有认证错误信息必须使用国际化文案，禁止硬编码英文错误信息。
9. **详细用法**：开发、维护、扩展国际化的详细方法见 [i18n-guide.md](mdc:admin-panel/docs/i18n-guide.md)
10. **参考文件**：
   - [src/i18n/config.ts](mdc:admin-panel/src/i18n/config.ts)
   - [src/i18n/routing.ts](mdc:admin-panel/src/i18n/routing.ts)
   - [src/i18n/request.ts](mdc:admin-panel/src/i18n/request.ts)
   - [src/lib/i18n-utils.ts](mdc:admin-panel/src/lib/i18n-utils.ts)
   - [src/lib/detect-locale.ts](mdc:admin-panel/src/lib/detect-locale.ts)
   - [messages/zh.json](mdc:admin-panel/messages/zh.json)
   - [messages/en.json](mdc:admin-panel/messages/en.json)
   - [messages/ja.json](mdc:admin-panel/messages/ja.json)
