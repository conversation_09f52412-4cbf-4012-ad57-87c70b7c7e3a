---
description: 
globs: 
alwaysApply: false
---
# 组件开发规范

- 组件命名采用 PascalCase，文件名与组件名一致。
- 基础组件（Button, Card, Table, ...）优先复用 shadcn/ui（必要时可遍历查询 src/components/ui 目录）。
- 业务组件（StatCard, DataTable, FilterBar, StatusBadge, ...）按 PRD 规范实现。
- 所有表单使用 React Hook Form + Zod 校验。
- 组件需具备响应式和无障碍支持。
- 组件需要支持国际化。
- 支持明暗主题切换。
- 重要操作需有反馈（成功、警告、错误）。

详细组件接口与样式规范见 [admin-panel-prd.md](mdc:../docs/admin-panel-prd.md)

