---
description: 
globs: 
alwaysApply: false
---
# 实施计划与进度管理规则

[implementation-plan.md](mdc:../docs/implementation-plan.md) 是 AuraESIM 管理后台开发的阶段性实施计划与进度跟踪文件。

- 该文件以阶段（阶段一至阶段八）为单位，采用 checklist 形式，覆盖从项目初始化到测试部署的全流程。
- 每个阶段包含目标、详细任务清单、阶段总结与确认机制。
- 每完成一个阶段，需在 checklist 中勾选对应任务，并在阶段总结处补充实际完成情况。
- 阶段推进需经产品/负责人确认"OK"后，方可进入下一阶段。
- 该文件应持续更新，反映最新进展、变更和阶段性总结。
- 进度管理文件是团队协作、验收和回溯的重要依据。

详细内容见 [implementation-plan.md](mdc:../docs/implementation-plan.md)

