# Phase 3 实施指南：全局布局与导航

## 概述

Phase 3 完成了 AuraESIM 管理后台的全局布局与导航系统，为后续的业务模块开发奠定了坚实的基础。

## 实施内容

### 1. DashboardLayout 布局组件

**文件位置**: `src/components/layout/DashboardLayout.tsx`

**功能特性**:
- 集成 SidebarProvider 提供侧边栏状态管理
- 使用 SidebarInset 作为主内容区域
- 包含 DashboardHeader 顶部导航
- 响应式设计，支持移动端和桌面端

**使用方式**:
```tsx
import { DashboardLayout } from '@/components/layout/DashboardLayout'

export default function Layout({ children }) {
  return (
    <DashboardLayout>
      {children}
    </DashboardLayout>
  )
}
```

### 2. AppSidebar 侧边栏组件

**文件位置**: `src/components/layout/AppSidebar.tsx`

**功能特性**:
- 基于用户角色的动态菜单显示
- 支持折叠/展开状态
- 导航分组（主要功能、系统管理）
- 用户信息和操作菜单
- 完整的国际化支持

**导航结构**:
- **主要功能**: 仪表板、用户管理、eSIM管理、订单管理、促销管理、财务管理
- **系统管理**: 费率管理、代理商管理、企业管理、数据分析、系统设置（仅管理员可见）

**权限控制**:
- 普通用户: 访问主要功能模块
- 代理商: 访问主要功能模块
- 企业用户: 访问主要功能模块
- 管理员: 访问所有模块包括系统管理

### 3. DashboardHeader 顶部导航

**文件位置**: `src/components/layout/DashboardHeader.tsx`

**功能特性**:
- 侧边栏切换按钮
- 面包屑导航
- 全局搜索功能
- 通知中心（带未读计数）
- 设置菜单
- 主题切换
- 语言切换

**组件结构**:
- SidebarTrigger: 侧边栏控制
- Breadcrumb: 路径导航
- Search: 全局搜索
- Notifications: 通知下拉菜单
- Settings: 设置下拉菜单
- ModeToggle: 主题切换
- LanguageSwitcher: 语言切换

### 4. 路由结构

**基础路由**: `/[locale]/dashboard/`

**业务模块路由**:
- `/dashboard` - 仪表板首页
- `/dashboard/users` - 用户管理
- `/dashboard/esims` - eSIM管理
- `/dashboard/orders` - 订单管理
- `/dashboard/promotions` - 促销管理
- `/dashboard/finance` - 财务管理

**系统管理路由** (仅管理员):
- `/dashboard/system/rates` - 费率管理
- `/dashboard/system/resellers` - 代理商管理
- `/dashboard/system/enterprises` - 企业管理
- `/dashboard/system/analytics` - 数据分析
- `/dashboard/system/settings` - 系统设置

**个人功能路由**:
- `/dashboard/profile` - 个人资料
- `/dashboard/notifications` - 通知中心

### 5. 权限控制增强

**ProtectedRoute 组件更新**:
- 支持 `allowedRoles` 数组参数
- 支持 `requiredRole` 单个角色参数
- 自动处理角色权限验证
- 无权限时自动重定向

**使用示例**:
```tsx
// 仅管理员可访问
<ProtectedRoute requiredRole="admin">
  {children}
</ProtectedRoute>

// 多角色可访问
<ProtectedRoute allowedRoles={['admin', 'reseller']}>
  {children}
</ProtectedRoute>
```

### 6. 国际化支持

**新增翻译键**:
- `navigation.main` - 主要功能
- `navigation.system` - 系统管理
- `navigation.esims` - eSIM管理
- `navigation.finance` - 财务管理
- `navigation.notifications` - 通知中心
- `navigation.logout` - 退出登录
- `header.*` - 头部相关翻译

**支持语言**:
- 中文 (zh)
- 英文 (en)
- 日文 (ja)

## 技术特性

### 响应式设计
- 桌面端: 固定侧边栏布局
- 移动端: 抽屉式侧边栏
- 自适应内容区域

### 状态管理
- 侧边栏展开/折叠状态持久化
- 用户认证状态全局管理
- 主题和语言偏好设置

### 性能优化
- 组件懒加载
- 路由级代码分割
- 图标按需加载

### 可访问性
- 键盘导航支持
- 屏幕阅读器友好
- 高对比度主题支持

## 开发指南

### 添加新的导航菜单项

1. 在 `AppSidebar.tsx` 中添加菜单配置:
```tsx
const navigationItems = [
  // 现有项目...
  {
    title: 'newFeature',
    icon: NewIcon,
    href: '/dashboard/new-feature',
    roles: ['admin', 'reseller']
  }
]
```

2. 添加对应的翻译:
```json
{
  "navigation": {
    "newFeature": "新功能"
  }
}
```

3. 创建对应的页面文件:
```tsx
// src/app/[locale]/dashboard/new-feature/page.tsx
export default function NewFeaturePage() {
  return <div>新功能页面</div>
}
```

### 自定义面包屑导航

在页面组件中使用 `useBreadcrumb` hook (待实现):
```tsx
const { setBreadcrumb } = useBreadcrumb()

useEffect(() => {
  setBreadcrumb([
    { label: '仪表板', href: '/dashboard' },
    { label: '用户管理', href: '/dashboard/users' },
    { label: '用户详情' }
  ])
}, [])
```

## 下一步计划

Phase 3 完成后，接下来将进入 Phase 4：核心模块骨架（静态页面），将为每个业务模块创建完整的静态页面和基础交互功能。

## 注意事项

1. 所有新增的路由页面目前都是占位页面，显示"功能正在开发中"
2. 权限控制已实现，但需要在后续阶段完善具体的权限验证逻辑
3. 通知功能使用模拟数据，需要在后续阶段集成真实的通知系统
4. 搜索功能界面已完成，但搜索逻辑需要在后续阶段实现
