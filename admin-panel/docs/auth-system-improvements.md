# 认证系统改进方案

## 概述

针对您提出的两个问题，我设计了一套完整的解决方案：

1. **通用HTTP错误处理机制**：统一处理API错误，避免重复的catch和判断
2. **页面权限配置系统**：简单灵活的权限配置机制，支持多角色管理

## 1. 通用HTTP错误处理机制

### 核心组件

**文件**: `src/lib/error-handler.ts`

### 特性

1. **统一错误分类**：
   - `NETWORK` - 网络错误
   - `AUTHENTICATION` - 认证错误（401）
   - `AUTHORIZATION` - 授权错误（403）
   - `VALIDATION` - 验证错误（422）
   - `NOT_FOUND` - 资源不存在（404）
   - `SERVER` - 服务器错误（5xx）
   - `UNKNOWN` - 未知错误

2. **自动错误处理**：
   - 自动判断是否需要登出
   - 自动判断是否需要重定向
   - 自动显示用户友好的错误消息

3. **业务层自定义**：
   - 支持自定义错误消息
   - 支持自定义错误处理逻辑
   - 提供错误处理装饰器

### 使用方式

#### 基础使用（自动处理）
```typescript
import { handleError } from '@/lib/error-handler'

try {
  const result = await api.get('/some-endpoint')
  return result
} catch (error) {
  // 自动处理错误并显示toast
  handleError(error)
  throw error // 可选：重新抛出错误
}
```

#### 业务层自定义处理
```typescript
import { errorHandler } from '@/lib/error-handler'

try {
  const result = await api.get('/some-endpoint')
  return result
} catch (error) {
  const errorResult = errorHandler.handleApiError(error)
  
  // 根据错误类型进行自定义处理
  if (errorResult.type === 'VALIDATION') {
    // 自定义验证错误处理
    setFormErrors(parseValidationErrors(error))
  } else {
    // 使用默认处理
    errorHandler.showError(errorResult, '自定义错误消息')
  }
  
  if (errorResult.shouldLogout) {
    // 执行登出逻辑
    logout()
  }
}
```

#### 使用装饰器（推荐）
```typescript
import { withErrorHandling } from '@/lib/error-handler'

const fetchUserData = withErrorHandling(async (userId: string) => {
  return await api.get(`/users/${userId}`)
})

// 使用时无需手动处理错误
const userData = await fetchUserData('123')
```

### 在Auth Store中的应用

已更新 `src/store/auth.ts`：
- `login` 方法使用通用错误处理
- `initializeAuth` 方法使用通用错误处理
- 自动处理401错误的登出逻辑

## 2. 页面权限配置系统

### 核心组件

1. **权限配置**: `src/lib/auth-config.ts`
2. **权限Hook**: `src/hooks/usePageAuth.ts`
3. **页面守卫**: `src/components/layout/PageGuard.tsx`

### 设计原则

1. **配置驱动**：通过配置文件统一管理页面权限
2. **简单易用**：一行代码即可为页面添加权限控制
3. **灵活扩展**：支持自定义权限检查逻辑
4. **类型安全**：完整的TypeScript类型支持

### 权限配置结构

```typescript
interface PageAuthConfig {
  requireAuth: boolean        // 是否需要登录
  allowedRoles?: UserRole[]   // 允许的角色列表
  redirectTo?: string         // 无权限时的重定向路径
  showLoading?: boolean       // 是否显示加载状态
  customCheck?: (user: any) => boolean  // 自定义权限检查
}
```

### 页面权限配置示例

```typescript
export const PAGE_AUTH_CONFIG: Record<string, PageAuthConfig> = {
  // 公开页面
  '/login': {
    requireAuth: false,
    showLoading: false
  },
  
  // 需要登录但无角色限制
  '/dashboard': {
    requireAuth: true,
    allowedRoles: [], // 空数组表示所有已登录用户
    showLoading: true
  },
  
  // 需要特定角色
  '/users': {
    requireAuth: true,
    allowedRoles: ['admin', 'reseller', 'enterprise'],
    redirectTo: '/dashboard',
    showLoading: true
  },
  
  // 仅管理员
  '/system/settings': {
    requireAuth: true,
    allowedRoles: ['admin'],
    redirectTo: '/dashboard',
    showLoading: true
  }
}
```

### 使用方式

#### 方式1：使用PageGuard组件（推荐）

```typescript
// 在页面组件中使用
import PageGuard from '@/components/layout/PageGuard'

export default function UsersPage() {
  return (
    <PageGuard>
      <div>
        <h1>用户管理</h1>
        {/* 页面内容 */}
      </div>
    </PageGuard>
  )
}
```

#### 方式2：使用usePageAuth Hook

```typescript
import { usePageAuth } from '@/hooks/usePageAuth'

export default function UsersPage() {
  const { hasPermission, isLoading } = usePageAuth()
  
  if (isLoading) {
    return <div>Loading...</div>
  }
  
  if (!hasPermission) {
    return null // 重定向已自动处理
  }
  
  return (
    <div>
      <h1>用户管理</h1>
      {/* 页面内容 */}
    </div>
  )
}
```

#### 方式3：在Layout中统一处理

```typescript
// 在layout.tsx中使用
import PageGuard from '@/components/layout/PageGuard'

export default function Layout({ children }) {
  return (
    <PageGuard>
      <DashboardLayout>
        {children}
      </DashboardLayout>
    </PageGuard>
  )
}
```

### 高级功能

#### 自定义权限检查

```typescript
'/admin/sensitive': {
  requireAuth: true,
  allowedRoles: ['admin'],
  customCheck: (user) => {
    // 额外的权限检查逻辑
    return user.permissions?.includes('SENSITIVE_DATA_ACCESS')
  },
  redirectTo: '/dashboard'
}
```

#### 动态路由支持

系统自动支持动态路由，会向上查找父路径的配置：
- `/users/123` 会使用 `/users` 的配置
- `/system/settings/advanced` 会使用 `/system/settings` 的配置

## 最佳实践对比

### 当前方案 vs 传统方案

| 特性 | 传统方案 | 新方案 |
|------|----------|--------|
| 错误处理 | 每个API调用都需要手动catch | 统一处理，业务层可选择性自定义 |
| 权限配置 | 每个页面单独配置ProtectedRoute | 集中配置，自动应用 |
| 代码复用 | 重复的权限检查代码 | 高度复用，配置驱动 |
| 维护性 | 分散在各个组件中 | 集中管理，易于维护 |
| 类型安全 | 部分类型安全 | 完整的TypeScript支持 |
| 扩展性 | 需要修改多个文件 | 只需修改配置文件 |

### 符合最佳实践的特点

1. **关注点分离**：权限逻辑与业务逻辑分离
2. **配置驱动**：通过配置而非代码控制行为
3. **单一职责**：每个组件只负责自己的职责
4. **开闭原则**：对扩展开放，对修改封闭
5. **DRY原则**：避免重复代码
6. **类型安全**：完整的TypeScript支持

## 迁移指南

### 1. 替换现有的ProtectedRoute

**旧方式**：
```typescript
<ProtectedRoute allowedRoles={['admin', 'reseller']}>
  <UsersPage />
</ProtectedRoute>
```

**新方式**：
```typescript
// 在auth-config.ts中配置
'/users': {
  requireAuth: true,
  allowedRoles: ['admin', 'reseller'],
  redirectTo: '/dashboard'
}

// 在组件中使用
<PageGuard>
  <UsersPage />
</PageGuard>
```

### 2. 更新错误处理

**旧方式**：
```typescript
try {
  const result = await api.get('/users')
  return result
} catch (error) {
  if (error instanceof ApiError) {
    if (error.status === 401) {
      logout()
      router.push('/login')
    } else if (error.status === 403) {
      toast.error('权限不足')
    }
    // ... 更多错误处理
  }
}
```

**新方式**：
```typescript
try {
  const result = await api.get('/users')
  return result
} catch (error) {
  handleError(error) // 一行代码搞定
}
```

## 总结

新的认证系统具有以下优势：

1. **简化开发**：减少重复代码，提高开发效率
2. **统一管理**：集中配置权限和错误处理
3. **易于维护**：修改权限只需更新配置文件
4. **类型安全**：完整的TypeScript支持
5. **扩展性强**：支持自定义权限检查和错误处理
6. **用户体验**：统一的错误提示和加载状态

这套方案完全符合现代前端开发的最佳实践，为后续更多页面的权限配置提供了简单、灵活、可维护的解决方案。
