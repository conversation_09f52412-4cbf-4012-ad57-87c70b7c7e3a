# AuraESIM 管理后台开发指南

## 项目概述

AuraESIM 管理后台是一个基于 Next.js 15 的现代化 Web 应用，采用 App Router、TypeScript、Tailwind CSS 和 shadcn/ui 构建。支持多语言、多主题，并实现了完整的认证和权限管理系统。

## 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件库**: shadcn/ui
- **状态管理**: Zustand
- **数据获取**: TanStack Query
- **表单处理**: React Hook Form + Zod
- **国际化**: next-intl
- **包管理器**: Bun
- **开发工具**: ESLint, Prettier

## 项目结构

```
admin-panel/
├── src/
│   ├── app/[locale]/              # 路由页面
│   │   ├── dashboard/             # 仪表板相关页面
│   │   ├── login/                 # 登录页面
│   │   └── layout.tsx             # 全局布局
│   ├── components/                # 组件
│   │   ├── business/              # 业务组件
│   │   ├── common/                # 通用组件
│   │   ├── layout/                # 布局组件
│   │   └── ui/                    # UI 基础组件
│   ├── hooks/                     # 自定义 Hooks
│   ├── lib/                       # 工具库
│   ├── store/                     # 状态管理
│   └── types/                     # 类型定义
├── messages/                      # 国际化文件
├── docs/                          # 文档
└── public/                        # 静态资源
```

## 开发环境设置

### 1. 安装依赖

```bash
bun install
```

### 2. 环境变量配置

复制 `.env.example` 到 `.env.local` 并配置相应的环境变量：

```bash
cp .env.example .env.local
```

### 3. 启动开发服务器

```bash
bun run dev
```

应用将在 http://localhost:3000 启动（如果端口被占用会自动选择其他端口）。

## 核心功能

### 认证系统

- **登录**: 支持邮箱/密码登录
- **JWT**: 基于 JWT 的身份验证
- **权限控制**: 基于角色的访问控制 (RBAC)
- **会话管理**: 自动刷新和过期处理

### 布局系统

- **DashboardLayout**: 主要的仪表板布局
- **AppSidebar**: 可折叠的侧边栏导航
- **DashboardHeader**: 顶部导航栏
- **响应式设计**: 支持移动端和桌面端

### 国际化

- **支持语言**: 中文、英文、日文
- **动态切换**: 运行时语言切换
- **路由国际化**: URL 包含语言代码

### 主题系统

- **多主题**: 支持亮色、暗色、系统主题
- **动态切换**: 实时主题切换
- **持久化**: 主题偏好本地存储

## 开发规范

### 代码风格

- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 规则
- 组件使用 PascalCase 命名
- 文件使用 kebab-case 命名

### 组件开发

1. **UI 组件**: 放在 `components/ui/` 目录
2. **业务组件**: 放在 `components/business/` 目录
3. **布局组件**: 放在 `components/layout/` 目录
4. **通用组件**: 放在 `components/common/` 目录

### 页面开发

1. 在 `app/[locale]/` 下创建对应的路由文件
2. 使用 `ProtectedRoute` 包装需要认证的页面
3. 添加相应的国际化翻译
4. 实现响应式设计

### 状态管理

- **全局状态**: 使用 Zustand
- **服务器状态**: 使用 TanStack Query
- **表单状态**: 使用 React Hook Form

## API 集成

### 配置

API 配置在 `src/lib/api.ts` 中：

```typescript
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
})
```

### 使用示例

```typescript
// 创建 API Hook
export function useUsers() {
  return useQuery({
    queryKey: ['users'],
    queryFn: () => api.get('/users').then(res => res.data)
  })
}

// 在组件中使用
function UsersPage() {
  const { data: users, isLoading, error } = useUsers()
  
  if (isLoading) return <Loading />
  if (error) return <Error />
  
  return <UserList users={users} />
}
```

## 部署

### 构建

```bash
bun run build
```

### 启动生产服务器

```bash
bun run start
```

### Docker 部署

```bash
docker build -t admin-panel .
docker run -p 3000:3000 admin-panel
```

## 常见问题

### 1. 端口冲突

如果 3000 端口被占用，Next.js 会自动选择其他可用端口。

### 2. 环境变量

确保所有必需的环境变量都已正确配置在 `.env.local` 文件中。

### 3. 类型错误

运行 `bun run type-check` 检查 TypeScript 类型错误。

### 4. 样式问题

确保 Tailwind CSS 配置正确，运行 `bun run build-css` 重新构建样式。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
