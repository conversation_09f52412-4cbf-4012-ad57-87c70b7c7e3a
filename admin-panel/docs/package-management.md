# 数据套餐管理模块

## 概述

数据套餐管理模块是与eSIM管理同等重要的核心功能模块，提供完整的数据套餐管理功能，包括套餐列表、提供商管理、筛选搜索、详情查看等功能。

## 功能特性

### 1. 套餐管理
- **套餐列表**: 展示所有可用的数据套餐
- **套餐详情**: 查看套餐的详细信息
- **套餐筛选**: 支持多维度筛选套餐
- **套餐搜索**: 快速搜索特定套餐

### 2. 提供商管理
- **提供商列表**: 管理所有数据套餐提供商
- **提供商状态**: 监控提供商的活跃状态
- **数据同步**: 同步提供商的套餐数据

### 3. 数据展示
- **网格视图**: 卡片式展示套餐信息
- **表格视图**: 表格形式展示详细数据
- **统计信息**: 套餐总数、可用性等统计

### 4. 国际化支持
- **多语言**: 支持中文、英文、日文
- **本地化**: 价格、日期等本地化显示

## 页面结构

```
/packages                    # 套餐管理主页
├── /providers              # 提供商管理页面
├── /[provider]/[id]         # 套餐详情页面
└── /analytics              # 套餐分析页面（待开发）
```

## 组件架构

### 核心组件
- `PackageList`: 套餐列表组件
- `PackageFilters`: 套餐筛选组件
- `PackageDashboard`: 套餐仪表板组件
- `ProviderList`: 提供商列表组件

### 服务层
- `PackageService`: 套餐相关API服务
- `ProviderService`: 提供商相关API服务

### 类型定义
- `Package`: 套餐数据类型
- `Provider`: 提供商数据类型
- `PackageFilters`: 筛选条件类型

## API 接口

### 提供商接口
```typescript
GET /api/v1/providers                           # 获取提供商列表
GET /api/v1/providers/{provider}/packages       # 获取指定提供商的套餐
POST /api/v1/admin/providers/{provider}/sync    # 同步提供商数据
```

### 套餐接口
```typescript
GET /api/v1/providers/{provider}/packages/{id}  # 获取套餐详情
POST /api/v1/admin/packages/sync               # 同步所有套餐数据
```

## 数据模型

### Package (套餐)
```typescript
interface Package {
  id: string                    # 套餐ID
  name: string                  # 套餐名称
  description: string           # 套餐描述
  price: number                 # 价格(分)
  currency: string              # 货币
  dataVolume: number            # 数据量(字节)
  validityDays: number          # 有效期(天)
  locationCodes: string[]       # 支持地区代码
  supportsSMS: boolean          # 是否支持短信
  dataType: string              # 数据类型(LIMITED/UNLIMITED)
  networkTypes: string[]        # 网络类型(4G/5G/LTE)
  supportTopUp: boolean         # 是否支持充值
  provider?: string             # 提供商
  status?: PackageStatus        # 状态
}
```

### Provider (提供商)
```typescript
interface Provider {
  id: string                    # 提供商ID
  name: string                  # 提供商名称
  type: string                  # 提供商类型
  status: ProviderStatus        # 状态
  packageCount?: number         # 套餐数量
  lastSyncAt?: string          # 最后同步时间
}
```

## 筛选功能

### 支持的筛选条件
- **提供商**: 按提供商筛选
- **地区**: 按覆盖地区筛选
- **数据类型**: 有限/无限流量
- **网络类型**: 4G/5G/LTE
- **价格范围**: 自定义价格区间
- **数据量范围**: 自定义流量区间
- **功能支持**: SMS支持、充值支持

### 搜索功能
- **关键词搜索**: 套餐名称、描述搜索
- **地区代码搜索**: 支持地区代码快速搜索
- **提供商搜索**: 按提供商名称搜索

## 菜单位置

数据套餐管理模块在导航菜单中的位置：

```
仪表板
用户管理
📦 数据套餐管理  ← 新增模块，位于eSIM管理之前
📱 eSIM管理
订单管理
促销管理
财务管理
```

## 权限控制

### 角色权限
- **ADMIN**: 完整的套餐管理权限
- **RESELLER**: 查看和搜索套餐
- **ENTERPRISE**: 查看和搜索套餐

### 功能权限
- 查看套餐列表: 所有角色
- 查看套餐详情: 所有角色
- 同步套餐数据: 仅ADMIN
- 管理提供商: 仅ADMIN

## 开发说明

### 环境要求
- Node.js 18+
- Next.js 15+
- TypeScript 5+
- Tailwind CSS 3+

### 依赖包
- `@radix-ui/react-*`: UI组件库
- `next-intl`: 国际化支持
- `lucide-react`: 图标库

### 开发命令
```bash
# 启动开发服务器
bun run dev

# 构建生产版本
bun run build

# 运行测试
bun run test
```

## 未来规划

### 待开发功能
1. **套餐分析**: 套餐使用情况分析
2. **价格趋势**: 套餐价格变化趋势
3. **地区覆盖**: 地区覆盖情况可视化
4. **批量操作**: 批量管理套餐
5. **自动同步**: 定时自动同步套餐数据

### 优化方向
1. **性能优化**: 大数据量下的列表性能
2. **缓存策略**: 套餐数据缓存机制
3. **实时更新**: WebSocket实时数据更新
4. **移动端适配**: 响应式设计优化

## 技术架构

### 前端架构
```
Pages (页面层)
├── Components (组件层)
├── Services (服务层)
├── Types (类型层)
└── Utils (工具层)
```

### 状态管理
- 使用React Hooks进行本地状态管理
- 通过Context进行全局状态共享
- 服务层处理API调用和数据缓存

### 样式系统
- Tailwind CSS作为基础样式框架
- shadcn/ui组件库提供一致的UI体验
- 响应式设计支持多设备访问
