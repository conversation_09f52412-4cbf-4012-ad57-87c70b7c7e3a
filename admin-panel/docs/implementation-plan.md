# 管理后台 Admin Panel 实施计划

---

## 阶段一：项目初始化与环境搭建

**目标**：完成 Next.js、Tailwind CSS、shadcn/ui、Zustand、TanStack Query 等基础配置

**任务清单**：

- [x] 初始化 Next.js 15 项目（App Router, 使用最新版本，下同）
- [x] 安装并配置 Tailwind CSS
- [x] 集成 shadcn/ui 组件库
- [x] 安装并配置 Zustand、TanStack Query、React Hook Form、Zod
- [x] 配置环境变量示例文件 `.env.example`
- [x] 配置 ESLint、Prettier、TypeScript 严格模式

**阶段总结 & 确认**：

- 已完成所有依赖安装与配置，shadcn/ui 初始化，TypeScript 严格模式，页面可正常访问。
- 阶段一全部完成。
- 请确认后回复 **"OK"**

---

## 阶段二：认证与权限管理

**目标**：实现登录流程及基于角色的页面路由保护

**任务清单**：

- [x] 实现登录页面 UI（表单、校验）
- [x] 集成 JWT 认证或 NextAuth
- [x] 实现身份信息存储（Token 存储、全局状态）
- [x] 实现页面路由拦截与角色权限验证（RBAC）
- [x] 创建公共鉴权 HOC/中间件

**阶段总结 & 确认**：

- 已完成完整的认证与权限管理系统：
  - ✅ 登录页面（/login）：包含表单校验、错误处理、Loading状态
  - ✅ JWT认证：集成后端API登录接口，支持Token存储和管理
  - ✅ 全局状态管理：基于Zustand的认证store，支持持久化
  - ✅ 路由保护：ProtectedRoute组件和权限Hook，支持角色级访问控制
  - ✅ 认证上下文：AuthProvider全局认证状态初始化
  - ✅ 测试页面：仪表板页面用于验证登录跳转和权限控制
- 阶段二全部完成，可进入阶段三。
- 请确认后回复 **"OK"**

---

## 阶段三：全局布局与导航

**目标**：完成后台主框架布局

**任务清单**：

- [x] 实现 `DashboardLayout` 布局组件
- [x] 实现侧边栏 `Sidebar`（折叠/展开、导航分组）
- [x] 实现顶部导航 `Header`（全局搜索、通知、用户菜单）
- [x] 配置 Next.js App Router 全局布局和默认布局
- [x] 设置基础路由文件结构（`app/dashboard/...`）

**阶段总结 & 确认**：

- ✅ 已完成完整的全局布局与导航系统：
  - ✅ DashboardLayout：完整的仪表板布局组件，支持侧边栏和主内容区域
  - ✅ AppSidebar：功能完整的侧边栏，支持折叠/展开、导航分组、角色权限控制
  - ✅ DashboardHeader：顶部导航栏，包含全局搜索、通知中心、用户菜单
  - ✅ 路由结构：完整的dashboard路由结构，支持各业务模块
  - ✅ 权限控制：基于角色的导航菜单显示和页面访问控制
  - ✅ 响应式设计：支持移动端和桌面端的自适应布局
- 阶段三全部完成，可进入阶段四。
- 请确认后回复 **"OK"**

---

## 阶段四：核心模块骨架（静态页面）

**目标**：完成主要模块的静态页面与路由

**任务清单**：

- [ ] Dashboard 页面静态展示
- [ ] 用户管理列表及详情页静态展示
- [ ] eSIM 管理列表及详情页静态展示
- [ ] 订单管理列表及详情页静态展示
- [ ] 促销管理列表及详情页静态展示
- [ ] 财务管理页面静态展示
- [ ] 系统管理页面（费率、代理商、企业）静态展示
- [ ] 个人中心页面静态展示

**阶段总结 & 确认**：

- 所有核心模块页面已创建并通过路由可访问，使用静态 Mock 数据
- 请确认后回复 **"OK"**

---

## 阶段五：核心模块数据集成（基础功能）

**目标**：将静态页面与后端 API 对接，完成基础数据展示与交互

**任务清单**：

- [ ] 封装用户 API Hook（`useUsers`）
- [ ] 封装 eSIM API Hook（`useEsims`）
- [ ] 封装订单 API Hook（`useOrders`）
- [ ] 封装促销 API Hook（`usePromotions`）
- [ ] 封装财务 API Hook（`useCredit`, `useTransactions`）
- [ ] 封装系统管理 API Hook（`useRates`, `useResellers`, `useEnterprises`）
- [ ] 在各页面中使用 React Query 获取并展示真实数据
- [ ] 处理 Loading 与 Error 状态

**阶段总结 & 确认**：

- 核心模块可正常调用后端 API 并展示数据，基本交互可用
- 请确认后回复 **"OK"**

---

## 阶段六：公共组件与工具完善

**目标**：完善业务组件库，提高复用率与一致性

**任务清单**：

- [ ] 完善 `DataTable` 组件（搜索、排序、分页）
- [ ] 完善 `FilterBar` 与 `StatusBadge` 组件
- [ ] 完善 `StatCard`、图表等可视化组件
- [ ] 提取通用表单组件（`Form`, `Input`, `Select`）
- [ ] 编写组件文档与使用示例

**阶段总结 & 确认**：

- 通用组件已完善并在各模块中复用，提高开发效率
- 请确认后回复 **"OK"**

---

## 阶段七：高级功能与细节完善

**目标**：实现搜索过滤、批量操作、导出、操作日志、实时更新等高级功能

**任务清单**：

- [ ] 实现全局搜索与模块内筛选
- [ ] 实现批量操作（批量分配、批量删除等）
- [ ] 实现数据导出功能（CSV、Excel）
- [ ] 集成 Recharts，完善 Dashboard 数据可视化
- [ ] 实现通知中心和操作日志页面
- [ ] 集成 WebSocket 或 webhook 实时更新 eSIM 与订单状态

**阶段总结 & 确认**：

- 高级功能已上线，用户体验得到明显提升
- 请确认后回复 **"OK"**

---

## 阶段八：测试与部署

**目标**：保证代码质量并部署上线

**任务清单**：

- [ ] 编写单元测试（Jest + Testing Library）
- [ ] 编写集成测试与端到端测试（Playwright）
- [ ] 配置 CI/CD（GitHub Actions）
- [ ] 编写 Dockerfile 与部署脚本
- [ ] 部署到测试环境并验证
- [ ] 编写上线文档与部署指南

**阶段总结 & 确认**：

- 测试覆盖率满足要求，部署流程稳定可复现
- 请确认后回复 **"OK"**