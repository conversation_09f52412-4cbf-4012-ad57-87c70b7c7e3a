# AuraESIM 管理后台多语言（国际化）开发指南

本项目采用 [next-intl](https://next-intl-docs.vercel.app/) + Next.js App Router 实现完整的多语言（国际化）支持，默认支持中文（zh）、英文（en）、日文（ja），并可方便扩展更多语言。

---

## 📁 目录结构

```
admin-panel/
├── messages/            # 语言资源文件（JSON）
│   ├── zh.json          # 中文
│   ├── en.json          # 英文
│   └── ja.json          # 日文
├── src/
│   ├── app/
│   │   ├── [locale]/    # 语言路由目录，所有页面均应放在此目录下
│   │   │   ├── layout.tsx  # 语言特定布局，包含NextIntlClientProvider
│   │   │   └── page.tsx    # 语言特定首页
│   │   ├── i18n/            # 国际化核心配置与工具
│   │   │   ├── config.ts    # 支持语言、默认语言、名称、国旗
│   │   │   ├── request.ts   # 服务器端 next-intl 配置
│   │   │   └── routing.ts   # 路由本地化配置
│   │   ├── lib/
│   │   │   ├── i18n-utils.ts  # 国际化工具函数
│   │   │   └── detect-locale.ts # 语言检测与保存功能
│   │   ├── types/
│   │   │   └── i18n.ts      # 国际化相关类型定义
│   │   └── components/
│   │       └── layout/
│   │           └── language-switcher.tsx  # 语言切换器组件
│   └── components/
│       └── ui/
│           └── language-switcher.tsx  # 语言切换器组件
```

---

## 🚀 快速用法

### 1. 在组件/页面中使用翻译

```tsx
import { useTranslations } from 'next-intl'
const t = useTranslations('common')

return <button>{t('submit')}</button>
```
- `t('submit')` 会自动根据当前语言返回对应文案
- 支持嵌套键：`t('dashboard.welcome')`

### 2. 页面/布局国际化

- 所有页面均应放在 `src/app/[locale]/` 目录下，自动获得 `locale` 参数
- 在布局组件中自动注入 `NextIntlClientProvider`，无需手动包裹

### 3. 语言切换

```tsx
import { LanguageSwitcher } from '@/components/layout/language-switcher'

<LanguageSwitcher />
```
- 支持国旗、语言名、下拉切换，自动跳转到对应语言路由
- 切换后保存用户选择到 localStorage

### 4. 本地化数字、日期、货币

```tsx
import { formatLocalizedNumber, formatLocalizedDate, formatLocalizedCurrency } from '@/lib/i18n-utils'

formatLocalizedNumber(1234567.89, locale) // 1,234,567.89 / 1,234,567.89 / 1,234,567.89
formatLocalizedDate(new Date(), locale)   // 2024年6月1日 / Jun 1, 2024 / 2024年6月1日
formatLocalizedCurrency(1234.56, locale, 'USD') // ￥1,234.56 / $1,234.56 / ¥1,234.56
```

---

## 🌍 语言资源维护

- 所有语言资源存放于 `messages/` 目录下（不在 src 内），按 JSON 文件分语言
- 结构建议分模块嵌套，如：`common`、`dashboard`、`user`、`order` 等
- **添加新语言**：
  1. 新增 `messages/xx.json` 文件
  2. 在 `src/i18n/config.ts` 中追加语言代码、名称、国旗
  3. 在 `src/i18n/routing.ts` 的 locales 数组中追加
  4. 在 pathnames 对象中添加新语言的路径映射
- **追加新文案**：三种语言的 JSON 文件需同步维护，保持 key 一致

---

## 🛠️ 常用开发技巧

### 1. 获取当前语言
```tsx
import { useLocale } from 'next-intl'
const locale = useLocale()
```

### 2. 路由跳转（自动带语言前缀）
```tsx
import { useRouter } from '@/i18n/routing'
const router = useRouter()
router.push('/dashboard') // 自动跳转到 /zh/dashboard /en/dashboard 等
```

### 3. 服务器端获取语言资源
```ts
import { getMessages } from 'next-intl/server'
const messages = await getMessages()
```

### 4. 检测和保存用户语言偏好
```ts
import { detectBrowserLocale, saveLocale } from '@/lib/detect-locale'

// 检测浏览器语言
const browserLocale = detectBrowserLocale()

// 保存用户语言选择
saveLocale('zh')
```

---

## ⚙️ 进阶配置

- **国际化路由**：已在 `src/i18n/routing.ts` 配置，支持路径本地化
- **自动语言检测**：通过 `localeDetection: true` 配置，支持根据 Accept-Language 头检测
- **类型安全**：`src/types/i18n.ts` 提供完整类型定义，包括:
  - `Messages`: 语言消息类型
  - `TranslationKey`: 可用的翻译键类型
  - `LocaleParams`: 国际化路由参数
  - `PageProps`/`LayoutProps`: 带国际化的页面/布局 Props

### 项目中的国际化路径配置

在 `src/i18n/routing.ts` 中:

```ts
export const routing = defineRouting({
  locales: ['zh', 'en', 'ja'],
  defaultLocale: 'en',
  localeDetection: true,
  localePrefix: 'always',
  pathnames: {
    '/': '/',
    '/dashboard': {
      zh: '/dashboard',
      en: '/dashboard',
      ja: '/dashboard'
    },
    // 其他路径...
  }
});
```

---

## 💡 常见问题

- **如何追加新语言？**
  1. 新建 `messages/xx.json` 资源文件
  2. 配置 `src/i18n/config.ts` 中的 locales, localeNames, localeFlags
  3. 更新 `src/i18n/routing.ts` 中的 locales 数组和 pathnames 对象
  4. 维护所有 key 的多语言内容

- **如何让自定义组件支持国际化？**
  - 组件内用 `useTranslations` 获取 t 函数
  - 所有文案均用 t('key') 获取
  - 可选通过 Props 接收 locale 参数（需要类型声明）

- **如何处理应用布局的国际化？**
  - 在 `src/app/[locale]/layout.tsx` 中引入 `NextIntlClientProvider`
  - 通过 `getMessages()` 获取语言资源并传递给 Provider
  - 使用 `generateStaticParams()` 预生成所有语言路由

- **如何验证 locale 参数?**
  ```ts
  if (!routing.locales.includes(locale)) {
    notFound();
  }
  ```

---

## 📚 参考
- [next-intl 官方文档](https://next-intl-docs.vercel.app/)
- [Next.js 国际化路由](https://nextjs.org/docs/app/building-your-application/routing/internationalization)

---

如有疑问请联系前端负责人或查阅本文件最新版本。 